@charset "UTF-8";
[data-datepicker]{
  container: main-size / inline-size;
  width: 100%;
  & > div{
    display: flex;
    justify-content: space-between;
    max-width: 620px;
    width: 100%;
    margin-top: 1rem;
    & > div{
      & > header{
        display: flex;
        flex-flow: row wrap;
        justify-content: space-evenly;
        width: 100%;
        height: 2.5rem;
        & > span{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2.5rem;
          height: 2.5rem;
          user-select: none;
          border: solid 2px transparent;
          &:hover{
            cursor: pointer;
            border: solid 2px var(--select-color, #c99616);
          }
        }
      }
      & > ul{
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        grid-row-gap: .2rem;
        min-width: 294px;
        max-width: 320px;
        & > li{
          display: flex;
          align-items: center;
          justify-content: center;
          height: 2.5rem;
          user-select: none;
          border: solid 2px transparent;
          &.holiday{
            font-weight: bold;
            color: var(--holiday-color, inherit);
          }
          &.reserved{ background-color: var(--reserved-color, #ccc) }
          &.reservedstart{
            background: linear-gradient(
              to right,
              transparent 48%,
              var(--reserved-color, #ccc) 68%
            );
            border: 0;
          }
          &.reservedstop{
            background: linear-gradient(
              to right,
              var(--reserved-color, #ccc) 32%,
              transparent 52%
            );
            border: 0;
          }
          &[data-day]{
            &:hover{ border: solid 2px var(--select-color, #c99616) }
            &.start{
              background-color: var(--select-color, #c99616);
              border-radius: 1rem 0 0 1rem;
            }
            &.stop{
              background-color: var(--select-color, #c99616);
              border-radius: 0 1rem 1rem 0;
            }
            &.start.stop{ border-radius: 1rem } 
            &.selected{ background-color: var(--select-color, #c99616) }
          }
        }
      }
    }
  }
}
@container main-size (max-width: 600px ){
  [data-datepicker]{
    & > div{
      & > div{
        &:last-child{ display: none }
      }
    }
  }
}