<?php
$apiUrl = NavOnlineInvoice\Config::PROD_URL; // ::TEST_URL;

$userData = [ 'login' => $config['integration_settings']['navInvoice']['nav_login'],
              'password' => $config['integration_settings']['navInvoice']['nav_passwd'],
              'taxNumber' => substr( $config['tax_number'], 0, 8 ),
              'signKey' => $config['integration_settings']['navInvoice']['nav_xml_key'],
              'exchangeKey' => $config['integration_settings']['navInvoice']['nav_xml_change_key']
            ];

$softwareData = [ 'softwareId' => '123456789012345678',
                  'softwareName' => 'HeliosAi Invoice',
                  'softwareOperation' => 'ONLINE_SERVICE',
                  'softwareMainVersion' => '1.0',
                  'softwareDevName' => 'HeliosAi Invoice Dev',
                  'softwareDevContact' => 'string',
                  'softwareDevCountryCode' => 'HU',
                  'softwareDevTaxNumber' => '12345678-2-11'
                ];
