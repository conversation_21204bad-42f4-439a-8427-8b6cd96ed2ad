        <style>
          article{
            & .carts{
              &>.cart{
                & ul.formbox{
                  display: flex;
                  flex-flow: row wrap;
                  justify-content: center;
                  gap: 1rem;
                  & li{
                    &.form{
                      position: relative;
                      line-height: 100%;
                      & input{
                        height: 3rem;
                        width: 100%;
                        padding-top: 1rem;
                        border: 1px solid transparent;
                        border-radius: 8px;
                        text-indent: 0.8rem;
                        &:focus{
                          border-color: #472C4C;
                          & ~ label{
                            opacity: 0.8;
                            transform: scale(0.9) translateY(-0.65rem) translateX(0.1rem);
                          }
                        }
                        &:not(:placeholder-shown) ~ label{
                          opacity: 0.8;
                          transform: scale(0.9) translateY(-0.65rem) translateX(0.1rem);
                        }
                      }
                      &>label{
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 100%;
                        padding: 1rem 0.8rem 0;
                        pointer-events: none;
                        border: 1px solid transparent;
                        //text-transform: uppercase;
                        transform-origin: 0 0;
                        transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
                        color: #76aea2;
                      }
                    }
                    &.col0{ width: 100% }
                  }
                }
                & dialog{ margin-top: -3rem }
              }
            }
          }
        </style>
        <div class="carts" style="margin-top: 20vh">
          <div class="cart">
            <h1>Jelentkezz be vagy regisztrálj!</h1>
            <form name="form_login" method="post" action="<?= http::$path ?>/login">
              <?= http::csrfToken() ?>
              <input type="hidden" name="log" value="in">
              <ul class="formbox">
                <li class="form col0" data-tooltip="line">
                  <input
                    class="validation"
                    type="email"
                    name="email"
                    id="email"
                    value="<?= $_POST['email'] ?? '' ?>"
                    placeholder=""
                    required autofocus
                  >
                  <label for="email">Email</label>
                  <div class="input-message tooltip">
                    <div hidden class="error"></div>
                    <div class="info">Érvényes emailcímet adjon meg.</div>
                  </div>
                </li>
                <li class="form col0">
                  <input type="password" name="psw" id="psw" placeholder="" required>
                  <label for="psw">Jelszó</label>
                </li>
                <li><button name="btn_login">Bejelentkezés</button></li>
              </ul>
              <center>
                <a href="<?= http::$path?>/forgotpwd" title="">Elfelejtetted a jelszavad?</a><br>
                Nincs regisztrációd?<br>
                <a class="button" href="<?= http::$path ?>/registration">Regisztráció</a>
              </center>
            </form>
            <?php if( response::$vw->error ?? 0 ){ ?>
            <?= response::alert('error','login') ?>
            <?php } ?>
          </div>
        </div>
        <link rel="stylesheet" type="text/css" href="/shared/js/spritzer/dialog/dialog.css">
        <script type="module">
          import{ validation, dialog } from '/shared/js/spritzer/index.js'
          window.addEventListener( 'DOMContentLoaded', validation())
          <?php if( $_POST ?? 0 ){ ?>
          dialog( {type: 'status:error', content: 'Nem megfelelő email, vagy jelszó!', timeDelay: 6000, parent: '.cart'} )
          <?php } ?>
        </script>