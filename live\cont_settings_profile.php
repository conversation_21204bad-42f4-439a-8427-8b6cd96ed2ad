<?php
http::cleanPost();
if( $_POST['btn_settings'] ?? 0 ){
  if( !( response::$vw->error ?? 0 )){
    setup::$user->nickname = $_POST['nickname'];
    setup::$user->name = $_POST['name'];
    setup::$user->first_name = $_POST['first_name'];
    db::save( 'shared.users', [['nickname', $_POST['nickname']],
                               ['name', $_POST['name']],
                               ['first_name', $_POST['first_name']]
                              ], ['id', $_SESSION['USER'], 'i'] );
  }
}
if( $_POST['btn_settings_billing'] ?? 0 ){
  setup::$company->configuration->supplierInfo->taxNumber = $_POST['taxNumber'];
  setup::$company->configuration->supplierInfo->communityVatNumber = $_POST['communityVatNumber'];
  setup::$company->configuration->supplierInfo->name = $_POST['supplierName'];
  setup::$company->configuration->supplierInfo->countryCode = 'HU';
  setup::$company->configuration->supplierInfo->postalCode = $_POST['postalCode'];
  setup::$company->configuration->supplierInfo->city = $_POST['city'];
  setup::$company->configuration->supplierInfo->streetName = $_POST['streetName'];
  setup::$company->configuration->supplierInfo->publicPlaceCategory = $_POST['publicPlaceCategory'];
  setup::$company->configuration->supplierInfo->number = $_POST['number'];
  setup::$company->configuration->supplierInfo->building = $_POST['building'];
  setup::$company->configuration->supplierInfo->staircase = $_POST['staircase'];
  setup::$company->configuration->supplierInfo->floor = $_POST['floor'];
  setup::$company->configuration->supplierInfo->door = $_POST['door'];
  setup::$company->configuration->supplierInfo->bankAccountNumberHUF = $_POST['bankAccountNumberHUF'];
  setup::$company->configuration->supplierInfo->bankAccountNumberEUR = $_POST['bankAccountNumberEUR'];
  setup::save_configuration( $_SESSION['COMPANY'], setup::$company->configuration );
}
if( $_POST['btn_settings_pswchange'] ?? 0 ){
  if( $errorCode = valid::validPsw( $_POST['psw'], 8, '11111' )){
    $errorMessage = match ( $errorCode ){
      1 => 'Nincs megadva a jelszó!',
      2 => 'Nem megfelelő karakter van a jelszóban!',
      3 => 'Rövid a jelszó! Legalább 8 karakter',
      4 => 'Túl hosszú a jelszó! Maximum 30 karakter',
      5 => 'Nem tartalmaz számot a jelszó!',
      6 => 'Nem tartalmaz betűt a jelszó!',
      7 => 'Nem tartalmaz kisbetűt a jelszó!',
      8 => 'Nem tartalmaz nagybetűt a jelszó!',
      9 => 'Nem tartalmazhat csak azonos karaktereket a jelszó!',
      default => 'Ismeretlen hiba!'
    };
    response::add( 'error', 'form', ['psw' => $errorMessage] );
  }else{
    if( $_POST['psw'] == $_POST['psw_again'] ){
      $psw_hash = password_hash( $psw, PASSWORD_BCRYPT );
      db::save( 'shared.users', [['password', $_POST['psw']], ['password_hash', $psw_hash]], ['id', $_SESSION['USER'], 'i'] );
    }else response::add( 'error', 'form', ['psw_again' => 'Nem egyezik az újra megadott jelszó!'] );
  }
  if( response::$vw->error ?? 0 ) http::$route[2] = 'pc'; 
}
if( $_POST['btn_settings_emailchange'] ?? 0 ){
  if( http::cleanPost( $_POST['email'], 'email' ))
    response::add( 'error', 'form', ['email' => 'Nem megfelelő az emailcím!'] );
  elseif( db::get( 'shared.users', 'email="'.$email.'"' ))
    response::add( 'error', 'Ez az emailcím már létezik!<br>'.$email );
  else
    db::save( 'shared.users', [['email', $email]], ['id', $_SESSION['USER'], 'i'] );
  if( response::$vw->error ?? 0 ) http::$route[2] = 'ec';
}
/*
if(isset(http::$data['post']['btn_settings_billing'])){
  $error = false;
  http::clean('snev');
  http::clean('scim_telepules');
  http::clean('scim_pontos');
  http::clean('adohu');
  http::clean('cvszamhu');
  http::clean('bankhu');
  http::clean('pnev');
  http::clean('pcim_telepules');
  http::clean('pcim_pontos');
  if($error){
    response::add('error','form', $error);
    $error = false;
  }
  if(!response::get('error')){
    if(isset(http::$data['post']['regtax_id']) and http::$data['post']['regtax_id'] > 0){
      if(db::upin('adat.regtax',[['snev','s',http::$data['post']['snev']],
                                 ['scim_telepules','s',http::$data['post']['scim_telepules']],
                                 ['scim_pontos','s',http::$data['post']['scim_pontos']],
                                 ['adohu','s',http::$data['post']['adohu']],
                                 ['cvszamhu','s',http::$data['post']['cvszamhu']],
                                 ['bankhu','s',http::$data['post']['bankhu']],
                                 ['pnev','s',http::$data['post']['pnev']],
                                 ['pcim_telepules','s',http::$data['post']['pcim_telepules']],
                                 ['pcim_pontos','s',http::$data['post']['pcim_pontos']]]
                               ,['regtax_id','i',http::$data['post']['regtax_id']]))
        db::$stmt->close();
    }else{
      if(db::upin('adat.regtax',[['snev','s',http::$data['post']['snev']],
                                 ['scim_telepules','s',http::$data['post']['scim_telepules']],
                                 ['scim_pontos','s',http::$data['post']['scim_pontos']],
                                 ['adohu','s',http::$data['post']['adohu']],
                                 ['cvszamhu','s',http::$data['post']['cvszamhu']],
                                 ['bankhu','s',http::$data['post']['bankhu']],
                                 ['pnev','s',http::$data['post']['pnev']],
                                 ['pcim_telepules','s',http::$data['post']['pcim_telepules']],
                                 ['pcim_pontos','s',http::$data['post']['pcim_pontos']],
                                 ['reg_id','i',$_SESSION['REG']],
                                 ['projekt_id','i',http::$proid]])){
        http::$data['post']['regtax_id'] = db::$stmt->insert_id;
        db::$stmt->close();
      }
    }
  }
  http::reset();
}
*/
if( !( http::$route[2] ?? 0 )){
  $_POST['email'] = $_POST['email'] ?? setup::$user->email;
  $_POST['nickname'] = $_POST['nickname'] ?? setup::$user->nickname;
  $_POST['name'] = $_POST['name'] ?? setup::$user->name;
  $_POST['first_name'] = $_POST['first_name'] ?? setup::$user->first_name;

  $_POST['supplierName'] = $_POST['supplierName'] ?? setup::$company->configuration->supplierInfo->name;
  $_POST['taxNumber'] = $_POST['taxNumber'] ?? setup::$company->configuration->supplierInfo->taxNumber;
  $_POST['communityVatNumber'] = $_POST['communityVatNumber'] ?? setup::$company->configuration->supplierInfo->communityVatNumber;
  $_POST['countryCode'] = 'HU';
  $_POST['postalCode'] = $_POST['postalCode'] ?? setup::$company->configuration->supplierInfo->postalCode;
  $_POST['city'] = $_POST['city'] ?? setup::$company->configuration->supplierInfo->city;
  $_POST['streetName'] = $_POST['streetName'] ?? setup::$company->configuration->supplierInfo->streetName;
  $_POST['publicPlaceCategory'] = $_POST['publicPlaceCategory'] ?? setup::$company->configuration->supplierInfo->publicPlaceCategory;
  $_POST['number'] = $_POST['number'] ?? setup::$company->configuration->supplierInfo->number;
  $_POST['building'] = $_POST['building'] ?? setup::$company->configuration->supplierInfo->building;
  $_POST['staircase'] = $_POST['staircase'] ?? setup::$company->configuration->supplierInfo->staircase;
  $_POST['floor'] = $_POST['floor'] ?? setup::$company->configuration->supplierInfo->floor;
  $_POST['door'] = $_POST['door'] ?? setup::$company->configuration->supplierInfo->door;
  $_POST['bankAccountNumberHUF'] = $_POST['bankAccountNumberHUF'] ?? setup::$company->configuration->supplierInfo->bankAccountNumberHUF;
  $_POST['bankAccountNumberEUR'] = $_POST['bankAccountNumberEUR'] ?? setup::$company->configuration->supplierInfo->bankAccountNumberEUR;
  response::add( 'view', 'countries', model_basic::list_countries( 'name, code2' ));
}