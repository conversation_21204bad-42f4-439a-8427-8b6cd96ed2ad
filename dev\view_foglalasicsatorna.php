  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Foglalási csatornák</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új csatorna</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable( 'csatornalist', $_SESSION['csatornalist']['head'] ) ?>
      </article>
    </div>
  </section>

  <script src="/shared/js/spritzer.js"></script>
  <script>
    var func = {
      csatornalist_modalFunctions: function( data ){
        console.log('modalFunctions',data)
        let datable = data[0],
          ifunction = data[1],
                 id = data[2] || null
        switch( datable ){
          case 'csatornalist':
            switch( ifunction ){
              case 'edit_callback':
                event.preventDefault()
                let fd = new FormData( document.forms.namedItem( 'form_csatorna_modosit' ) )
                fd.append( 'csatorna_id', id )
                fd.append( 'btn_modositas', true )
                ajax( {
                  url: '/modal/csatorna',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok )
                      mdb( {width: '200px', margin_top_x: 2, time_delay: 3000, title: "Üzenet", content: "A Csatorna módosítva"} )
                    else
                      mdb( {width: '200px', margin_top_x: 2, time_delay: 3000, title: "Hiba", content: "Sikertelen a mentés"} )
                    setTimeout( location.replace( '/foglalasicsatorna' ), 2000 )
                  },
                  fail: ( err ) => {
                    mdb( {width: '200px', time_delay: 3000, title: "Hiba!", content: "Sikertelen művelet"} )
                  }
                } )
              break;
              case 'edit_close':
                location.replace('/foglalasicsatorna')
              break
            }
          break
        }
      },
      csatornalist_eventFunctions: function( data ){
        console.log( 'eventFunctions',data )
        let datable = data[0],
          ifunction = data[1],
                 id = data[2] || null
        switch( datable ){
          case 'csatornalist':
            switch( ifunction ){
              case 'trash-empty':
                let fd = new FormData()
                fd.append( 'csatorna_id', id )
                ajax( { 
                  url: '/modal/csatornadel',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      mdb( {width: '200px', margin_top_x: 2, time_delay: 3000, title: "Üzenet", content: "A csatorna törlésre került"} )
                      // táblázat frissítse
                      setTimeout( location.replace( '/foglalasicsatorna' ), 2000 )
                    }else
                      mdb( {width: '200px', margin_top_x: 2, time_delay: 3000, title: "Hiba", content: "Sikertelen a törlés, használatban van a csatorna"} )
                  },
                  fail: ( err ) => {
                    mdb( {width: '200px', time_delay: 3000, title: "Hiba!", content: "Sikertelen művelet"} )
                  }
                } )        
              break
            }
          break;
        }
      }
    }
    window.addEventListener('DOMContentLoaded', () => {
      datatable()
      
      const create = document.querySelector( '.btn.create' )
      create ? create.addEventListener( 'click', () => {
        ajax( { 
          url: '/modal/csatornacreate',
          done: ( back ) => {
            let response = JSON.parse( back.response )
            if( response.ok ){
              mdb( {
                id: 'csatornalist-edit',
                is_keep_open: true,
                load_file: '/modal/csatornalist/edit/'+ response.ok,
                func_event: ['csatornalist_modalFunctions', ['csatornalist', 'edit', response.ok]],
                func_close: ['csatornalist_modalFunctions', ['csatornalist', 'edit_close']],
                func_callback: ['csatornalist_modalFunctions', ['csatornalist', 'edit_callback', response.ok]]
              } )
              mdb( {width: '200px', margin_top_x: 2, time_delay: 3000, title: "Üzenet", content: "A csatorna létrehozva"} )
              // táblázat frissítse
              
            }else
              mdb( {width: '200px', margin_top_x: 2, time_delay: 3000, title: "Hiba", content: "Sikertelen a létrehozás"} )
          },
          fail: ( err ) => {
            mdb( {width: '200px', time_delay: 3000, title: "Hiba!", content: "Sikertelen művelet"} )
          }
        } )
      } ) : null
    } )
  </script>