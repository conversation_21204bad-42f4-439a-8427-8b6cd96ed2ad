<header>
  <h6>Lakóegység - <?= response::$vw->view->accommodationunittype->name ?></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="accommodation_units_edit" id="accommodation_units_edit">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <ul class="formbox">
      <li class="form col4" style="--toicon:var(--icon-angle-double-down)">
        <select name="status" placeholder="">
          <option value="1"<?=( $_POST['status'] == '1' )? ' selected' : '' ?>>Nem látható</option>  
          <option value="2"<?=( $_POST['status'] == '2' )? ' selected' : '' ?>>Látható</option>  
        </select>
        <label>Állapot</label>
      </li>
      <li class="form col3 bgonto" data-to="m2" data-tooltip="line">
        <input class="validation" type="number" name="area" placeholder="" value="<?= $_POST['area'] ?? '' ?>" min="0">
        <label>Méret</label>
        <div class="input-message tooltip">
          <div hidden class="error"></div>
          <div class="info">Tájékoztató adat a leírásnál</div>
        </div>
      </li>
      <li class="form col3">
        <input type="text" name="oeder" placeholder="" value="<?= $_POST['sort_by'] ?? '' ?>">
        <label>Sorrend</label>
      </li>
      <li class="form col6">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?? ''?>" required autocomplete="off">
        <label>Neve</label>
      </li>
      <li class="form col4">
        <input type="text" name="building" placeholder="" value="<?= $_POST['building'] ?? ''?>" autocomplete="off">
        <label>Épület</label>
      </li>
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="ntak_type" placeholder="">
          <option value="ECONOMY"<?=($_POST['ntak_type'] == 'ECONOMY')?' selected':''?>>ECONOMY</option>
          <option value="STANDARD"<?=($_POST['ntak_type'] == 'STANDARD')?' selected':''?>>STANDARD</option>
          <option value="SUPERIOR"<?=($_POST['ntak_type'] == 'SUPERIOR')?' selected':''?>>SUPERIOR</option>
          <option value="JUNIOR_SUITE"<?=($_POST['ntak_type'] == 'JUNIOR_SUITE')?' selected':''?>>Junior Suite (Kislakosztály)</option>
          <option value="SUITE"<?=($_POST['ntak_type'] == 'SUITE')?' selected':''?>>Suite (Lakosztály) </option>
          <option value="MOBILHAZ"<?=($_POST['ntak_type'] == 'MOBILHAZ')?' selected':''?>>Mobilház</option>
          <option value="UDULOHAZ"<?=($_POST['ntak_type'] == 'UDULOHAZ')?' selected':''?>>Üdülőház</option>
          <option value="PARCELLA"<?=($_POST['ntak_type'] == 'PARCELLA')?' selected':''?>>Parcella</option>
          <option value="SATORHELY_KEMPINGHELY"<?=($_POST['ntak_type'] == 'SATORHELY_KEMPINGHELY')?' selected':''?>>Sátorhely/ Kempinghely</option>
          <option value="EGYEDI"<?=($_POST['ntak_type'] == 'EGYEDI')?' selected':''?>>Egyedi</option>
          <option value="PRIVAT_SZOBA_KOZOS_FURDOVEL"<?=($_POST['ntak_type'] == 'PRIVAT_SZOBA_KOZOS_FURDOVEL')?' selected':''?>>Privát szoba közös fürdővel (ifjúsági szálláshely)</option>
          <option value="PRIVAT_SZOBA_SAJAT_FURDOVEL"<?=($_POST['ntak_type'] == 'PRIVAT_SZOBA_SAJAT_FURDOVEL')?' selected':''?>>Privát szoba saját fürdővel (ifjúsági szálláshely)</option>
          <option value="HALOTERMI_AGY"<?=($_POST['ntak_type'] == 'HALOTERMI_AGY')?' selected':''?>>Hálótermi ágy</option>
          <option value="APARTMAN"<?=($_POST['ntak_type'] == 'APARTMAN')?' selected':''?>>Apartman</option>
          <option value="EGYEB"<?=($_POST['ntak_type'] == 'EGYEB')?' selected':''?>>Egyéb</option>
        </select>
        <label>NTAK típus</label>
      </li>
      <li class="form col5 bgonto" data-to="db">
        <input class="validation" type="number" name="single_beds_number" placeholder="" value="<?= $_POST['single_beds_number'] ?? ''?>" min="0">
        <label>Egyszemélyes ágyak</label>
        <div class="input-message">
          <div hidden class="error"></div>
        </div>
      </li>
      <li class="form col5 bgonto" data-to="db">
        <input class="validation" type="number" name="double_beds_number" placeholder="" value="<?= $_POST['double_beds_number'] ?? ''?>" min="0">
        <label>Kétszemélyes ágyak</label>
        <div class="input-message">
          <div hidden class="error"></div>
        </div>
      </li>
      <li class="form col5 bgonto" data-to="db">
        <input class="validation" type="number" name="extra_beds_number" placeholder="" value="<?= $_POST['extra_beds_number'] ?? ''?>" min="0">
        <label>Pótágyak</label>
        <div class="input-message">
          <div hidden class="error"></div>
        </div>
      </li>
      <li class="form col5 bgonto" data-to="fő">
        <input type="text" placeholder="" value="<?= response::$vw->view->accommodationunittype->places ?>" disabled>
        <label>Férőhely</label>
      </li>
      <?php if( setup::is_right( 12 )){ ?>
      <li class="form col0">
        <textarea id="joditeditor1" name="leirasrovid"><?= html_entity_decode($_POST['brief'] ?? '')?></textarea>
        <label>Rövid leírás</label>
      </li>
      <li class="form col0">
        <textarea id="joditeditor2" name="leiras"><?= html_entity_decode($_POST['description'] ?? '')?></textarea>
        <label>Leírás</label>
      </li>
      <?php } ?>
    </ul>
  </form>
</section>
<footer>
  <button class="callback validation-submit" type="submit" form="accommodation_units_edit" name="btn_modositas" title="Lakóegység adatainak mentése">Módosít</button>
  <button class="close">Kilép</button>
</footer>