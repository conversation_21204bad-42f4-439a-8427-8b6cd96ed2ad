<?php
if( $_POST ?? 0 ){
  $error = false;
  http::cleanPost();
  if( valid::isVariable( $_POST['ntakNumber'] )) $error['ntakNumber'] = 'Kötelező megadni az NTAK számot!';
  if( valid::isVariable( $_POST['taxNumber'] )) $error['taxNumber'] = 'Kötelező megadni az adószámot!';
  if( valid::isVariable( $_POST['name'] )) $error['name'] = 'Kötelező megadni a szálláshely nevét!';
  if( valid::validNumeric( $_POST['accommodation_units_number'], 1, 8 )) $error['accommodation_units_number'] = 'Kötelező megadni lakóegységek számát! 1-8 között!';
  if( valid::validNumeric( $_POST['number_of_places'], 1, 16 )) $error['number_of_places'] = 'Kötelező megadni a férőhelyek számát! 1-16 között!';
  
  if( $error ){
    response::add( 'error', 'form', $error );
    $error = false;
  }
  if( !( response::$vw->error ?? 0 )){
    if( !( setup::$company->configuration->supplierInfo ?? 0 )) setup::$company->configuration->supplierInfo = (object) [];
    setup::$company->configuration->supplierInfo->ntakNumber = $_POST( 'ntakNumber' );
    setup::$company->configuration->supplierInfo->taxNumber = $_POST( 'taxNumber' );
    setup::$company->configuration->supplierInfo->communityVatNumber = $_POST( 'communityVatNumber' );
    setup::$company->configuration->supplierInfo->accommodation_units_number = $_POST( 'accommodation_units_number' );
    setup::$company->configuration->supplierInfo->number_of_places = $_POST( 'number_of_places' );
    setup::$company->name = $_POST( 'name' );
    setup::$company->configuration->is_generates = 1;
    setup::save_configuration( $_SESSION['COMPANY'], setup::$company->configuration );
    setup::save( 'shared.companies', [['name', setup::$company->name]], ['id', $_SESSION['COMPANY'], 'i'] );

    if( !is_dir( 'upload/'.$_SESSION['COMPANY'] ))
      mkdir( 'upload/'.$_SESSION['COMPANY'], 0666 );

    if( setup::$company->configuration->supplierInfo->accommodation_units_number == 1 ){
      $id = db::save(
        DATABASENAME.'.accommodation_unit_types',
        [
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['name', setup::$company->name],
          ['places', setup::$company->configuration->supplierInfo->number_of_places, 'i']
        ]
      );
      db::save(
        DATABASENAME.'.accommodation_units',
        [
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['accommodation_unit_type_id', $id, 'i'],
          ['name', setup::$company->name]
        ]
      );
    }

    http::gourl( '/' );
  }
}
$_POST['ntakNumber'] = $_POST['ntakNumber'] ?? setup::$company->configuration->supplierInfo->ntakNumber ?? '';
$_POST['taxNumber'] = $_POST['taxNumber'] ?? setup::$company->configuration->supplierInfo->taxNumber ?? '';
$_POST['communityVatNumber'] = $_POST['communityVatNumber'] ?? setup::$company->configuration->supplierInfo->communityVatNumber ?? '';
$_POST['name'] = $_POST['name'] ?? setup::$company->name ?? '';
$_POST['accommodation_units_number'] = $_POST['accommodation_units_number'] ?? setup::$company->configuration->supplierInfo->accommodation_units_number ?? 1;
$_POST['number_of_places'] = $_POST['number_of_places'] ?? setup::$company->configuration->supplierInfo->number_of_places ?? 1;
