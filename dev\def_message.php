<?php
function messageReplaceVariables( $string, $data ){
  if( is_integer( $data ))
    $data = db::get_booking_info( $data );
  if( is_array( $data )){
    $lakoegysegek = '';
    $data['erkezes'] = $data['accommodation_units'][0]['erkezes'];
    $data['tavozas'] = $data['accommodation_units'][0]['tavozas'];
    if( count( $data['accommodation_units'] ) > 1 )
      foreach( $data['accommodation_units'] as $unit ){
        if( $unit['erkezes'] < $data['erkezes'] )
          $data['erkezes'] = $unit['erkezes'];
        if( $unit['tavozas'] > $data['tavozas'] )
          $data['tavozas'] = $unit['tavozas'];
        $lakoegysegek.= (( $lakoegysegek != '' )? ', ' : '' ).$unit['erkezes'].' - '.$unit['tavozas'].' '.$unit['lakoegyseg'];
      }
    else
      $lakoegysegek = $data['accommodation_units'][0]['lakoegyseg'];
    if( setup::$company->configuration->supplierInfo->accommodation_units_number == 1 ) $lakoegysegek = '';
    
    $elolegfiz =
      ( round(( strtotime( $data['erkezes'] ) - strtotime( date( 'Y-m-d' ))) / 60 / 60 / 24 ) > setup::$company->configuration->events->advancePayment->deadline )
      ? date( 'Y-m-d', strtotime( date( 'Y-m-d' )) + setup::$company->configuration->events->advancePayment->deadline * 24 * 60 * 60 ) : date( 'Y-m-d' );

    $string = str_replace(
      [
        '[FOGLALASID]',
        '[FOGLALASLINK]',
        '[FOGLALASLINKANDROID]',
        '[FOGLALASFOGLALONEV]',
        '[FOGLALASERKEZES]',
        '[FOGLALASTAVOZAS]',
        '[FOGLALASEJ]',
        '[FOGLALASFO]',
        '[FOGLALASLAKOEGYSEGEK]',
        '[FOGLALASFIZETENDO]',
        '[FOGLALASELOLEG]',
        '[ELOLEGFIZKELT]'
      ],
      [
        $data['foglalas_id'],
        'https://tren.hu?re='.base64_encode( 'foglalas|'.$data['foglalas_id'] ),
        'intent://tren.hu?re='.base64_encode( 'foglalas|'.$data['foglalas_id'] ).'#Intent;scheme=https;package=com.android.chrome;end;',
        $data['kapcsolattarto'],
        $data['erkezes'],
        $data['tavozas'],
        (( strtotime( $data['tavozas'] ) - strtotime( $data['erkezes'] )) / 24 / 60 / 60 ).' éj',
        $data['vendegfo'],
        $lakoegysegek,
        $data['fizetendo'].' HUF',
        $data['eloleg'].' HUF',
        $elolegfiz
      ],
      $string
    );
  }
  return $string;
}
