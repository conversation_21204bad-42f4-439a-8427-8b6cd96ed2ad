<header>
  <h6>Kupon</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_coupon">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <ul class="formbox">
      <li class="form col3" style="--toicon:var(--icon-angle-double-down)">
        <select name="status" placeholder="">
          <option value="1"<?=( $_POST['status'] == '1' )? ' selected' : '' ?>>Nem látható</option>  
          <option value="2"<?=( $_POST['status'] == '2' )? ' selected' : '' ?>>L<PERSON>tható</option>  
        </select>
        <label>Állapot</label>
      </li>
      <li class="form col7">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?? '' ?>">
        <label>Megnevezés</label>
      </li>
      <li class="form col5">
        <input type="text" name="validity_start" placeholder="" value="<?= $_POST['validity_start'] ?? date( 'Y-m-d' ) ?>">
        <label>Kupon érvényesség kezdete</label>
      </li>
      <li class="form col5">
        <input type="text" name="validity_end" placeholder="" value="<?= $_POST['validity_end'] ?? $_POST['validity_start'] ?? date( 'Y-m-d' ) ?>">
        <label>Kupon érvényesség vége</label>
      </li>
      <li class="form col5">
        <input type="text" name="booking_period_start" placeholder="" value="<?= $_POST['booking_period_start'] ?? date( 'Y-m-d' ) ?>">
        <label>Foglalási időszak kezdete</label>
      </li>
      <li class="form col5">
        <input type="text" name="booking_period_end" placeholder="" value="<?= $_POST['booking_period_end'] ?? $_POST['booking_period_start'] ?? date( 'Y-m-d' ) ?>">
        <label>Foglalási időszak vége</label>
      </li>
      <li class="form col25">
        <input type="text" name="code" placeholder="" value="<?= $_POST['code'] ?? '' ?>">
        <label>Kód</label>
      </li>
      <li class="form col25" style="--toicon:var(--icon-angle-double-down)">
        <select name="multiple" placeholder="">
          <option value="0"<?=( $_POST['multiple'] == '0' )? ' selected' : '' ?>>Többször</option>  
          <option value="1"<?=( $_POST['multiple'] == '1' )? ' selected' : '' ?>>Egyszer</option>  
        </select>
        <label>Felhasználható</label>
      </li>
      <li class="form col3" style="--toicon:var(--icon-angle-double-down)">
        <select name="fixed" placeholder="">
          <option value="0"<?=( $_POST['fixed'] == '0' )? ' selected' : '' ?>>Érték</option>  
          <option value="1"<?=( $_POST['fixed'] == '1' )? ' selected' : '' ?>>Százalék</option>
          <option value="2"<?=( $_POST['fixed'] == '2' )? ' selected' : '' ?>>Napi érték</option>
        </select>
        <label>Kedvezmény mód</label>
      </li>
      <li class="form col2">
        <input type="text" name="discount" placeholder="" value="<?= $_POST['discount'] ?? 0 ?>">
        <label>Kedvezmény</label>
      </li>
      <li class="form col3" style="--toicon:var(--icon-angle-double-down)">
        <select name="weekly_start_day" placeholder="">
          <option value="0"<?=( $_POST['weekly_start_day'] == '0' )? ' selected' : '' ?>>Nem használt</option>  
          <option value="1"<?=( $_POST['weekly_start_day'] == '1' )? ' selected' : '' ?>>Hétfő</option>  
          <option value="2"<?=( $_POST['weekly_start_day'] == '1' )? ' selected' : '' ?>>Kedd</option>
          <option value="3"<?=( $_POST['weekly_start_day'] == '2' )? ' selected' : '' ?>>Szerda</option>
          <option value="4"<?=( $_POST['weekly_start_day'] == '4' )? ' selected' : '' ?>>Csütörtök</option>
          <option value="5"<?=( $_POST['weekly_start_day'] == '5' )? ' selected' : '' ?>>Péntek</option>
          <option value="6"<?=( $_POST['weekly_start_day'] == '6' )? ' selected' : '' ?>>Szombat</option>
          <option value="7"<?=( $_POST['weekly_start_day'] == '7' )? ' selected' : '' ?>>Vasárnap</option>
        </select>
        <label>Heti első nap</label>
      </li>
      <li class="form col3" style="--toicon:var(--icon-angle-double-down)">
        <select name="weekly_stop_day" placeholder="">
          <option value="0"<?=( $_POST['weekly_stop_day'] == '0' )? ' selected' : '' ?>>Nem használt</option>
          <option value="1"<?=( $_POST['weekly_stop_day'] == '1' )? ' selected' : '' ?>>Hétfő</option>  
          <option value="2"<?=( $_POST['weekly_stop_day'] == '2' )? ' selected' : '' ?>>Kedd</option>
          <option value="3"<?=( $_POST['weekly_stop_day'] == '3' )? ' selected' : '' ?>>Szerda</option>
          <option value="4"<?=( $_POST['weekly_stop_day'] == '4' )? ' selected' : '' ?>>Csütörtök</option>
          <option value="5"<?=( $_POST['weekly_stop_day'] == '5' )? ' selected' : '' ?>>Péntek</option>
          <option value="6"<?=( $_POST['weekly_stop_day'] == '6' )? ' selected' : '' ?>>Szombat</option>
          <option value="7"<?=( $_POST['weekly_stop_day'] == '7' )? ' selected' : '' ?>>Vasárnap</option>
        </select>
        <label>Heti utolsó nap</label>
      </li>
      <li class="form col4">
        <input type="text" name="min_booking_days" placeholder="" value="<?= $_POST['min_booking_days'] ?? '1' ?>">
        <label>Min. foglalt napok</label>
      </li>      
      <li class="form col0">
        <textarea name="description"><?= $_POST['description'] ?? '' ?></textarea>
        <label>Leírás</label>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Kupon mentése">Módosít</button>
</footer>