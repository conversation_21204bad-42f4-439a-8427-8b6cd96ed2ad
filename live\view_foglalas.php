  <section class="container">
    <div class="cart">
        <?= response::alert('message',0,0,5)?>
        <?= response::alert()?>
        <header><h5>Foglalások</h5></header>
        <article>
          <?php if( response::$vw->view->szabadlakoegysegek ){ ?>
          <?= table::datatable(
            'foglalasoklist',
            [
              'id' => ['th' => '#'],
              'csatorna' => ['th' => 'Csatorna'],
              'allapotstr' => ['th' => 'Állapot', 'status' => 'allapot', 'min-width' => '130px'],
              'kapcsolattarto' => ['th' => 'Foglaló', 'min-width' => '200px', 'order' => 'kapcsolattarto'],
              'foglalvastr' => ['th' => 'Foglalva', 'order' => 'foglalva', 'min-width' => '115px'],
              'erkezesstr' => ['th' => 'Érkezés', 'order' => 'erkezes', 'min-width' => '100px'],
              'tavozasstr' => ['th' => 'Távozás', 'order' => 'tavozas', 'min-width' => '100px'],
              'vendegfo' => ['th' => 'Fő', 'min-width' => '30px'],
              'name' => ['th' => 'Lakóegység'],
              'fizetendo' => ['th' => 'Díj', 'class' => 'tar', 'min-width' => '82px'],
              'fizetve' => ['th' => 'Fizetve', 'class' => 'tar', 'min-width' => '82px'],
              '*1' => [
                'th' => '',
                'icons' => [
                  'edit:modal:Szerkesztés',
                  'stackoverflow:modal:Számlázás:::confirm:where:is_invoiced'
                ],
                'min-width' => '90px'
              ]
            ],
            ['search' => true, 'create' => 'Új foglalás'],
            true
          ) ?>
          <?php }else{ ?>
          Előbb lakóegységet kell felvinni!
          <?php } ?>
        </article>
    </div>
  </section>
  <script type="module">
    import
      {$, dialog, dialogRebuild, closeDialog, datatable, insertRow, renderTbody, ajax, getFormatDate, getNextDate, getDiffDay} from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd
      switch( ifunction ){
        case 'foglalasoklist_add':
          dialog( {
            id: 'foglalasoklist-create',
            isESCkey: true,
            isCloseBack: true,
            isKeepOpen: true,
            loadFile: '/modal/foglalasoklistcreate',
            events: ['change'],
            eventManagers: {
              'callback': ['', 'foglalasoklist_create_callback'],
              'change': [['input[name="nap1"], input[name="nap2"], input[name="ej"]', 'foglalasoklist_change_date']]
            },
            eventManagerFunctions: ( data ) => {
              let 
                ifunction = data[1],
                id = data[2] || null,
                fd
              switch( ifunction ){
                case 'foglalasoklist_create_callback':
                  event.preventDefault()
                  let 
                    is_ko = true,
                    fd = new FormData( document.forms.namedItem( 'form_uj_foglalas' ) )
                  fd.append( 'btn_mentes', true )
                  if( fd.get('nap1') && fd.get('nap1') != '' &&
                      fd.get('nap2') && fd.get('nap2') != '' &&
                      fd.get('lakoegysegek[]') )
                    ajax( {
                      url: '/modal/foglalascreate',
                      body: fd,
                      done: ( back ) => {
                        let response = JSON.parse( back.response )
                        if( response.ok ){
                          dialog( {type: 'status:success', content: 'A foglalás mentve', timeDelay: 3000} )
                          insertRow( $( '[data-datatable="foglalasoklist"]' ), response.id )
                          closeDialog( 'foglalasoklist-create' )
                        }else
                          dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
                      },
                      fail: ( err ) => {
                        dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                      }
                    } )
                  else{
                    dialog( {type: 'status:error', content: 'Csillaggal jelölt mezők kitöltése kötelező!<br><b>Érkezés</b>, <b>távozás</b> és legalább egy <b>lakóegység</b> kiválasztása', timeDelay: 6000} )
                    is_ko = false;
                  }
                break
                case 'foglalasoklist_change_date':
                  let
                    erkezes = document.querySelector('input[name="nap1"]'),
                    tavozas = document.querySelector('input[name="nap2"]'),
                    ej = document.querySelector('input[name="ej"]')
                  
                  switch( event.target.name ){
                    case 'ej':
                      tavozas.value = ej2tavozas( erkezes.value, ej.value )
                    break
                    case 'nap2':
                      ej.value = getDiffDay( new Date(tavozas.value.valueOf() ), new Date(erkezes.value.valueOf() ) )
                    break
                    case 'nap1':
                      let
                        erk = new Date(erkezes.value.valueOf() ),
                        tav = new Date(tavozas.value.valueOf() )
                      if( erk < tav )
                        ej.value = getDiffDay( tav, erk )
                      else
                        tavozas.value = ej2tavozas( erkezes.value, ej.value )
                    break
                  }

                  function ej2tavozas( erkezes, ej ){
                    return getFormatDate( 'Y-m-d', getNextDate( new Date( erkezes ), parseInt( ej ) ) )
                  }
                break
              }
            }
          } )
        break
        case 'foglalasoklist_edit':
          return {
            'click': [
              ['button[name="btn_modositas"]', 'foglalasoklist_edit_foglalas_modositas', id],
              ['.btn.lemond', 'foglalasoklist_edit_foglalas_lemond', id],
              ['.btn.torol', 'foglalasoklist_edit_foglalas_torol', id],
              ['.btn.torolle', 'foglalasoklist_edit_lakoegyseg_torol', id],
              ['.btn.plussz-le', 'foglalasoklist_edit_lakoegyseg_hozzaad', id],
              ['button[name="btn_modositas-le"]', 'foglalasoklist_edit_lakoegyseg_modositas', id],
              ['button[name="btn_fizet"]', 'foglalasoklist_edit_fizet', id],
              ['[data-datatable="fizetesek"] .icons i', 'foglalasoklist_edit_fizet_torol']
            ]
          }
        break
        case 'foglalasoklist_edit_foglalas_modositas':
          event.preventDefault()
          fd = new FormData( $( 'form[name="form_foglalas_modosit"]' ) )
          fd.append( 'foglalas_id', id )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/foglalas',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                renderTbody( $( '[data-datatable="foglalasoklist"]' ) )
                closeDialog( 'foglalasoklist-edit' )
                dialog( {type: 'status:success', content: 'A foglalás módosítva', timeDelay: 3000} )
              }else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'foglalasoklist_edit_foglalas_lemond':
          event.preventDefault()
            fd = new FormData()
            fd.append( 'foglalas_id', id )
            ajax( {
              url: '/modal/lemondas',
              body: fd,
              done: ( back ) => {
                let response = JSON.parse( back.response )
                if( response.ok ){
                  renderTbody( $( '[data-datatable="foglalasoklist"]' ) )
                  closeDialog( 'foglalasoklist-edit' )
                  dialog( {type: 'status:success', content: 'A foglalás lemondva', timeDelay: 3000} )
                }else
                  dialog( {type: 'status:error', content: 'Sikertelen a lemondás!', timeDelay: 6000} )
              },
              fail: ( err ) => {
                dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
              }
            } )
        break
        case 'foglalasoklist_edit_foglalas_torol':
          event.preventDefault()
          fd = new FormData()
          fd.append( 'foglalas_id', id )
          ajax( {
            url: '/modal/foglalastorol',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                renderTbody( $( '[data-datatable="foglalasoklist"]' ) )
                  closeDialog( 'foglalasoklist-edit' )
                dialog( {type: 'status:success', content: 'A foglalás törölve', timeDelay: 3000} )
              }else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'foglalasoklist_edit_lakoegyseg_torol':
          event.preventDefault()
          fd = new FormData( event.target.closest( 'form[name="form_data_modosit-le"]' ) )
          fd.append( 'foglalas_id', id )
          ajax( {
            url: '/modal/foglalastorolle',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                renderTbody( $( '[data-datatable="foglalasoklist"]' ) )
                dialogRebuild( 'foglalasoklist-edit' )
                dialog( {type: 'status:success', content: 'A foglalt lekóegység törölve', timeDelay: 3000} )
              }else
              dialog( {type: 'status:error', content: 'Sikertelen a foglalt lakóegység törlés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'foglalasoklist_edit_lakoegyseg_hozzaad':
          event.preventDefault()
          let options = '';
          <?php foreach( response::$vw->view->szabadlakoegysegek as $lakoegyseg ){ ?>
          options += '<option value="<?= $lakoegyseg->id ?>"><?= $lakoegyseg->name ?></option>';
          <?php } ?>
          dialog({
            id: 'lakoegyseg-select',
            type: 'dialog',
            btnCallback: 'Hozzáad',
            isBtnCallback: true,
            isBtnClose: true,
            isKeepOpen: true,
            title: 'Lakóegység választás',
            content:
              '<form name="form_data_ujlakoegyseg">' +
              '  <ul class="formbox">' +              
              '    <li class="form col0" style="--toicon:var(--icon-angle-double-down)">' +
              '      <select name="ujlakoegyseg_id">' + options +
              '      </select>' +
              '      <label>Lakóegység</label>' +
              '    </li>' +
              '  </ul>' +
              '</form>',
            eventManagers: {
              'afterBuilding': ['', 'lakoegyseg_select'],
              'callback': ['', 'lakoegyseg_callback', id]
            },
            eventManagerFunctions: function( data ){
              let ifunction = data[1],
                  id = data[2] || null,
                  fd
              switch( ifunction ){
                case 'lakoegyseg_callback':
                  fd = new FormData( $( 'form[name="form_data_ujlakoegyseg"]' ) )
                  fd.append( 'foglalas_id', id )
                  ajax( {
                    url: '/modal/plusszfoglalle',
                    body: fd,
                    done: ( back ) => {
                      let response = JSON.parse( back.response )
                      if( response.ok ){
                        closeDialog( 'lakoegyseg-select' )
                        renderTbody( $( '[data-datatable="foglalasoklist"]' ) )
                        dialogRebuild( 'foglalasoklist-edit' )
                        dialog( {type: 'status:success', content: 'A foglalás módosítva', timeDelay: 3000} )
                      }else
                      dialog( {type: 'status:error', content: 'A foglalás módosítása sikertelen!', timeDelay: 6000} )
                    },
                    fail: ( err ) => {
                      dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                    }
                  } )
                break;
                case 'lakoegyseg_select':
                  const select = document.querySelector( 'form[name="form_data_ujlakoegyseg"] select' )
                  select ? select.focus() : null
                break;
              }
            }
          } )
        break
        case 'foglalasoklist_edit_lakoegyseg_modositas':
          event.preventDefault()
          fd = new FormData( event.target.closest( 'form[name="form_data_modosit-le"]' ) )
          fd.append( 'btn_modositas-le', true )
          ajax( {
            url: '/modal/foglalaslemod',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                renderTbody( $( '[data-datatable="foglalasoklist"]' ) )
                dialog( {type: 'status:success', content: 'A foglalás módosítva', timeDelay: 3000} )
              }else
              dialog( {type: 'status:error', content: 'A foglalás módosítása sikertelen!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'foglalasoklist_edit_fizet':
          event.preventDefault()
          fd = new FormData( $( 'form[name="form_data_fizet"]' ) )
          fd.append( 'foglalas_id', id )
          fd.append( 'btn_fizet', true )
          ajax( {
            url: '/modal/foglalasfizet',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                dialogRebuild( 'foglalasoklist-edit' )
                dialog( {type: 'status:success', content: 'A fizetés mentve', timeDelay: 3000} )
              }else
                dialog( {type: 'status:error', content: 'A fizetés mentése sikertelen!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'foglalasoklist_edit_fizet_torol':
          event.preventDefault()
          fd = new FormData()
          fd.append( 'penz_id', event.target.closest( 'tr[data-id]' ).dataset.id )
          ajax( {
            url: '/modal/foglalasfizettorol',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                dialogRebuild( 'foglalasoklist-edit' )
                dialog( {type: 'status:success', content: 'A fizetési tétel törölve', timeDelay: 3000} )                
              }else
              dialog( {type: 'status:error', content: 'A fizetés törlése nem sikerült!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'foglalasoklist_edit_close':
          //renderTbody( $( '[data-datatable="foglalasoklist"]' ) )
          //dialogRebuild( 'foglalasoklist-edit' )
          location.replace( '/foglalas' )
        break
        case 'foglalasoklist_stackoverflow':
          dialog( {
            id: 'foglalasoklist-invoice',
            isESCkey: true,
            isCloseBack: true,
            isKeepOpen: true,
            loadFile: '/modal/foglalasinvoice',
            eventManagers: {
              'callback': ['', 'foglalasoklist_invoice_callback']
            },
            eventManagerFunctions: ( data ) => {
              let 
                ifunction = data[1],
                id = data[2] || null,
                fd
              switch( ifunction ){
                case 'foglalasoklist_invoice_callback':
                  event.preventDefault()
                  let 
                    is_ko = true,
                    fd = new FormData( document.forms.namedItem( 'form_uj_foglalas' ) )
                  fd.append( 'btn_mentes', true )
                  if( fd.get('nap1') && fd.get('nap1') != '' &&
                      fd.get('nap2') && fd.get('nap2') != '' &&
                      fd.get('lakoegysegek[]') )
                    ajax( {
                      url: '/modal/foglalascreate',
                      body: fd,
                      done: ( back ) => {
                        let response = JSON.parse( back.response )
                        if( response.ok ){
                          dialog( {type: 'status:success', content: 'A foglalás mentve', timeDelay: 3000} )
                          insertRow( $( '[data-datatable="foglalasoklist"]' ), response.id )
                          closeDialog( 'foglalasoklist-create' )
                        }else
                          dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
                      },
                      fail: ( err ) => {
                        dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                      }
                    } )
                  else{
                    dialog( {type: 'status:error', content: 'Csillaggal jelölt mezők kitöltése kötelező!<br><b>Érkezés</b>, <b>távozás</b> és legalább egy <b>lakóegység</b> kiválasztása', timeDelay: 6000} )
                    is_ko = false;
                  }
                break
              }
            }
          } )
        break
      }
    }

    window.addEventListener('DOMContentLoaded', () => {
      datatable( {
        eventManagers: {
          'add': ['', 'foglalasoklist_add']
        },
        eventManagerFunctions
      } )     
    } )
  </script>