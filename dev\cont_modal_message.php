<?php
if( $message_templates = setup::list( DATABASENAME.'.message_templates', 'company_id = '.$_SESSION['COMPANY'].' AND MID(signs, 1, 1) = "2"' ) )
  response::add( 'view', 'templates', $message_templates );
if( $bookings = setup::list( DATABASENAME.'.foglalas', 'ceg_id = '.$_SESSION['COMPANY'].' AND email<>""', 'foglalas_id DESC' ) )
  response::add( 'view', 'bookings', $bookings );
if( $message = db::get( 'messages', http::$route[3] ) ){
  $_POST['id'] = $message['id'];
  $_POST['body'] = $message['body'];
  $_POST['subject'] = $message['name'];
  $_POST['booking_id'] = $message['booking_id'];
  $_POST['template_id'] = $message['template_id'];
  $_POST['device'] = $message['device'];
  $_POST['type'] = $message['type'] ?? 1;
  $_POST['sender'] = $message['sender'] ?? setup::$company->configuration->supplierInfo->email ?? '';
  $_POST['to'] = $message['fromto'];
  $_POST['sending_time'] = $message['sending_time'];
}