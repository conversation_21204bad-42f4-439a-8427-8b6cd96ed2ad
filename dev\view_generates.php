  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Indu<PERSON><PERSON> be<PERSON>llítások</h5>
      </header>
      <article>
        <p>
          <PERSON><PERSON><PERSON><PERSON> a beállításokra mindenképp szükség <PERSON>, hogy a használat elkezdődjön. A regisztrációkat ellenőrizzük,
          mert fontosnak tartjuk, hogy csak szab<PERSON>lyosan működő szálláshelyek legyenek a rendszerünkben.
        </p>
        <form name="form_generates" method="post" action="<?= http::$path ?>/generates">
          <ul class="formbox">
            <li class="form col0">
              <input class="validation" type="text" name="name" value="<?= $_POST['name'] ?? '' ?>" placeholder="" required autofocus autocomplete="off">
              <label>Szálláshely neve</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col0">
              <input class="validation" type="text" name="ntakNumber" value="<?= $_POST['ntakNumber'] ?? '' ?>" placeholder="" required autocomplete="off">
              <label>NTAK regisztrációs szám</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col0">
              <input class="validation" type="text" name="taxNumber" value="<?= $_POST['taxNumber'] ?? '' ?>" placeholder="" required autocomplete="off">
              <label>Magyar adószám (NAV)</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col0">
              <input type="text" name="communityVatNumber" value="<?= $_POST['communityVatNumber'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Közösségi adószám (EU)</label>
            </li>
            <li class="form col0" data-tooltip="line">
              <input class="validation" type="number" name="accommodation_units_number" value="<?= $_POST['accommodation_units_number'] ?? '' ?>" placeholder="" min="1" max="8" required>
              <label>Lakóegységek száma (engedély szerint)</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Ha 8 lakóegységnél több van írjon nekünk</div>
              </div>
            </li>
            <li class="form col0 bgonto" data-to="fő" data-tooltip="line">
              <input class="validation" type="number" name="number_of_places" value="<?= $_POST['number_of_places'] ?? '' ?>" placeholder="" min="1"  max="16" required>
              <label>Férőhelyek száma (engedély szerint)</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Ha 16 férőhelynél több van írjon nekünk</div>
              </div>
            </li>
            <li><button name="btn_generates">Mentés</button></li>
          </ul>
        </form>            
      </article>
    </div>
  </section>
  <script type="module">
    import{ validation } from '/shared/js/spritzer/index.js'
    window.addEventListener( 'DOMContentLoaded', validation())
  </script>