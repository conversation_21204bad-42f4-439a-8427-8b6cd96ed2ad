.gdprplusckb{
  display: none;
  position: fixed;
  z-index: 9999;
  bottom: 40px;
  left: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  color: var(--profirat-color);
  background-color: var(--profirat-bg-color);
  & > div{
    & > a{
      display: block;
      position: absolute;
      right: 0;
      width: 30px;
      height: 30px;
      padding: 4px;
      margin: -15px -15px 0 0;
      border-radius: 50%;
      font-size: 20px;
      line-height: 20px;
      text-align: center;
      border-color: var(--profirat-border-color);
      background-color: var(--profirat-link-bg-color);
      color: var(--profirat-link-color);
      &:hover {
        text-decoration: none;
        color: var(--profirat-link-hover-color);
      }
    }
    & > div{
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 100%;
      padding: 15px;
      border-bottom-width: 1px;
      border-bottom-style: solid;
      border-bottom-color: var(--profirat-border-color);
      &:last-child{ border: 0 }
      & > span{
        display: flex;
        align-items: center;
        & input{
          display: inline-block;
          margin-right: 4px;
        }
      }
      & > a{ margin-left: 4px }
    }
  }
}

.gdprplusbox{
  display: none;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  & > div{
    position: relative;
    z-index: 1;
    max-width: 90vw;
    margin: 10vh auto;
    border-radius: 6px;
    color: var(--profirat-color, #000);
    background-color: var(--profirat-bg-color, #fff);
    & > a{
      display: block;
      position: absolute;
      right: 0;
      width: 30px;
      height: 30px;
      padding: 4px;
      margin: -15px -15px 0 0;
      border-radius: 50%;
      font-size: 20px;
      line-height: 20px;
      text-align: center;
      background-color: var(--profirat-link-bg-color);
      border-color: var(--profirat-border-color);
      color: var(--profirat-link-color);
      &:hover{
        text-decoration: none;
        color: var(--profirat-link-hover-color);
      }
    }
    & > span{
      display: block;
      padding: 4px 15px;
      border-top-width: 1px;
      border-top-style: solid;
      border-top-color: var(--profirat-border-color);
    }
    & > div{
      max-width: 100%;
      max-height: 80vh;
      padding: 15px;
      overflow: hidden;
      overflow-y: auto;
      & h5{
        font-size: 18px;
        margin: 6px 0;
      }
      & h5{
        font-size: 16px;
        margin: 4px 0;
      }
      & p{
        font-size: 14px;
        margin: 2px 0;
      }
    }
  }
}

@media only screen and (min-width: 480px){
  .gdprplusbox > div{ max-width: 60vw }
}