<?php
/**
 * A<PERSON> kez<PERSON><PERSON>
 *
 * $_SESSION['PROID'] has<PERSON><PERSON><PERSON><PERSON>, ha má<PERSON>
 *
 * @method `init();`          <PERSON><PERSON><PERSON><PERSON><PERSON> indítása
 * @method `response();`      <PERSON><PERSON><PERSON>z összeállítás
 * @method `apikeykontrol();` <PERSON><PERSON><PERSON><PERSON>
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2019, Tánczos Róbert
 *
 * @version 1.4.0
 * @since 1.4.0 2023.10.21 api_key kezelés változása
 * @since 1.3.0 2022.12.30 verziózás bevezetése a fájlnévben
 * @since 1.2.0 2020.02.29 <PERSON><PERSON><PERSON>s, általánossá tétel
 * @since 1.1.0 2018.07.11 Optimalizálás, php 7.x
 * @since 1.0.0 2018.01.08
 */

class api extends model_basic{
  public static $request = [];
  public static $error = false;

  private static function _requestStatus( $code ){
    $status = [
      200 => 'OK',
      404 => 'Not Found',
      405 => 'Method Not Allowed',
      500 => 'Internal Server Error'
    ];
    return $status[$code ?? 500] ?? $status[500];
  }

  private static function _cleanInputs( $data ){
    return is_array( $data )? array_map( [self::class, '_cleanInputs'], $data ) : trim( strip_tags( $data ) );
  }
  
  public static function init(){
    switch( $_SERVER['REQUEST_METHOD'] ?? 'POST' ){
      case 'POST': self::$request = self::_cleanInputs( $_POST ?? [] ); break;
      case 'GET': self::$request = self::_cleanInputs( $_GET ?? [] ); break;
      default: self::response( 'Invalid Method', 405 ); break;
    }
    if( ( self::$request ?? 0 ) and array_values( self::$request )[0] == '' ){
      self::$request['apikey'] = array_key_first( self::$request );
      unset( self::$request[self::$request['apikey']] );
    }
  }
  
  public static function response( $data, $status = 200 ){
    header( 'HTTP/1.1 ' . $status . ' ' . self::_requestStatus( $status ) );
    echo json_encode( $data );
  }

  public static function apikeykontrol(){
    if( $company = self::get_companyApi( self::$request['apikey'] ?? '' ) ){
      $company['configuration'] = json_decode( $company['configuration'] ?? '' );
      $company['api_settings'] = json_decode( $company['api_settings'] ?? '' );
      if( $company['configuration']->api_domain ?? 0 ){        
        $referer = ( $_SERVER['HTTP_REFERER'] ?? 0 )?
          substr( $_SERVER['HTTP_REFERER'], strpos( $_SERVER['HTTP_REFERER'], '://' ) + 3 ) : 0;
        $referer = $referer ? substr( $referer, 0, strpos( $referer, '/' ) ) : 0;
        $ok_domain = false;
        if( $referer )
          foreach( $company['api_settings'] as $api )
            if( ( $api ?? 0 ) and $api->domain == $referer ){
              $ok_domain = true;
              break;
            }
        else $ok_domain = true;
        if( !$ok_domain ){
          self::$error = ["status" => "false", "hibakod" => "AK04" ]; // jogosulatlan domain-ről hozzáférés
          return false;
        }
      }
      if( $company['configuration']->api_ip ?? 0 ){
        $ok_ip = false;
        if( $api ?? 0 )
          $ok_ip = ( $api->ip == $_SERVER['REMOTE_ADDR'] )? true : false;
        else
          foreach( $company['api_settings'] as $api )
            if( ( $api ?? 0 ) and $api->ip == $_SERVER['REMOTE_ADDR'] ){
              $ok_ip = true;
              break;
            }
        if( !$ok_ip ){
          self::$error = ["status" => "false", "hibakod" => "AK03" ]; // jogosulatlan IP-ről hozzáférés
          return false;
        }
      }
      if( $api ?? 0 ){
        if( $api->expire < date('Y-m-d' ) ){
          self::$error = ["status" => "false", "hibakod" => "AK02" ]; // lejárt apikulcs
          return false;
        }
      }
    }else{
      self::$error = ["status" => "false", "hibakod" => "AK01" ]; // nemlétező apikulcs
      return false;
    }
    return $company;
  }
}