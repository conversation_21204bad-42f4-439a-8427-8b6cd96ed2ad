<?php
date_default_timezone_set( 'Europe/Budapest' );
const CLASSVERSIONS = [
  'errors'      => '1.3',
  'mysql'       => '2.2'
];
CONST DATABASENAME = 'tren';
require_once 'shared/class/autoload.php';
errors::start( 37 );

// import
if(( $_GET['f'] ?? 0 ) and $_GET['f'] == 'I' ){
  $message = ical::import_icals();
  error_log( $message."\n", 3, 'shared/log/ical'.date('Ymd').'.txt' );
  error_log( "\n", 3, 'shared/log/ical'.date('Ymd').'.txt' );
}

// export
if( $lakoegysegek = db::list( DATABASENAME.'.accommodation_units', 'MID(signs,1,1)=2' )){
  if(( $_GET['f'] ?? 0 ) and $_GET['f'] == 'E' )
    foreach( $lakoegysegek as $lakoegyseg )
      ical::export_ical( $lakoegyseg['id'], $lakoegyseg['company_id'] );
}
/*
https://admin.booking.com/hotel/hoteladmin/extranet_ng/manage/search_reservations.html?source=nav&upcoming_reservations=1&hotel_id=1867885&lang=hu&date_from=2024-08-09&date_to=2024-08-10&date_type=booking
Booking foglalások foglalás időpontja szerinti időszakban.

Booking főoldal
https://admin.booking.com/hotel/hoteladmin/extranet_ng/manage/home.html?hotel_id=1867885&lang=hu&ses=362bc7e78f705c0408ca448ceaa1359d
Booking foglalás link ( Bejelentkezés után működik. res_id a foglalás azonosítója )
https://admin.booking.com/hotel/hoteladmin/extranet_ng/manage/booking.html?&hotel_id=1867885&lang=hu&res_id=4396300272
*/