{ "project": 37,"num": "1","when": "2025-06-28 14:45:38","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "2","when": "2025-06-28 14:45:38","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "3","when": "2025-06-28 14:45:38","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg 06.08.-09.</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>50000</unitPrice>                     <unitPriceHUF>50000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>50000</lineNetAmount>                             <lineNetAmountHUF>50000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>50000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>50000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg 06.08.-09.</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>50000</unitPrice>
                    <unitPriceHUF>50000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>50000</lineNetAmount>
                            <lineNetAmountHUF>50000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>50000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>50000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "4","when": "2025-06-28 14:45:38","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "9","when": "2025-06-28 14:45:38","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1167,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1167,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 1"}]}
{ "project": 37,"num": "10","when": "2025-06-28 14:45:38","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "Hiba: mysqli_sql_exception: Incorrect decimal value: '' for column 'price_net' at row 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "Hiba: mysqli_sql_exception: Incorrect decimal value: '' for column 'price_net' at row 1"}]}
