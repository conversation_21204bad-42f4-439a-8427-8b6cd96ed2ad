{ "project": 37,"num": "1","when": "2025-06-28 14:16:08","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellen<PERSON><PERSON><PERSON> kezd<PERSON>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellen<PERSON><PERSON><PERSON> kezd<PERSON>"}]}
{ "project": 37,"num": "2","when": "2025-06-28 14:16:08","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'true', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'true',
))"}]}
{ "project": 37,"num": "3","when": "2025-06-28 14:16:08","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "4","when": "2025-06-28 14:16:08","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "5","when": "2025-06-28 14:16:08","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "6","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "7","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'true', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'true',
))"}]}
{ "project": 37,"num": "8","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "9","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "10","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "11","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "12","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "13","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "14","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "15","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "16","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "17","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "18","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "19","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "20","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "21","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "22","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "23","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "24","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "25","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "26","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "27","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'true', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'true',
))"}]}
{ "project": 37,"num": "28","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "29","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "30","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "31","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "32","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'true', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'true',
))"}]}
{ "project": 37,"num": "33","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "34","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "35","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "36","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "37","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "38","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "39","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "40","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "41","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "42","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'true', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'true',
))"}]}
{ "project": 37,"num": "43","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "44","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "45","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "46","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "47","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "48","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "49","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "50","when": "2025-06-28 14:16:09","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "51","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "52","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "53","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "54","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "55","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "56","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "57","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'true', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'true',
))"}]}
{ "project": 37,"num": "58","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "59","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "60","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "61","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "62","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'true', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'true',
))"}]}
{ "project": 37,"num": "63","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "64","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "65","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "66","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "67","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "68","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "69","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "70","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "71","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "72","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "73","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "74","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "75","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
{ "project": 37,"num": "76","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"message": "DEBUG: documentumType = 3, ellenőrzés kezdése","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1083,"function": "user_error","args": "DEBUG: documentumType = 3, ellenőrzés kezdése"}]}
{ "project": 37,"num": "77","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"message": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(    0 => 'false', ))","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1087,"function": "user_error","args": "DEBUG: advanceIndicator = \SimpleXMLElement::__set_state(array(
   0 => 'false',
))"}]}
{ "project": 37,"num": "78","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"message": "DEBUG: advancePaymentData isset = false","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1088,"function": "user_error","args": "DEBUG: advancePaymentData isset = false"}]}
{ "project": 37,"num": "79","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"message": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1094,"function": "user_error","args": "DEBUG: Feltétel teljesült! documentumType = 2-re állítva"}]}
{ "project": 37,"num": "80","when": "2025-06-28 14:16:10","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"message": "DEBUG: Végső documentumType = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1099,"function": "user_error","args": "DEBUG: Végső documentumType = 2"}]}
