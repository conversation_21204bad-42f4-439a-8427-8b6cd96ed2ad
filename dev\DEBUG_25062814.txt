{ "project": 37,"num": "1","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "2","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "3","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg 06.08.-09.</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>50000</unitPrice>                     <unitPriceHUF>50000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>50000</lineNetAmount>                             <lineNetAmountHUF>50000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>50000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>50000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg 06.08.-09.</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>50000</unitPrice>
                    <unitPriceHUF>50000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>50000</lineNetAmount>
                            <lineNetAmountHUF>50000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>50000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>50000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "4","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "8","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "9","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "11","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 1"}]}
{ "project": 37,"num": "12","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 1"}]}
{ "project": 37,"num": "13","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "14","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "15","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "16","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>18788</unitPrice>                     <unitPriceHUF>18788</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>18788</lineNetAmount>                             <lineNetAmountHUF>18788</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>18788</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>18788</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>18788</unitPrice>
                    <unitPriceHUF>18788</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>18788</lineNetAmount>
                            <lineNetAmountHUF>18788</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>18788</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>18788</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "17","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "21","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "22","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "24","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 2"}]}
{ "project": 37,"num": "25","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 2"}]}
{ "project": 37,"num": "26","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "27","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "28","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "29","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>32270</unitPrice>                     <unitPriceHUF>32270</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>32270</lineNetAmount>                             <lineNetAmountHUF>32270</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>32270</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>32270</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>32270</unitPrice>
                    <unitPriceHUF>32270</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>32270</lineNetAmount>
                            <lineNetAmountHUF>32270</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>32270</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>32270</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "30","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "34","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "35","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "37","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 3","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 3"}]}
{ "project": 37,"num": "38","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 3","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 3"}]}
{ "project": 37,"num": "39","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "40","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "41","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 2"}]}
{ "project": 37,"num": "42","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>36318</unitPrice>                     <unitPriceHUF>36318</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>36318</lineNetAmount>                             <lineNetAmountHUF>36318</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>36318</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>36318</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>                 <line>                     <lineNumber>2</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                         <advancePaymentData>                             <advanceOriginalInvoice>TO-2025-33</advanceOriginalInvoice>                             <advancePaymentDate>2025-06-02</advancePaymentDate>                             <advanceExchangeRate>1</advanceExchangeRate>                         </advancePaymentData>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>-18788</unitPrice>                     <unitPriceHUF>-18788</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>-18788</lineNetAmount>                             <lineNetAmountHUF>-18788</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>-18788</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>-18788</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>36318</unitPrice>
                    <unitPriceHUF>36318</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>36318</lineNetAmount>
                            <lineNetAmountHUF>36318</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>36318</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>36318</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
                <line>
                    <lineNumber>2</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                        <advancePaymentData>
                            <advanceOriginalInvoice>TO-2025-33</advanceOriginalInvoice>
                            <advancePaymentDate>2025-06-02</advancePaymentDate>
                            <advanceExchangeRate>1</advanceExchangeRate>
                        </advancePaymentData>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>-18788</unitPrice>
                    <unitPriceHUF>-18788</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>-18788</lineNetAmount>
                            <lineNetAmountHUF>-18788</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>-18788</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>-18788</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "43","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "47","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "48","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "50","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 4","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 4"}]}
{ "project": 37,"num": "51","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 4","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 4"}]}
{ "project": 37,"num": "52","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #2"}]}
{ "project": 37,"num": "56","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "57","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "59","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #2, invoice_id: 4","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #2, invoice_id: 4"}]}
{ "project": 37,"num": "60","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #2, visszakapott ID: 5","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #2, visszakapott ID: 5"}]}
{ "project": 37,"num": "61","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 2 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 2 sor feldolgozva"}]}
{ "project": 37,"num": "62","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "63","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 4","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 4"}]}
{ "project": 37,"num": "64","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>29455</unitPrice>                     <unitPriceHUF>29455</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>29455</lineNetAmount>                             <lineNetAmountHUF>29455</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>29455</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>29455</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>                 <line>                     <lineNumber>2</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>31880</unitPrice>                     <unitPriceHUF>31880</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>31880</lineNetAmount>                             <lineNetAmountHUF>31880</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>31880</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>31880</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>                 <line>                     <lineNumber>3</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>31880</unitPrice>                     <unitPriceHUF>31880</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>31880</lineNetAmount>                             <lineNetAmountHUF>31880</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>31880</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>31880</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>                 <line>                     <lineNumber>4</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                         <advancePaymentData>                             <advanceOriginalInvoice>TO-2025-32</advanceOriginalInvoice>                             <advancePaymentDate>2025-06-05</advancePaymentDate>                             <advanceExchangeRate>1</advanceExchangeRate>                         </advancePaymentData>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>-50000</unitPrice>                     <unitPriceHUF>-50000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>-50000</lineNetAmount>                             <lineNetAmountHUF>-50000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>-50000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>-50000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>29455</unitPrice>
                    <unitPriceHUF>29455</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>29455</lineNetAmount>
                            <lineNetAmountHUF>29455</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>29455</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>29455</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
                <line>
                    <lineNumber>2</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>31880</unitPrice>
                    <unitPriceHUF>31880</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>31880</lineNetAmount>
                            <lineNetAmountHUF>31880</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>31880</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>31880</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
                <line>
                    <lineNumber>3</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>31880</unitPrice>
                    <unitPriceHUF>31880</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>31880</lineNetAmount>
                            <lineNetAmountHUF>31880</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>31880</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>31880</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
                <line>
                    <lineNumber>4</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                        <advancePaymentData>
                            <advanceOriginalInvoice>TO-2025-32</advanceOriginalInvoice>
                            <advancePaymentDate>2025-06-05</advancePaymentDate>
                            <advanceExchangeRate>1</advanceExchangeRate>
                        </advancePaymentData>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>-50000</unitPrice>
                    <unitPriceHUF>-50000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>-50000</lineNetAmount>
                            <lineNetAmountHUF>-50000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>-50000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>-50000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "65","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "69","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "70","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "72","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 5","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 5"}]}
{ "project": 37,"num": "73","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 6","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 6"}]}
{ "project": 37,"num": "74","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #2"}]}
{ "project": 37,"num": "78","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "79","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "81","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #2, invoice_id: 5","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #2, invoice_id: 5"}]}
{ "project": 37,"num": "82","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #2, visszakapott ID: 7","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #2, visszakapott ID: 7"}]}
{ "project": 37,"num": "83","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #3","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #3"}]}
{ "project": 37,"num": "87","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "88","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "90","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #3, invoice_id: 5","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #3, invoice_id: 5"}]}
{ "project": 37,"num": "91","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #3, visszakapott ID: 8","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #3, visszakapott ID: 8"}]}
{ "project": 37,"num": "92","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #4","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #4"}]}
{ "project": 37,"num": "96","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "97","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "99","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #4, invoice_id: 5","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #4, invoice_id: 5"}]}
{ "project": 37,"num": "100","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #4, visszakapott ID: 9","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #4, visszakapott ID: 9"}]}
{ "project": 37,"num": "101","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 4 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 4 sor feldolgozva"}]}
{ "project": 37,"num": "102","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "103","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "104","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>14500</unitPrice>                     <unitPriceHUF>14500</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>14500</lineNetAmount>                             <lineNetAmountHUF>14500</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>14500</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>14500</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>14500</unitPrice>
                    <unitPriceHUF>14500</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>14500</lineNetAmount>
                            <lineNetAmountHUF>14500</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>14500</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>14500</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "105","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "109","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "110","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "112","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 6","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 6"}]}
{ "project": 37,"num": "113","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 10","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 10"}]}
{ "project": 37,"num": "114","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "115","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "116","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "117","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg 06.15.-06.16.</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>13000</unitPrice>                     <unitPriceHUF>13000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>13000</lineNetAmount>                             <lineNetAmountHUF>13000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>13000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>13000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg 06.15.-06.16.</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>13000</unitPrice>
                    <unitPriceHUF>13000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>13000</lineNetAmount>
                            <lineNetAmountHUF>13000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>13000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>13000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "118","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "122","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "123","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "125","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 7","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 7"}]}
{ "project": 37,"num": "126","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 11","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 11"}]}
{ "project": 37,"num": "127","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "128","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "129","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "130","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>4</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>15000</unitPrice>                     <unitPriceHUF>15000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>60000</lineNetAmount>                             <lineNetAmountHUF>60000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>60000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>60000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>4</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>15000</unitPrice>
                    <unitPriceHUF>15000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>60000</lineNetAmount>
                            <lineNetAmountHUF>60000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>60000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>60000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "131","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "135","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "136","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "138","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 8","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 8"}]}
{ "project": 37,"num": "139","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 12","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 12"}]}
{ "project": 37,"num": "140","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "141","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "142","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "143","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>47919</unitPrice>                     <unitPriceHUF>47919</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>47919</lineNetAmount>                             <lineNetAmountHUF>47919</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>47919</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>47919</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>47919</unitPrice>
                    <unitPriceHUF>47919</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>47919</lineNetAmount>
                            <lineNetAmountHUF>47919</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>47919</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>47919</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "144","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "148","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "149","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "151","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 9","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 9"}]}
{ "project": 37,"num": "152","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 13","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 13"}]}
{ "project": 37,"num": "153","when": "2025-06-28 14:52:33","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "154","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "155","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 2"}]}
{ "project": 37,"num": "156","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>26000</unitPrice>                     <unitPriceHUF>26000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>26000</lineNetAmount>                             <lineNetAmountHUF>26000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>26000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>26000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>                 <line>                     <lineNumber>2</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                         <advancePaymentData>                             <advanceOriginalInvoice>TO-2025-38</advanceOriginalInvoice>                             <advancePaymentDate>2025-06-11</advancePaymentDate>                             <advanceExchangeRate>1</advanceExchangeRate>                         </advancePaymentData>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>-13000</unitPrice>                     <unitPriceHUF>-13000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>-13000</lineNetAmount>                             <lineNetAmountHUF>-13000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>-13000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>-13000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>26000</unitPrice>
                    <unitPriceHUF>26000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>26000</lineNetAmount>
                            <lineNetAmountHUF>26000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>26000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>26000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
                <line>
                    <lineNumber>2</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                        <advancePaymentData>
                            <advanceOriginalInvoice>TO-2025-38</advanceOriginalInvoice>
                            <advancePaymentDate>2025-06-11</advancePaymentDate>
                            <advanceExchangeRate>1</advanceExchangeRate>
                        </advancePaymentData>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>-13000</unitPrice>
                    <unitPriceHUF>-13000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>-13000</lineNetAmount>
                            <lineNetAmountHUF>-13000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>-13000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>-13000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "157","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "161","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "162","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "164","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 10","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 10"}]}
{ "project": 37,"num": "165","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 14","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 14"}]}
{ "project": 37,"num": "166","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #2"}]}
{ "project": 37,"num": "170","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "171","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "173","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #2, invoice_id: 10","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #2, invoice_id: 10"}]}
{ "project": 37,"num": "174","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #2, visszakapott ID: 15","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #2, visszakapott ID: 15"}]}
{ "project": 37,"num": "175","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 2 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 2 sor feldolgozva"}]}
{ "project": 37,"num": "176","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "177","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "178","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>4</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>30000</unitPrice>                     <unitPriceHUF>30000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>120000</lineNetAmount>                             <lineNetAmountHUF>120000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>120000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>120000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>4</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>30000</unitPrice>
                    <unitPriceHUF>30000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>120000</lineNetAmount>
                            <lineNetAmountHUF>120000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>120000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>120000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "179","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "183","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "184","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "186","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 11","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 11"}]}
{ "project": 37,"num": "187","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 16","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 16"}]}
{ "project": 37,"num": "188","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "189","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "190","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "191","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg 2025.07.18.-20.</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>130000</unitPrice>                     <unitPriceHUF>130000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>130000</lineNetAmount>                             <lineNetAmountHUF>130000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>130000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>130000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg 2025.07.18.-20.</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>130000</unitPrice>
                    <unitPriceHUF>130000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>130000</lineNetAmount>
                            <lineNetAmountHUF>130000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>130000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>130000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "192","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "196","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "197","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "199","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 12","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 12"}]}
{ "project": 37,"num": "200","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 17","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 17"}]}
{ "project": 37,"num": "201","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "202","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "203","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "204","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>PIECE</unitOfMeasure>                     <unitPrice>18000</unitPrice>                     <unitPriceHUF>18000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>18000</lineNetAmount>                             <lineNetAmountHUF>18000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>18000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>18000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>PIECE</unitOfMeasure>
                    <unitPrice>18000</unitPrice>
                    <unitPriceHUF>18000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>18000</lineNetAmount>
                            <lineNetAmountHUF>18000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>18000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>18000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "205","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "209","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "210","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "212","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 13","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 13"}]}
{ "project": 37,"num": "213","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 18","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 18"}]}
{ "project": 37,"num": "214","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "215","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "216","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 2"}]}
{ "project": 37,"num": "217","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>32800</unitPrice>                     <unitPriceHUF>32800</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>32800</lineNetAmount>                             <lineNetAmountHUF>32800</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>32800</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>32800</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>                 <line>                     <lineNumber>2</lineNumber>                     <advanceData>                         <advanceIndicator>true</advanceIndicator>                         <advancePaymentData>                             <advanceOriginalInvoice>TO-2025-44</advanceOriginalInvoice>                             <advancePaymentDate>2025-06-27</advancePaymentDate>                             <advanceExchangeRate>1</advanceExchangeRate>                         </advancePaymentData>                     </advanceData>                     <lineExpressionIndicator>false</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Előleg</lineDescription>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>-18000</lineNetAmount>                             <lineNetAmountHUF>-18000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>-18000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>-18000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>32800</unitPrice>
                    <unitPriceHUF>32800</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>32800</lineNetAmount>
                            <lineNetAmountHUF>32800</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>32800</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>32800</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
                <line>
                    <lineNumber>2</lineNumber>
                    <advanceData>
                        <advanceIndicator>true</advanceIndicator>
                        <advancePaymentData>
                            <advanceOriginalInvoice>TO-2025-44</advanceOriginalInvoice>
                            <advancePaymentDate>2025-06-27</advancePaymentDate>
                            <advanceExchangeRate>1</advanceExchangeRate>
                        </advancePaymentData>
                    </advanceData>
                    <lineExpressionIndicator>false</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Előleg</lineDescription>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>-18000</lineNetAmount>
                            <lineNetAmountHUF>-18000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>-18000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>-18000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "218","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "222","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "223","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "225","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 14","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 14"}]}
{ "project": 37,"num": "226","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 19","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 19"}]}
{ "project": 37,"num": "227","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #2","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #2"}]}
{ "project": 37,"num": "231","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "232","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "234","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #2, invoice_id: 14","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #2, invoice_id: 14"}]}
{ "project": 37,"num": "235","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #2, visszakapott ID: 20","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #2, visszakapott ID: 20"}]}
{ "project": 37,"num": "236","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 2 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 2 sor feldolgozva"}]}
{ "project": 37,"num": "237","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "238","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "239","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>31000</unitPrice>                     <unitPriceHUF>31000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>31000</lineNetAmount>                             <lineNetAmountHUF>31000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>31000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>31000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>31000</unitPrice>
                    <unitPriceHUF>31000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>31000</lineNetAmount>
                            <lineNetAmountHUF>31000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>31000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>31000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "240","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "244","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "245","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "247","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 15","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 15"}]}
{ "project": 37,"num": "248","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 21","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 21"}]}
{ "project": 37,"num": "249","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
{ "project": 37,"num": "250","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"message": "DEBUG: invoiceLines count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1143,"function": "user_error","args": "DEBUG: invoiceLines count = 1"}]}
{ "project": 37,"num": "251","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"message": "DEBUG: invoiceLines->line count = 1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1144,"function": "user_error","args": "DEBUG: invoiceLines->line count = 1"}]}
{ "project": 37,"num": "252","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"message": "DEBUG: invoiceLines XML = <invoiceLines>                 <mergedItemIndicator>false</mergedItemIndicator>                 <line>                     <lineNumber>1</lineNumber>                     <advanceData>                         <advanceIndicator>false</advanceIndicator>                     </advanceData>                     <lineExpressionIndicator>true</lineExpressionIndicator>                     <lineNatureIndicator>SERVICE</lineNatureIndicator>                     <lineDescription>Magánszálláshely szolgáltatás</lineDescription>                     <quantity>1</quantity>                     <unitOfMeasure>OWN</unitOfMeasure>                     <unitOfMeasureOwn>éj</unitOfMeasureOwn>                     <unitPrice>31000</unitPrice>                     <unitPriceHUF>31000</unitPriceHUF>                     <lineAmountsNormal>                         <lineNetAmountData>                             <lineNetAmount>31000</lineNetAmount>                             <lineNetAmountHUF>31000</lineNetAmountHUF>                         </lineNetAmountData>                         <lineVatRate>                             <vatExemption>                                 <case>AAM</case>                                 <reason>Alanyi adómentes</reason>                             </vatExemption>                         </lineVatRate>                         <lineGrossAmountData>                             <lineGrossAmountNormal>31000</lineGrossAmountNormal>                             <lineGrossAmountNormalHUF>31000</lineGrossAmountNormalHUF>                         </lineGrossAmountData>                     </lineAmountsNormal>                     <intermediatedService>false</intermediatedService>                 </line>             </invoiceLines>","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1145,"function": "user_error","args": "DEBUG: invoiceLines XML = <invoiceLines>
                <mergedItemIndicator>false</mergedItemIndicator>
                <line>
                    <lineNumber>1</lineNumber>
                    <advanceData>
                        <advanceIndicator>false</advanceIndicator>
                    </advanceData>
                    <lineExpressionIndicator>true</lineExpressionIndicator>
                    <lineNatureIndicator>SERVICE</lineNatureIndicator>
                    <lineDescription>Magánszálláshely szolgáltatás</lineDescription>
                    <quantity>1</quantity>
                    <unitOfMeasure>OWN</unitOfMeasure>
                    <unitOfMeasureOwn>éj</unitOfMeasureOwn>
                    <unitPrice>31000</unitPrice>
                    <unitPriceHUF>31000</unitPriceHUF>
                    <lineAmountsNormal>
                        <lineNetAmountData>
                            <lineNetAmount>31000</lineNetAmount>
                            <lineNetAmountHUF>31000</lineNetAmountHUF>
                        </lineNetAmountData>
                        <lineVatRate>
                            <vatExemption>
                                <case>AAM</case>
                                <reason>Alanyi adómentes</reason>
                            </vatExemption>
                        </lineVatRate>
                        <lineGrossAmountData>
                            <lineGrossAmountNormal>31000</lineGrossAmountNormal>
                            <lineGrossAmountNormalHUF>31000</lineGrossAmountNormalHUF>
                        </lineGrossAmountData>
                    </lineAmountsNormal>
                    <intermediatedService>false</intermediatedService>
                </line>
            </invoiceLines>"}]}
{ "project": 37,"num": "253","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"message": "DEBUG: Feldolgozás - sor #1","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1150,"function": "user_error","args": "DEBUG: Feldolgozás - sor #1"}]}
{ "project": 37,"num": "257","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"message": "DEBUG: quantity = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1158,"function": "user_error","args": "DEBUG: quantity = ''"}]}
{ "project": 37,"num": "258","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"message": "DEBUG: unitPrice = ''","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1162,"function": "user_error","args": "DEBUG: unitPrice = ''"}]}
{ "project": 37,"num": "260","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"message": "DEBUG: Mentés előtt - sor #1, invoice_id: 16","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1171,"function": "user_error","args": "DEBUG: Mentés előtt - sor #1, invoice_id: 16"}]}
{ "project": 37,"num": "261","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"message": "DEBUG: Mentés után - sor #1, visszakapott ID: 22","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1173,"function": "user_error","args": "DEBUG: Mentés után - sor #1, visszakapott ID: 22"}]}
{ "project": 37,"num": "262","when": "2025-06-28 14:52:34","type": 1024,"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"message": "DEBUG: Összesen 1 sor feldolgozva","trace": [{"file": "/site/hd0/tren.hu/website/dev/index.php","line": 74,"function": "require_once","args": "/site/hd0/tren.hu/website/dev/modal.php"},{"file": "/site/hd0/tren.hu/website/dev/modal.php","line": 1175,"function": "user_error","args": "DEBUG: Összesen 1 sor feldolgozva"}]}
