<?php
if( $foglalas = db::get_foglalas( http::$route[3] )){
  response::add( 'view','foglalas', $foglalas );
  response::add( 'view', 'countries', model_basic::list_countries( 'code2, name' ));
  if( $foglalas['invoice_info'] ?? 0 ){
    $invoice_info = json_decode( $foglalas['invoice_info'] );
    $_POST['inv_name'] = $invoice_info->name ?? '';
    $_POST['inv_countryCode'] = $invoice_info->countryCode ?? 'HU';
    $_POST['inv_zip'] = $invoice_info->zip ?? '';
    $_POST['inv_city'] = $invoice_info->city ?? '';
    $_POST['inv_address'] = $invoice_info->address ?? '';
    $_POST['inv_taxNumber'] = $invoice_info->taxNumber ?? '';
    $_POST['inv_communityVatNumber'] = $invoice_info->communityVatNumber ?? '';
  }else{
    $_POST['inv_name'] = $foglalas['kapcsolattarto'];
    $_POST['inv_countryCode'] = 'HU';
  }
}