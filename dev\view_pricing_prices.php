  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5><PERSON>rak</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új ár</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable(
          'accommodation_unit_type_prices',
          [ 'name' => ['th' => 'Megnevezés'],
            'guestsprices' => ['th' => 'Árak'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés',
                'trash-empty:event:Törlés:::confirm'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { $, $$, datatable, dialog, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd, li, prices
      switch( ifunction ){
        case 'accommodation_unit_type_prices_create':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_price' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/pricecreate',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
              location.replace( '/pricing/prices' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'accommodation_unit_type_prices_edit':
          return {
            'click': [
              ['div.del-price', 'prices_del_price'],
              ['button.add-price', 'prices_add_price']
            ]
          }
        break
        case 'accommodation_unit_type_prices_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_price' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/price',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Az adatok módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'accommodation_unit_type_prices_edit_close':
          location.replace( '/pricing/prices' )
        break
        case 'accommodation_unit_type_prices_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( {
            url: '/modal/pricedel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/pricing/prices' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'prices_add_price':
          event.preventDefault()
          prices = JSON.parse( $( 'input[name="prices"]' ).value )
          const guest = $( 'input#guest' ).value
          const price = $( 'input#price' ).value
          $( 'input#guest' ).value = ''
          $( 'input#price' ).value = ''
          prices.push( [guest, price] )
          $( 'input[name="prices"]' ).value = JSON.stringify( prices )
          $('.uladd').insertAdjacentHTML( 'beforeend',
            `
            <li class="form col5" style="--toicon:var(--icon-trash-empty)">
              <input type="text" placeholder="" value="${guest} fő ${price} Ft/éj" disabled>
              <label>Ár</label>
              <div class="del-price" data-key="${prices.length - 1}" title="Ár törlése"></div>
            </li>
            ` )
        break
        case 'prices_del_price':
          li = event.target.parentNode
          prices = JSON.parse( $( 'input[name="prices"]' ).value )
          const key = event.target.dataset.key
          prices.splice( key, 1 )
          $( 'input[name="prices"]' ).value = JSON.stringify( prices )
          li.remove()
          li = $$( 'div.del-price' )
          for( let i = 0; i < li.length; i++ )
            li[i].dataset.key = i
        break
      }
    }

    window.addEventListener('DOMContentLoaded', () => {
      datatable( {
        create: {
          loadFile: '/modal/pricescreatemodal'
        },
        eventManagerFunctions
      } )
    } )
  </script>