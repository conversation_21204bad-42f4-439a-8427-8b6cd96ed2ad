<?php
/**
 * nye<PERSON><PERSON> fordító
 *
 * @method `init();`         signs <PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON>a
 * @method `q();       Vállalkoz<PERSON> beolvasása api kulcs alapján
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2017, Táncz<PERSON> Ró<PERSON>
 * 
 * @version 3.0.0
 * @since 3.0.0 2025.06.08 Új mySQL táblák, teljes újraí<PERSON>
 * @since 2.1.0 2017.05.26 2-3 ágból egy új egységes
 */

class Translate extends model_basic{
  
  private static $sourceLng;    /**< alap nyelv */
  private static $dictionary;   /**< szótár */
  private static $collectionIs; /**< kifejezések gyűjtése true/false */
  private static $page;         /**< weboldal */

  /** Szótár betöltése
   */

  private static function loadingDictionary(): void {
    self::$dictionary = [];
    if( !isset( $_SESSION['LNG'] )) return;
    if( $_SESSION['LNG'] == self::$sourceLng and !self::$collectionIs ) return;
    $list = self::list_dictionary( self::$sourceLng, $_SESSION['LNG'], $_SESSION['PROID'] ?? 0, self::$page );
    if( !$list ) return;
    foreach( $list as $row )
      self::$dictionary[mb_strtolower( $row['question'],'UTF-8' )] = [$row['answer'], $row['id']];
  }

  /** Egy adott kifejezés a szótárból
   *
   * @param string $targetLng cél nyelv 
   * @param string $question  lefordítandó kifejezés
   * @return string           lefordított kifejezés vagy az eredeti
   */
  private static function getDictionary( string $targetLng, string $question ): string {
    if( $targetLng == self::$sourceLng ) return $question;
    return self::get_dictionary( self::$sourceLng, $targetLng, $question );
  }
  
  /** Alapértékek beállítása
   * 
   * @param string $sourceLng    alap nyelv, alapértelmezett érték 'hu'
   * @param bool   $collectionIs ha igaz, akkor a kifejezések kigyűjtésre kerülnek 
   */
  public static function init( string $sourceLng = 'hu', bool $collectionIs = false ): void {
    self::$sourceLng = $sourceLng;
    self::$collectionIs = $collectionIs;
    self::$page = '';
    self::loadingDictionary();
  }

  /** Fordítás
   * 
   * @param string  $question     lefordítandó kifejezés
   * @param int     $modification 0: nem változtat (alapértelmezés)
   *                              1: minden karakter kicsi
   *                              2: első karakter nagy
   *                              3: minden szó első betüje nagy
   *                              4: minden karakter nagy
   * @param string $targetLng     ha meg van adva, akkor ez a fordítás ezen a nyelven történjen
   * @return string               lefordított kifejezés vagy az eredeti
   */
  public static function q( string $question, int $modification = 0, string $targetLng = '' ): string {
    $lowerQuestion = mb_strtolower( $question, 'UTF-8' );
    if( self::$collectionIs and strlen( $question )){
      if( isset( self::$dictionary[$lowerQuestion] ))
        self::save_last_use_dictionary( intval( self::$dictionary[$lowerQuestion][1] ));   
      else{
        $id = self::save_last_use_dictionary();
        self::save_new_term( $id, self::$sourceLng, $question );
        self::$dictionary[$lowerQuestion] = ['', $id];
        return $question;
      }
    }
    
    if( $targetLng )
      $question = self::getDictionary( $targetLng, $question );
    else{
      if(
        $_SESSION['LNG'] == self::$sourceLng or 
        !isset( self::$dictionary[$lowerQuestion] ) or 
        self::$dictionary[$lowerQuestion][0] == ''
      ) return $question;
      
      $question = self::$dictionary[$lowerQuestion][0];
    }

    switch( $modification ){
      case 1: return mb_strtolower( $question, 'UTF-8' );
      case 2: return ucfirst( $question );
      case 3: return ucwords( $question );
      case 4: return mb_strtoupper( $question, 'UTF-8' );
      default: return $question;
    }
  }
}
