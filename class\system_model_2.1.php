<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *
 * @method `get_creative();`        Kreatív adatok beolvasása
 * @method `list_creative();`       Kreatívok adatainak beolvasása
 * @method `save_creativeStatus();` <PERSON>ott kreatív állapotának változtatása aktív 1/passzív 0
 * @method `get_regEmail();`        Reg adatok beolvasása email alapján
 * @method `save_reg();`            Megadott reg adatok mentése
 * @method `list_staff();`          Munkatársak adatainak beolvasása
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2017, Tánczos Róbert
 * 
 * @version 2.1.0
 * @since 2.1.0 2022.08.14 Creativ, Page, Element tábla kezelése get/list/save
 * @since 2.0.0 2022.05.05 Új verzió követés. Fájlnévben jelölve. Optimalizálás
 * @since 1.0.0 2017.01.15
 */

class model extends mysql{
  // === ahol van `signs` a táblában ott állítja az első jelzőt 0/1
  public static function save_Status( $id, $table, $database = 'shared' ){
    $sql = 'UPDATE '.$database.'.'.$table.' SET signs=CONCAT(IF(MID(signs,1,1)="1","0","1"),SUBSTRING(signs,2)) WHERE '.$table.'_id=? LIMIT 1';
    $res = self::query( $sql, [ $id ], 'i' );
    return $res;
  }
  // === theme
  public static function get_theme( $theme_id ){
    $sql = 'SELECT * FROM shared.theme WHERE theme_id=? LIMIT 1';
    $res = self::query( $sql, [ $theme_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function list_theme( $project_id = NULL, $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.theme';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' project_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY theme_id';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }

  // === creative
  public static function get_creative( $creative_id ){
    $sql = 'SELECT * FROM shared.creative WHERE creative_id=? LIMIT 1';
    $res = self::query( $sql, [ $creative_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function list_creative( $project_id = NULL, $partner_id = NULL,  $active = 0, $ot = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $partner_id = ( isset( $partner_id ) )? ( $partner_id )? $partner_id : $_SESSION['REGCEG'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.creative';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' project_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $partner_id ){
      $sql.= $wa.' partner_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $partner_id;
    }
    if( $active ){
      $sql.= $wa.' MID(signs, 1, 1)="1"';
      $wa = ' AND';
    }
    if( $ot ){
      $sql.= $wa.' MID(signs, 3, 1)=? AND MID(signs, 4, 1)=?';
      $type.= 'ss';
      $data[] = $ot[0];
      $data[] = $ot[1];
    }
    $sql.= ' ORDER BY project_id, partner_id, MID(signs,3,2)';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }
  public static function list_creativePG( $project_id, $partner_id, $page_group ){
    $project_id = ( $project_id )? $project_id : $_SESSION['PROID'];
    $partner_id = ( $partner_id )? $partner_id : $_SESSION['REGCEG'];
    $sql = 'SELECT * FROM shared.creative
            WHERE MID(signs , 1, 1)="1" AND project_id=? AND partner_id=? AND page_group=?
            ORDER BY sequence, creative_id';
    $res = self::query( $sql, [$project_id, $partner_id, $page_group], 'iii' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) )? $list : 0;
  }
  public static function save_creative( $creative, $creative_id = 0 ){
    $res = self::save( 'shared.creative', $creative, ( $creative_id )? ['creative_id', $creative_id, 'i'] : 0 );
    return $res;
  }
  public static function save_creativeStatus( $creative_id ){
    $sql = 'UPDATE shared.creative SET signs=CONCAT(IF(MID(signs,1,1)="1","0","1"),SUBSTRING(signs,2)) WHERE creative_id=? LIMIT 1';
    $res = self::query( $sql, [ $creative_id ], 'i' );
    return $res;
  }
  public static function del_creative( $creative_id ){
    $sql = 'DELETE FROM shared.creative WHERE creative_id=? LIMIT 1';
    $res = self::query( $sql, [ $creative_id ], 'i' )->get_result();
    return $res;
  }
  // === page
  public static function get_page( $page, $project_id = 0, $partner_id = 0 ){
    if( is_numeric( $page ) ){
      $sql = 'SELECT * FROM shared.page WHERE page_id=? LIMIT 1';
      $res = self::query( $sql, [ $page ], 'i' )->get_result()->fetch_assoc();
    }else{
      $project_id = ( $project_id )? $project_id : $_SESSION['PROID'];
      $partner_id = ( $partner_id )? $partner_id : $_SESSION['REGCEG'];
      $sql = 'SELECT * FROM shared.page WHERE project_id=? AND partner_id=? AND menu_link=? LIMIT 1';
      $res = self::query( $sql, [ $project_id, $partner_id, $page ], 'iis' )->get_result()->fetch_assoc();
    }
    return $res;
  }
  public static function list_page( $project_id = NULL, $partner_id = NULL,  $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $partner_id = ( isset( $partner_id ) )? ( $partner_id )? $partner_id : $_SESSION['REGCEG'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.page';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' project_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $partner_id ){
      $sql.= $wa.' partner_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $partner_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY project_id, partner_id, MID(signs,2,1)';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }
  public static function save_page( $page, $page_id = 0 ){
    $res = self::save( 'shared.page', $page, ( $page_id )? ['page_id', $page_id, 'i'] : 0 );
    return $res;
  }
  public static function save_pageStatus( $page_id ){
    $sql = 'UPDATE shared.page SET signs=CONCAT(IF(MID(signs,1,1)="1","0","1"),SUBSTRING(signs,2)) WHERE page_id=? LIMIT 1';
    $res = self::query( $sql, [ $page_id ], 'i' );
    return $res;
  }
  public static function del_page( $page_id ){
    $sql = 'DELETE FROM shared.page WHERE MID(signs, 2, 1)<>"F" AND page_id=? LIMIT 1';
    $res = self::query( $sql, [ $page_id ], 'i' )->get_result();
    return $res;
  }
  // === element
  public static function get_element( $element_id ){
    $sql = 'SELECT * FROM shared.element WHERE element_id=? LIMIT 1';
    $res = self::query( $sql, [ $element_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function list_element( $project_id = NULL, $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.element';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' (projects->"$[0]"=0 OR JSON_EXTRACT(projects, "$[*]")=?)';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY identifier, MID(signs, 3, 2), element_id';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }
  // === setting
  public static function get_setting( $setting_id ){
    $sql = 'SELECT * FROM shared.setting WHERE setting_id=? LIMIT 1';
    return self::query( $sql, [ $setting_id ], 'i' )->get_result()->fetch_assoc();
  }
  public static function get_settingIdentifier( $identifier ){
    $sql = 'SELECT * FROM shared.setting WHERE identifier=? LIMIT 1';
    return self::query( $sql, [ $identifier ] )->get_result()->fetch_assoc();
  }
  public static function list_setting( $project_id = NULL, $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.setting';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' (projects->"$[0]"=0 OR JSON_EXTRACT(projects, "$[*]")=?)';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY identifier, setting_id';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }
  // === size
  private static function sizeFormatList( $list ){
    foreach( $list as $key => $size )
      $list[$key] = self::sizeFormat( $size );
    return $list;
  }
  private static function sizeFormat( $size ){
    if( $size['name'][0] == '#' ){
      $rectangle = substr( $size['name'], 1, strpos( $size['name'], ' ' ) );
      $size['name'] = substr( $size['name'], strpos( $size['name'], ' ' ) );
      $size['size_format'] = $size['w'].'-'.$size['h'].'/'.$rectangle;
    }elseif( $size['w'] )
      $size['size_format'] = $size['w'].'x'.$size['h'];
    else
      $size['size_format'] = '';
    return $size;
  }
  public static function list_size(){
    $sql = 'SELECT * FROM shared.size ORDER BY name';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? self::sizeFormatList( $res ) : [];
    return ( count( $list ) )? $list : 0;
  }
  // === reg
  public static function get_reg( $reg_id ){
    $sql = 'SELECT * FROM adat.reg WHERE reg_id=? LIMIT 1';
    $res = self::query( $sql, [$reg_id], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function get_regEmail( $email ){
    $sql = 'SELECT * FROM adat.reg WHERE email=? LIMIT 1';
    $res = self::query( $sql, [$email] )->get_result()->fetch_assoc();
    return $res;
  }
  public static function save_reg( $data, $reg_id = 0 ){
    if( !$reg_id )
      $data = $data + [['ip', $_SERVER['REMOTE_ADDR']], ['utitkelt', date('Y-m-d H:i:s')]];
    return self::save( 'adat.reg', $data, ( $reg_id )? ['reg_id', $reg_id, 'i'] : 0 );
  }
  public static function list_reg(){
    $sql = 'SELECT * FROM adat.reg ORDER BY reg_id';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }
  public static function list_staff(){
    $sql = 'SELECT r.reg_id, r.neve, r.nevek, rm.jog, rm.utitkelt FROM adat.reg r
            INNER JOIN adat.regmodul rm ON r.reg_id=rm.reg_id
            WHERE rm.projekt_id='.$_SESSION['PROID'].' AND rm.ceg_id='.$_SESSION['REGCEG'].'
            AND MID(rm.jog, 1, 1)<>"" AND MID(rm.jog, 1, 1)<>"0" ORDER BY rm.jog, r.neve, r.nevek';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }
  // === regtax
  public static function get_regtax( $reg_id, $projekt_id, $ceg_id = 0 ){
    $sql = 'SELECT * FROM adat.regtax WHERE reg_id=? AND projekt_id=? AND ceg_id=? LIMIT 1';
    $res = self::query( $sql, [$reg_id, $projekt_id, $ceg_id], 'iii' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function save_regtax( $data, $regtax_id = 0 ){
    return self::save( 'adat.regtax', $data, ( $regtax_id )? ['regtax_id', $regtax_id, 'i'] : 0 );
  }
  // === pro_projekt
  public static function get_project( $project_id ){
    $sql = 'SELECT * FROM adat.pro_projekt WHERE projekt_id=? LIMIT 1';
    $res = self::query( $sql, [ $project_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  // === regceg
  public static function get_regceg( $ceg_id ){
    $sql = 'SELECT * FROM adat.regceg WHERE ceg_id=? LIMIT 1';
    $res = self::query( $sql, [ $ceg_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function save_regceg( $data, $ceg_id = 0 ){
    return self::save( 'adat.regceg', $data, ( $ceg_id )? ['ceg_id', $ceg_id, 'i'] : 0 );
  }

  public static function list_regmodulReg( $reg_id, $projekt_id ){
    $sql = 'SELECT * FROM adat.regmodul WHERE reg_id=? AND projekt_id=? ORDER BY ceg_id';
    $res = self::query( $sql, [$reg_id, $projekt_id], 'ii' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) )? $list : 0;
  }
  public static function get_regmodul( $data ){
    if( count( $data ) == 3 ){
      $sql = 'SELECT * FROM adat.regmodul WHERE reg_id=? AND projekt_id=? AND ceg_id=? LIMIT 1';
      $res = self::query( $sql, $data, 'iii' )->get_result()->fetch_assoc();
    }else{
      $sql = 'SELECT * FROM adat.regmodul WHERE reg_id=? AND projekt_id=? ORDER BY ceg_id LIMIT 1';
      $res = self::query( $sql, $data, 'ii' )->get_result()->fetch_assoc();
    }
    return $res;
  }
  public static function get_authModulDb( $data ){
    $sql = 'SELECT regmodul_id, jog, ceg_id FROM adat.regmodul WHERE reg_id=? AND projekt_id=? ORDER BY ceg_id';
    if( $res = self::query( $sql, $data, 'ii' )->get_result() ){
      $_SESSION['REGCEGDB'] = $res->num_rows;
      $rec = $res->fetch_assoc();
      $res->close();
      $res = $rec ?? 0;
    }
    return $res;
  }
  public static function get_regmodul1( $reg_id, $projekt_id = 0, $ceg_id = 0 ){
    $sql = 'SELECT * FROM adat.regmodul WHERE reg_id=?';
    $types = 'i';
    $data[] = $reg_id;
    if( $projekt_id ){
      $types.= 'i';
      $sql.=' AND projekt_id=?';
      $data[] = $projekt_id;
      if( $ceg_id ){
        $types.= 'i';
        $sql.=' AND ceg_id=?';
        $data[] = $ceg_id;
      }
    }
    $sql.= ' LIMIT 1';
    $res = self::query( $sql, $data, $types )->get_result()->fetch_assoc();
    return $res;
  }
  public static function save_regmodul( $data, $regmodul_id = 0 ){
    return self::save( 'adat.regmodul', $data, ( $regmodul_id )? ['regmodul_id', $regmodul_id, 'i'] : 0 );
  }
  public static function save_authModul( $data ){
    $sql = 'INSERT INTO adat.regmodul SET reg_id=?, projekt_id=?, utitkelt="'.date('Y-m-d H:i:s').'"';
    $res = self::query( $sql, $data, 'ii' );
    return $res;
  }
  public static function save_authModulUp( $regmodul_id ){
    $sql = 'UPDATE adat.regmodul SET utitkelt="'.date('Y-m-d H:i:s').'" WHERE regmodul_id=?';
    $res = self::query( $sql, [$regmodul_id], 'i' );
    return $res;
  }
  public static function save_authLog( $data ){
    $sql = 'INSERT INTO adat.reglog SET reg_id=?, projekt_id=?, ip="'.$_SERVER['REMOTE_ADDR'].'"';
    $res = self::query( $sql, $data, 'ii' );
    return $res;
  }
  public static function get_authCeg( $ceg_id ){
    $sql = 'SELECT * FROM adat.regceg WHERE ceg_id=? LIMIT 1';
    $res = self::query( $sql, [ $ceg_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function get_authApi( $apikey ){
    $sql = 'SELECT ceg_id,apierv,apiip,defjson FROM adat.regceg WHERE apikey=? LIMIT 1';
    if( $apikey != '' )
      $res = self::query( $sql, [ $apikey ] )->get_result()->fetch_assoc();
    else $res = false;
    return $res;
  }
  public static function get_authCegLink( $link ){
    if( substr( $link, 0, 4 ) == 'http')
      $sql = 'SELECT * FROM adat.regceg WHERE apidomain=? LIMIT 1';
    else
      $sql = 'SELECT * FROM adat.regceg WHERE clink=? LIMIT 1';
    $res = self::query( $sql, [ $link ] )->get_result()->fetch_assoc();
    return $res;
  }
  public static function list_regcegek( $cegidk ){
    $sql = 'SELECT * FROM adat.regceg WHERE ceg_id IN('.$cegidk.') ORDER BY ceg_id';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    if( $res )
      foreach( $res as $rec )
        $list[$rec['ceg_id']] = $rec['cneve'];
    return ( count( $list ) > 1 )? $list : 0;
  }

  public static function get_authRegTax( $reg_id ){
    $sql = 'SELECT * FROM adat.regtax WHERE reg_id=? LIMIT 1';
    $res = self::query( $sql, [$reg_id], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
}