<?php
switch( http::$route[1] ){
  case 'naptar':
  case 'foglalasoklist':
    switch( http::$route[2] ){
      case 'edit':
        require_once 'cont_modal_foglalas.php';
        require_once 'view_modal_foglalas.php';
      break;
      case 'stackoverflow':
        require_once 'cont_modal_foglalas_invoice.php';
        require_once 'view_modal_foglalas_invoice.php';
      break;
    }
  break;
  case 'foglalas':
    if( $_POST['btn_modositas'] ?? 0 ){
      $invoice_info = [
        'name' => $_POST['inv_name'],
        'countryCode' => $_POST['inv_countryCode'],
        'zip' => $_POST['inv_zip'],
        'city' => $_POST['inv_city'],
        'address' => $_POST['inv_address'],
        'taxNumber' => $_POST['inv_taxnumber'],
        'communityVatNumber' => $_POST['inv_communityVatNumber']    
      ];
      db::save_foglalas( [['eloleg', $_POST['eloleg'], 'i'],
                          ['fizetendo', $_POST['fizetendo'], 'i'],
                          ['vendegfo', $_POST['vendegfo'], 'i'],
                          ['email', $_POST['email']],
                          ['telefon', $_POST['telefon']],
                          ['kapcsolattarto', $_POST['kapcsolattarto']],
                          ['megjegyzes', $_POST['megjegyzes']],
                          ['invoice_info', json_encode( $invoice_info )]
                          ], $_POST['foglalas_id'] );
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'lemondas':
    if( $_POST['foglalas_id'] ?? 0 ?:0 ){
      db::save_foglalas( [['lemondva', date( 'Y-m-d H:i:s' )]], $_POST['foglalas_id'] );
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'foglalastorol':
    if( $_POST['foglalas_id'] ?? 0 ?:0 ){
      db::del_foglallakoegyseg( 0, $_POST['foglalas_id'] );
      db::del_foglalas( $_POST['foglalas_id'] );
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'foglalaslemod':
    if( $_POST['btn_modositas-le'] ?? 0 ){
      $napokfo = [];
      for( $i=0; $i < $_POST['ej']; $i++ ) $napokfo[] = intval( $_POST['vendegfo'] ?? 0 );
      db::save_foglallakoegyseg( [['erkezes', $_POST['erkezes']],
                                  ['napokfo', json_encode( $napokfo, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES )],
                                  ['lakoegyseg_id', $_POST['ujlakoegyseg_id']]
                                  ], intval( $_POST['foglallakoegyseg_id'] ));
    
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'plusszfoglalle':
    if( $_POST['foglalas_id'] ?? 0 ?:0 ){
      $foglalaslakoegyseg = db::get_foglalasMikor( $_POST['foglalas_id'] );
      db::save_foglallakoegyseg( [['foglalas_id', $foglalaslakoegyseg['foglalas_id'], 'i'],
                                  ['erkezes', $foglalaslakoegyseg['erkezes']],
                                  ['napokfo', $foglalaslakoegyseg['napokfo']],
                                  ['lakoegyseg_id', $_POST['ujlakoegyseg_id'], 'i']] );
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'foglalastorolle':
    db::del_foglallakoegyseg( $_POST['foglallakoegyseg_id'] );
    if( !db::get_foglalasMikor( $_POST['foglalas_id'] ))
      db::del_foglalas( $_POST['foglalas_id'] );
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  case 'foglalasfizet':
    if(( $_POST['btn_fizet'] ?? 0) and ($_POST['osszeg_huf'] ?? 0) and $_POST['osszeg_huf'] != '0' and $_POST['mikor'] ?? 0 and $_POST['mikor'] != '' ){
      $_POST['osszeg_mas'] = $_POST['osszeg_mas'] ?? 0;
      $_POST['osszeg_mas'] = ($_POST['osszeg_mas'] == '')? 0 : $_POST['osszeg_mas'];
      db::save( DATABASENAME.'.penz', [['ceg_id', $_SESSION['COMPANY'], 'i'],
                                        ['reg_id', $_SESSION['USER'], 'i'],
                                        ['foglalas_id', $_POST['foglalas_id'], 'i'],
                                        ['mikor', $_POST['mikor']],
                                        ['egyeb', $_POST['egyeb'] ?? ''],
                                        ['osszeg_huf', $_POST['osszeg_huf'], 'i'],
                                        ['osszeg_mas', $_POST['osszeg_mas']],
                                        ['penznem', $_POST['penznem'] ?? '']] );
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'foglalasfizettorol':
    db::del_fizetve( $_POST['penz_id'] );
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  case 'naptarcreate':
  case 'foglalasoklistcreate':
    require_once 'cont_modal_foglalas_create.php';
    require_once 'view_modal_foglalas_create.php';
  break;
  case 'foglalascreate':
    if( $_POST['btn_mentes'] ?? 0 ){
      $napokfo = [];
      $difnap = round(( strtotime( $_POST['nap2'] ) - strtotime( $_POST['nap1'] )) / 24 / 60 / 60 );
        for( $i=0; $i<$difnap; $i++ ) $napokfo[] = $_POST['fo'] ?? 0;
      $foglalas_id = db::save_foglalas(
                      [ ['ceg_id', $_SESSION['COMPANY'], 'i'],
                        ['reg_id', $_SESSION['USER'], 'i'],
                        ['csatorna_id', $_POST['csatorna'] ?? 0, 'i'],
                        ['foglalva', date( 'Y-m-d H:i:s' )],
                        ['eloleg', $_POST['eloleg'] ?? 0, 'i'],
                        ['fizetendo', $_POST['fizetendo'] ?? 0, 'i'],
                        ['vendegfo', $_POST['vendegfo'] ?? 0, 'i'],
                        ['email', $_POST['email'] ?? ''],
                        ['telefon', $_POST['telefon'] ?? ''],
                        ['kapcsolattarto', $_POST['kapcsolattarto'] ?? ''],
                        ['megjegyzes', $_POST['megjegyzes'] ?? '']
                      ] );
      if( $_POST['lakoegysegek'] ?? 0 )
      foreach( $_POST['lakoegysegek'] as $lakoegyseg_id ){
        db::save_foglallakoegysegFoglal(
            [ ['foglalas_id', $foglalas_id, 'i'],
              ['lakoegyseg_id', $lakoegyseg_id, 'i'],
              ['erkezes', $_POST['nap1']],
              ['napokfo', json_encode( $napokfo, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES )]
            ] );
      }
      
      $res = [ 'ok' => true, 'id' => $foglalas_id ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'naptarblockedadd': // Új zárolás dialógus megnyitása
    require_once 'cont_modal_blocked_create.php';
    require_once 'view_modal_blocked_create.php';
  break;
  case 'naptar-blocked_callback': // Zárolás létrehozása
    if( $_POST['btn_mentes'] ?? 0 ){
      $difnap = round(( strtotime( $_POST['nap2'] ) - strtotime( $_POST['nap1'] )) / 24 / 60 / 60 );
      if( $_POST['lakoegysegek'] ?? 0 )
        foreach( $_POST['lakoegysegek'] as $lakoegyseg_id ){
          $days = db::get_reserved_days( $_POST['nap1'], $_POST['nap2'], $lakoegyseg_id );
          $start_day = $_POST['nap1'];
          $napokfo = [];
          foreach( $days as $day ){
            if( !$day )
              $napokfo[] = 0;
            elseif( count( $napokfo )){
              db::save_blocked( $lakoegyseg_id, $start_day, $napokfo );
              $start_day = date( 'Y-m-d', strtotime( $start_day.' +'.( count( $napokfo ) + 1 ).' day' ));
              $napokfo = [];
            }else $start_day = date( 'Y-m-d', strtotime( $start_day.' +1 day' ));
          }
          if( count( $napokfo ))
            db::save_blocked( $lakoegyseg_id, $start_day, $napokfo );
        }
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'naptarblocked': // Zárolás dialógus megnyitása
    require_once 'cont_modal_blocked.php';
    require_once 'view_modal_blocked.php';
  break;
  case 'foglalasinvoice':
    require_once 'cont_modal_invoice.php';
    require_once 'view_modal_invoice.php';
  break;

  case 'accommodation_unit_types':
    require_once 'cont_modal_accommodationunittype.php';
    require_once 'view_modal_accommodationunittype.php';
  break;
  case 'accommodation_unit_type_save':
    if( $_POST['btn_modositas'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      db::save(
       DATABASENAME.'.accommodation_unit_types',
        [ ['name', $_POST['name']],
          ['places', $_POST['places']]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'accommodation_unit_typescreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.accommodation_unit_types',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['name', $_POST['name']]
        ]
      );

      $res = [ 'ok' => $id, 'link' => 'accommodationunittypes' ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'accommodation_unit_types_del':
    db::del( DATABASENAME.'.accommodation_unit_types', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'accommodation_units':
    switch( http::$route[2] ){
      case 'edit':
        require_once 'cont_modal_accommodationunit.php';
        require_once 'view_modal_accommodationunit.php';
      break;
      case 'picture':
        require_once 'cont_modal_lakoegysegkep.php';
        require_once 'view_modal_lakoegysegkep.php';
      break;
      case 'exchange':
        require_once 'cont_modal_booking_sync.php';
        require_once 'view_modal_booking_sync.php';
      break;
    }
  break;
  case 'accommodation_unit_save':
    if( $_POST['btn_modositas'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $_POST['building'] = str_replace( "'", "´", $_POST['building'] );
      $_POST['brief'] = str_replace( "'", "´", $_POST['brief'] );
      $_POST['description'] = str_replace( "'", "´", $_POST['description'] );
      db::save(
        DATABASENAME.'.accommodation_units',
        [ ['signs', $_POST['status'], 'i'],
          ['area', $_POST['area'] ?? 0, 'i'],
          ['single_beds_number', $_POST['single_beds_number'] ?? 0],
          ['double_beds_number', $_POST['double_beds_number'] ?? 0],
          ['extra_beds_number', $_POST['extra_beds_number'] ?? 0],
          ['sort_by', $_POST['sort_by']],
          ['ntak_type', $_POST['ntak_type']],
          ['building', $_POST['building']],
          ['name', $_POST['name']],
          ['brief', $_POST['brief']],
          ['description', $_POST['description']]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'accommodation_unitscreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.accommodation_units',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['name', $_POST['name']],
          ['accommodation_unit_type_id', $_POST['accommodation_unit_type_id'] ?? 0, 'i']         
        ]
      );

      $res = [ 'ok' => $id, 'link' => 'accommodationunits/'.$_POST['accommodation_unit_type_id'] ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'accommodation_units_del':
    if( db::get_piece( DATABASENAME.'.foglallakoegyseg', 'lakoegyseg_id='.$_POST['id'] ))
      $res = [ 'ok' => false ];
    else{
      db::del( DATABASENAME.'.szinkronnaptar', ' lakoegyseg_id='.$_POST['id'] );
      db::del( DATABASENAME.'.accommodation_units', 'id='.$_POST['id'] );
      $res = [ 'ok' => true ];
    }

    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  
  case 'szinkronnaptarcreate':
    db::save( DATABASENAME.'.szinkronnaptar', [
      ['lakoegyseg_id', $_POST['lakoegyseg_id'], 'i'],                                         
      ['csatorna_id', $_POST['csatorna_id'], 'i']
    ] );
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  case 'szinkronnaptarsave':
    db::save( DATABASENAME.'.szinkronnaptar',
      [['link', $_POST['link']]], ['szinkronnaptar_id', $_POST['szinkron_id'], 'i']
    );
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  case 'szinkronnaptardel':
    db::del( DATABASENAME.'.szinkronnaptar', 'szinkronnaptar_id='.$_POST['szinkron_id'].' AND lakoegyseg_id='.$_POST['lakoegyseg_id'] );
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'csatornalist':
    switch( http::$route[2] ){
      case 'edit':
        require_once 'cont_modal_foglalasicsatorna.php';
        require_once 'view_modal_foglalasicsatorna.php';
      break;
    }
  break;
  case 'csatorna':
    if( $_POST['btn_modositas'] ?? 0 ){
      $signs = $_POST['allapot'].$_POST['ical'].$_POST['chanelmanager'];
      db::save_csatorna( [['ceg_id', $_SESSION['COMPANY'], 'i'],
                          ['signs', $signs],
                          ['foglalasicsatorna', $_POST['foglalasicsatorna']],
                          ['megnevezes', $_POST['megnevezes']],
                          ['leiras', $_POST['leiras']]
                          ], $_POST['csatorna_id'] );
    
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'csatornacreate':
    $csatorna_id = db::save_csatorna( [['ceg_id', $_SESSION['COMPANY'], 'i']] );

    $res = [ 'ok' => $csatorna_id ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  case 'csatornadel':
    if( db::get_piece( DATABASENAME.'.foglalas', 'csatorna_id='.$_POST['csatorna_id'] ) or
        db::get_piece( DATABASENAME.'.szinkronnaptar', 'csatorna_id='.$_POST['csatorna_id'] ))
      $res = [ 'ok' => false ];
    else{
      db::del( DATABASENAME.'.csatorna', ' csatorna_id='.$_POST['csatorna_id'] );
      $res = [ 'ok' => true ];
    }

    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'messagetemplateslist':
    require_once 'cont_modal_messagetemplates.php';
    require_once 'view_modal_messagetemplates.php';
  break;
  case 'messagetemplate':
    if( $_POST['btn_modositas'] ?? 0 ){
      $signs = $_POST['status'];
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $_POST['subject'] = str_replace( "'", "´", $_POST['subject'] );
      $_POST['body'] = str_replace( "'", "´", $_POST['body'] );
      db::save(
       DATABASENAME.'.message_templates',
        [ ['signs', $signs],
          ['name', $_POST['name']],
          ['subject', $_POST['subject'] ?? null],
          ['body', $_POST['body'] ?? null]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'messagetemplateslistcreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.message_templates',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['signs', '1'],
          ['name', $_POST['name']]
        ]         
      );

      $res = [ 'ok' => $id, 'link' => 'message/templates' ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'messagetemplatedel':
    db::del( DATABASENAME.'.message_templates', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'services':
    require_once 'cont_modal_service.php';
    require_once 'view_modal_service.php';
  break;
  case 'service':
    if( $_POST ?? 0 ){
      if( $_FILES['myFile'] ?? 0 and $_FILES['myFile']['name'] != '' ){
        $file_type = explode( '.', $_FILES['myFile']['name'] );
        $file_type[1] = strtolower( $file_type[1] );
        if( 'jpg' == $file_type[1] or 'jpeg' == $file_type[1] ){
          if( $_POST['image_id'] ?? 0 )
            $pic = intval( $_POST['image_id'] );
          else{
            $pic = 1;
            while( file_exists( 'upload/'.$_SESSION['COMPANY'].'/pic'.sprintf( "%04d", $pic ).'.jpg' ))
              $pic++;
          }
          $kep = 'upload/'.$_SESSION['COMPANY'].'/pic'.sprintf( "%04d", $pic ).'.jpg';
          move_uploaded_file( $_FILES['myFile']['name'], $kep );
          $_POST['image_id'] = $pic;
        }
      }
      $signs = $_POST['status'].$_POST['context_night'].$_POST['context_guest'];
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $_POST['description'] = str_replace( "'", "´", $_POST['description'] );
      db::save(
       DATABASENAME.'.services',
        [ ['signs', $signs],
          ['name', $_POST['name']],
          ['link', $_POST['link'] ?? null],
          ['unit', $_POST['unit'] ?? null],
          ['image_id', $_POST['image_id']],
          ['sequence', $_POST['sequence'] ?? 0],
          ['price', $_POST['price'] ?? 0],
          ['selects', $_POST['selects'] ?? null],
          ['description', $_POST['description'] ?? null]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'servicescreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.services',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['signs', '100'],
          ['name', $_POST['name']]
        ]         
      );

      $res = [ 'ok' => $id ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'servicedel':
    db::del( DATABASENAME.'.services', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'reviews':
    require_once 'cont_modal_reviews_rating.php';
    require_once 'view_modal_reviews_rating.php';
  break;
  case 'review':
    if( $_POST['btn_modositas'] ?? 0 ){
      $signs = $_POST['status'];
      $_POST['author_name'] = str_replace( "'", "´", $_POST['author_name'] );
      $_POST['author_city'] = str_replace( "'", "´", $_POST['author_city'] );
      $_POST['title'] = str_replace( "'", "´", $_POST['title'] );
      $_POST['review'] = str_replace( "'", "´", $_POST['review'] );
      $_POST['negative_review'] = str_replace( "'", "´", $_POST['negative_review'] );
      $_POST['answer'] = str_replace( "'", "´", $_POST['answer'] );
      db::save(
        DATABASENAME.'.reviews',
        [
          ['provider_id', $_POST['provider_id'] ?? 1, 'i'],
          ['trip_purpose_id', $_POST['trip_purpose_id'] ?? 0, 'i'],
          ['location', $_POST['location'] ?? 0, 'i'],
          ['communication', $_POST['communication'] ?? 0, 'i'],
          ['cleanliness', $_POST['cleanliness'] ?? 0, 'i'],
          ['value', $_POST['value'] ?? 0, 'i'],
          ['checkin', $_POST['checkin'] ?? 0, 'i'],
          ['accuracy', $_POST['accuracy'] ?? 0, 'i'],
          ['services', $_POST['services'] ?? 0, 'i'],
          ['comfort', $_POST['comfort'] ?? 0, 'i'],
          ['rooms', $_POST['rooms'] ?? 0, 'i'],
          ['wifi', $_POST['wifi'] ?? 0, 'i'],
          ['author_name', $_POST['author_name'] ?? null],
          ['author_country', $_POST['author_country']],
          ['author_city', $_POST['author_city']],
          ['residency_days', $_POST['residency_days']],
          ['when_was', $_POST['when_was']],
          ['title', $_POST['title'] ?? null],
          ['review', $_POST['review'] ?? null],
          ['negative_review', $_POST['negative_review'] ?? null],
          ['answer', $_POST['answer'] ?? null],
          ['signs', $signs]
        ],
        intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'reviewscreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.reviews',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['signs', '1'],
          ['author_name', $_POST['name']]
        ]
      );

      $res = [ 'ok' => $id, 'link' => 'reviews/ratings' ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'reviewdel':
    db::del( DATABASENAME.'.reviews', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'coupons':
    require_once 'cont_modal_coupon.php';
    require_once 'view_modal_coupon.php';
  break;
  case 'coupon':
    if( $_POST['btn_modositas'] ?? 0 ){
      $signs = $_POST['status'].$_POST['fixed'].$_POST['multiple'];
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      db::save(
       DATABASENAME.'.coupons',
        [ ['signs', $signs],
          ['name', $_POST['name']],
          ['code', $_POST['code'] ?? null],
          ['validity_start', $_POST['validity_start'] ?? null],
          ['validity_end', $_POST['validity_end'] ?? null],
          ['booking_period_start', $_POST['booking_period_start'] ?? null],
          ['booking_period_end', $_POST['booking_period_end'] ?? null],
          ['weekly_start_day', $_POST['weekly_start_day'] ?? 0],
          ['weekly_stop_day', $_POST['weekly_stop_day'] ?? 0],
          ['min_booking_days', $_POST['min_booking_days'] ?? 1],
          ['discount', $_POST['discount'] ?? 0],
          ['description', $_POST['description'] ?? null]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'couponscreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.coupons',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['signs', '100'],
          ['name', $_POST['name']]
        ]         
      );

      $res = [ 'ok' => $id ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'coupondel':
    db::del( DATABASENAME.'.coupons', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'popups':
    require_once 'cont_modal_popup.php';
    require_once 'view_modal_popup.php';
  break;
  case 'popup':
    if( $_POST['btn_modositas'] ?? 0 ){
      $signs = $_POST['status'];
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $_POST['content'] = str_replace( "'", "´", $_POST['content'] );
      db::save(
       DATABASENAME.'.popups',
        [ ['signs', $signs],
          ['name', $_POST['name']],
          ['link', $_POST['link']],
          ['link_name', $_POST['link_name']],
          ['validity_start', $_POST['validity_start'] ?? null],
          ['validity_end', $_POST['validity_end'] ?? null],
          ['content', $_POST['content'] ?? null]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'popupscreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.popups',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['signs', '1'],
          ['name', $_POST['name']]
        ]
      );

      $res = [ 'ok' => $id ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'popupdel':
    db::del( DATABASENAME.'.popups', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'periods':
    require_once 'cont_modal_pricingperiod.php';
    require_once 'view_modal_pricingperiod.php';
  break;
  case 'period':
    if( $_POST['btn_modositas'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      db::save(
       DATABASENAME.'.periods',
        [ ['name', $_POST['name']],
          ['periods', $_POST['periodDays']],
          ['priority', $_POST['priority'] ?? 1],
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'periodscreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.periods',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['priority', '1'],
          ['name', $_POST['name']]
        ]
      );

      $res = [ 'ok' => $id, 'link' => 'pricing/periods' ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'perioddel':
    db::del( DATABASENAME.'.periods', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'accommodation_unit_type_prices':
    require_once 'cont_modal_pricingprice.php';
    require_once 'view_modal_pricingprice.php';
  break;
  case 'pricescreatemodal':
    require_once 'cont_modal_pricingpricecreate.php';
    require_once 'view_modal_pricingpricecreate.php';
  break;
  case 'price':
    if( $_POST['btn_modositas'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      db::save(
        DATABASENAME.'.accommodation_unit_type_prices',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['name', $_POST['name']],
          ['accommodation_unit_type_id', $_POST['accommodation_unit_type_id']],
          ['period_id', $_POST['period_id']],
          ['weekly_priced_days', $_POST['weekly_priced_days']],
          ['weekly_arrival_days', $_POST['weekly_arrival_days']],
          ['locked_nights', $_POST['locked_nights']],
          ['min_booking_period', $_POST['min_booking_period']],
          ['min_days', $_POST['min_days']],
          ['max_days', $_POST['max_days']],
          ['prices', $_POST['prices']]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'pricecreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.accommodation_unit_type_prices',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['name', $_POST['name']],
          ['accommodation_unit_type_id', $_POST['accommodation_unit_type_id']],
          ['period_id', $_POST['period_id']]
        ]
      );

      $res = [ 'ok' => $id ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'pricedel':
    db::del( DATABASENAME.'.accommodation_unit_type_prices', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'galleriescreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.galleries',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['signs', '1'],
          ['name', $_POST['name']]
        ]         
      );

      $res = [ 'ok' => $id, 'link' => 'website/galleries' ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'galleries':
    require_once 'cont_modal_websitegallery.php';
    require_once 'view_modal_websitegallery.php';
  break;
  case 'gallery':
    if( $_POST['btn_modositas'] ?? 0 ){
      $signs = $_POST['status'];
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $_POST['description'] = str_replace( "'", "´", $_POST['description'] );
      db::save(
       DATABASENAME.'.galleries',
        [ ['signs', $signs],
          ['name', $_POST['name']],
          ['image_sequence', $_POST['sequence']],
          ['description', $_POST['description'] ?? null]
        ], intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'gallerydel':
    db::del( DATABASENAME.'.galleries', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'messagescreate':
    if( $_POST['name'] ?? 0 ){
      $_POST['name'] = str_replace( "'", "´", $_POST['name'] );
      $id = db::save(
        DATABASENAME.'.messages',
        [ 
          ['company_id', $_SESSION['COMPANY'], 'i'],
          ['name', $_POST['name']]
        ]         
      );

      $res = [ 'ok' => $id, 'link' => 'message/messages' ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'messages':
    require_once 'cont_modal_message.php';
    require_once 'view_modal_message.php';
  break;
  case 'message':
    if( $_POST['btn_modositas'] ?? 0 ){
      $_POST['subject'] = str_replace( "'", "´", $_POST['subject'] );
      $_POST['body'] = str_replace( "'", "´", $_POST['body'] );
      db::save(
       DATABASENAME.'.messages',
        [
          ['booking_id', $_POST['booking_id']],
          ['template_id', $_POST['template_id']],
          ['device', $_POST['device'] ?? 3],
          ['type', "1"],
          ['sender', $_POST['sender']],
          ['fromto', $_POST['to']],
          ['name', $_POST['subject'] ?? null],
          ['body', $_POST['body'] ?? null]
        ],
        intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'messagedel':
    db::del( DATABASENAME.'.messages', intval( $_POST['id'] ));
    $res = [ 'ok' => true ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  case 'messagesend':
    if( $_POST ?? 0 ){
      if( $message = db::get( DATABASENAME.'.messages', intval( $_POST['id'] ))){
        $res = mailer::speedsend(
          '(#TM'.$message['id'].') '.$message['name'],
          $message['body'],
          $message['fromto'],
          ( $message['type'] )? SENDER_EMAIL : $message['sender'],
          ( $message['type'] )? $message['sender'] : null,
          ( $message['embeds'] ?? 0 )? json_decode( $message['embeds'], true ) : null          
        );
        if( gettype( $res ) == 'boolean' )
          db::save( DATABASENAME.'.messages', [['sending_time', date( 'Y-m-d H:i:s' )]], intval( $_POST['id'] ));
        $res = [ 'ok' => true ];
      }else $res = [ 'ok' => false ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'messageeditbooking':
    if( $_POST ?? 0 ){
      if( $booking = db::get( DATABASENAME.'.foglalas', 'foglalas_id='.intval( $_POST['booking_id'] ))){
        $res = ['ok' => true, 'data' => ['booking' => $booking]];
        if( $_POST['template_id'] )
          if( $template = db::get( DATABASENAME.'.message_templates', intval( $_POST['template_id'] ))){
            require_once 'def_message.php';
            $subject = messageReplaceVariables( $template['subject'], intval( $_POST['booking_id'] ));
            $body = messageReplaceVariables( $template['body'], intval( $_POST['booking_id'] ));
            $res['data']['subject'] = $subject;
            $res['data']['body'] = $body;
          } 
      }else $res = ['ok' => false];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'messageedittemplate':
    if( $_POST ?? 0 ){
      if( $template = db::get( DATABASENAME.'.message_templates', intval( $_POST['template_id'] ))){
        require_once 'def_message.php';
        $subject = messageReplaceVariables( $template['subject'], intval( $_POST['booking_id'] ));
        $body = messageReplaceVariables( $template['body'], intval( $_POST['booking_id'] ));
        $res = ['ok' => true, 'data' => ['subject' => $subject, 'body' => $body]];
      }else $res = ['ok' => false];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;

  case 'invoices':
    require_once 'cont_modal_invoice.php';
    require_once 'view_modal_invoice.php';
  break;
  case 'invoice':
    if( $_POST['btn_modositas'] ?? 0 ){
      db::save(
       DATABASENAME.'.invoices',
        [
          ['booking_id', $_POST['booking_id']],
          ['payday', $_POST['payday'] ?? null]
        ],
        intval( $_POST['id'] )
      );
                          
      $res = [ 'ok' => true ];
      header( 'Content-Type: application/json' );
      header( 'HTTP/1.1 200 OK' );
      echo json_encode( $res );
      exit;
    }
  break;
  case 'invoicesnavimport':
    $_POST['year'] ??= date( 'Y' );
    $_POST['month'] ??= date( 'm' );
    $dateFrom = $_POST['year'].'-'.$_POST['month'].'-01';
    $dateTo = $_POST['year'].'-'.$_POST['month'].'-'.date( 't', strtotime( $dateFrom )); 
    $res = [ 'ok' => true ];

    try{
      include 'config_nav.php';
      $navconfig = new NavOnlineInvoice\Config( $apiUrl, $userData, $softwareData );
      $reporter = new NavOnlineInvoice\Reporter( $navconfig );
      $invoiceQueryParams = [
        "mandatoryQueryParams" => [
          "invoiceIssueDate" => [
            "dateFrom" => $dateFrom,
            "dateTo" => $dateTo,
          ],
        ]
      ];
      $page = 1;

      $invoiceDigestResult = $reporter->queryInvoiceDigest( $invoiceQueryParams, $page, "OUTBOUND" );
      if( isset( $invoiceDigestResult->invoiceDigest ) and !empty( $invoiceDigestResult->invoiceDigest ))
        foreach( $invoiceDigestResult->invoiceDigest as $invoice ){
          if( db::get_invoice( 'invoice_number="'. ( string ) $invoice->invoiceNumber .'"' )) continue;

          $invoiceDataResult = $reporter->queryInvoiceData( [
            'invoiceNumber' => ( string ) $invoice->invoiceNumber,
            'invoiceDirection' => 'OUTBOUND'
          ], true );

          $invoiceData = [];
          
          $invoiceData[] = ['company_id', $_SESSION['COMPANY'], 'i'];

          $documentumStatus = match(( string )$invoice->invoiceOperation ){
            'STORNO' => 5,
            'MODIFY' => 6,
            default => 2
          };
          $documentumType = match(( string )$invoice->invoiceOperation ){
            'STORNO' => 4,
            'MODIFY' => 5,
            default => 3
          };
          if( $documentumType == 3 )
            foreach( $invoiceDataResult->invoiceMain->invoice->invoiceLines as $line )
              if(( string )$line->line->advanceData->advanceIndicator === 'true' and !isset( $line->line->advanceData->advancePaymentData )){
                $documentumType = 2;
                break;
              }

          $documentumNavStatus = match(( string )$invoice->invoiceOperation ){
            'STORNO' => 5,
            'MODIFY' => 6,
            default => 3
          };
          if( $documentumNavStatus == 3 )
            foreach( $invoiceDataResult->invoiceMain->invoice->invoiceLines as $line )
              if(( string )$line->line->advanceData->advanceIndicator === 'true' and !isset( $line->line->advanceData->advancePaymentData )){
                $documentumNavStatus = 4;
                break;
              }

          $invoiceData[] = ['signs', $documentumStatus.$documentumType.$documentumNavStatus.'0'];

          $invoiceData[] = [
            'payment_method',
            match(( string )$invoice->paymentMethod ){
              'CASH' => 1,
              'CARD' => 2,
              'TRANSFER' => 3,
              default => 4
            },
            'i'
          ];

          $invoiceData[] = ['payment_date', ( string )$invoice->paymentDate];
          $invoiceData[] = ['execution_date', ( string )$invoice->invoiceDeliveryDate];
          $invoiceData[] = ['currency', ( string )$invoice->currency];
          $invoiceData[] = ['exchange_rate', ( string )$invoiceDataResult->invoiceMain->invoice->invoiceHead->invoiceDetail->exchangeRate ?? 1, 'i'];
          $invoiceData[] = ['invoice_number', ( string ) $invoice->invoiceNumber];
          $invoiceData[] = ['number', ( integer ) substr( ( string )$invoice->invoiceNumber, 8 ), 'i'];
          $invoiceData[] = ['transaction', ( string )$invoice->transactionId];
          $invoiceData[] = ['created', ( string )$invoice->invoiceIssueDate];
          
          $invoiceData[] = [
            'customer',
            json_encode( [
              'name' => ( string )$invoice->customerName,
              'taxNumber' => ( string )$invoice->customerTaxNumber
            ] )
          ];

          $invoiceData[] = [
            'supplier',
            json_encode( [
              'name' => ( string )$invoice->supplierName,
              'taxNumber' => ( string )$invoice->supplierTaxNumber
            ] )
          ];

          $invoice_id = db::save_invoice( $invoiceData );

          // Debug: teljes invoiceLines struktúra
          user_error("DEBUG: invoiceLines count = " . count($invoiceDataResult->invoiceMain->invoice->invoiceLines));
          user_error("DEBUG: invoiceLines->line count = " . count($invoiceDataResult->invoiceMain->invoice->invoiceLines->line));
          user_error("DEBUG: invoiceLines XML = " . $invoiceDataResult->invoiceMain->invoice->invoiceLines->asXML());

          $lineCount = 0;
          foreach( $invoiceDataResult->invoiceMain->invoice->invoiceLines->line as $line ){
            $lineCount++;
            user_error("DEBUG: Feldolgozás - sor #$lineCount");

            $taxRateId = 0;
            if( $taxRate = db::get_taxCate(( string )$line->line->lineAmountsNormal->lineVatRate->vatExemption->case )) $taxRateId = $taxRate['id'];
            $invoiceItemData = [];
            $invoiceItemData[] = ['invoice_id', $invoice_id, 'i'];
            $invoiceItemData[] = ['tax_rate_id', $taxRateId, 'i'];
            $invoiceItemData[] = ['quantity', ( string )$line->line->quantity, 'i'];
            $invoiceItemData[] = ['unit', ( $line->line->unitOfMeasure == 'OWN' )? $line->line->unitOfMeasureOwn : $line->line->unitOfMeasure];
            $invoiceItemData[] = ['price_net', ( string )$line->line->unitPrice];
            $invoiceItemData[] = ['name', ( string )$line->line->lineDescription];
            $invoiceItemData[] = ['created', ( string )$invoice->invoiceIssueDate];
            if(( string )$line->line->advanceData->advanceIndicator === 'true' and ( $line->line->advanceData->advancePaymentData ?? 0 )){
              $originalInvoice = db::get_invoice( 'invoice_number="'.( string )$line->line->advanceData->advancePaymentData->advanceOriginalInvoice .'"' );
              $invoiceItemData[] = ['original_invoice_id', $originalInvoice['id'], 'i'];
            }

            user_error("DEBUG: Mentés előtt - sor #$lineCount, invoice_id: $invoice_id");
            $savedId = db::save_invoiceItem( $invoiceItemData );
            user_error("DEBUG: Mentés után - sor #$lineCount, visszakapott ID: $savedId");
          }
          user_error("DEBUG: Összesen $lineCount sor feldolgozva");
        }
    }catch( Exception $ex ){
      $res['error'] = 'Hiba: '. get_class( $ex ). ": " . $ex->getMessage();
      user_error( $res['error'] );
    }
    
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
  case 'invoicepdf':
    if( $_POST['invoice_id'] ?? 0 ){
      ob_start();
      if( $invoice = db::get_invoice( intval( $_POST['invoice_id'] ))){
        $header = pdf::invoiceHeader( $invoice );
        $footer = pdf::invoiceFooter();
        $html = pdf::invoiceSupplierAndCustomer( $invoice );
        $html.=
          '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt">' .
          '  <tr>'.
          '    <td width="20%" align="center"><b>Pénznem</b><br>HUF</td>'.
          '    <td width="20%" align="center"><b>Fizetési mód</b><br>'.['','készpénz', 'bankkártya', 'átutalás', 'egyéb'][$invoice['payment_method']].'</td>'.
          '    <td width="20%" align="center"><b>Teljesítés</b><br>'.$invoice['execution_date'].'</td>'.
          '    <td width="20%" align="center"><b>Keltezés</b><br>'.$invoice['issue_date'].'</td>'.
          '    <td width="20%" align="center"><b>Fizetési határidő</b><br>'.$invoice['payment_date'].'</td>'.
          '  </tr>'.
          '  <tr><td colspan="5" style="border-top: 1px solid #000">&nbsp;</td></tr>'.
          '</table>';

        $html.=
          '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt">'.
          '  <tr>'.
          '    <td width="40%" style="border-bottom: 1px solid #000"><b>Tétel neve</b></td>'.
          '    <td width="10%" align="right" style="border-bottom: 1px solid #000"><b>Mennyiség</b></td>'.
          '    <td width="10%" align="right" style="border-bottom: 1px solid #000"><b>Nettó egységár</b></td>'.
          '    <td width="10%" align="right" style="border-bottom: 1px solid #000"><b>Nettó érték</b></td>'.
          '    <td width="10%" align="center" style="border-bottom: 1px solid #000"><b>Áfakulcs</b></td>'.
          '    <td width="10%" align="right" style="border-bottom: 1px solid #000"><b>Áfa érték</b></td>'.
          '    <td width="10%" align="right" style="border-bottom: 1px solid #000"><b>Bruttó érték</b></td>'.
          '  </tr>';
        
        $sum_net = [];
        $sum_tax = [];
        $tax_rate = 0;
        $tax_name = '';
        foreach( $invoice['items'] as $item ){
          $tax_rate = $item['tax_rate'];
          $tax_name = $item['tax_name'];
          $item['price_net'] = intval( $item['price_net'] );
          $item_net = ( $item['quantity'] )? $item['quantity'] * $item['price_net'] : $item['price_net'];
          $html.=
            '  <tr>' .
            '    <td>'. $item['name'].'</td>'.
            '    <td align="right">'.(( $item['quantity'] )? $item['quantity'].' éj' : '').'</td>'.
            '    <td align="right">'.(( $item['quantity'] )? number_format( $item['price_net'], 0, '.', ' ' ) : '' ).'</td>'.
            '    <td align="right">'.number_format( $item_net, 0, '.', ' ' ).'</td>'.
            '    <td align="center">'.$tax_name.'</td>'.
            '    <td align="right">'.number_format( $item_net * ( $item['tax_rate'] - 1 ), 0, '.', ' ' ).'</td>'.
            '    <td align="right">'.number_format( $item_net * $item['tax_rate'], 0, '.', ' ' ).'</td>'.
            '  </tr>';
          if( !( $sum_net[$tax_name] ?? 0 )) $sum_net[$tax_name] = 0;
          if( !( $sum_tax[$tax_name] ?? 0 )) $sum_tax[$tax_name] = 0;
          $sum_net[$tax_name] += $item_net;
          $sum_tax[$tax_name] += $item_net * ( $item['tax_rate'] - 1 );
        }
        $html.= '</table>';
        
        $html.=
          '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt">'.
          '  <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>'.
          '  <tr>'.
          '    <td width="50%">&nbsp;</td>'.
          '    <td width="30%">Számla nettó értéke:</td>'.
          '    <td width="20%" align="right">'.http::moneyFormat( $sum_net[$tax_name], 'HUF' ).'</td>'.
          '  </tr>'.
          '  <tr>'.
          '    <td>&nbsp;</td>'.
          '    <td>ÁFA kulcs és értéke '.$tax_name.':</td>'.
          '    <td align="right">'.http::moneyFormat( $sum_tax[$tax_name], 'HUF' ).'</td>'.
          '  </tr>'.
          '  <tr>'.
          '    <td>&nbsp;</td>'.
          '    <td>Átháritott ÁFA összege:</td>'.
          '    <td align="right">'.http::moneyFormat( $sum_tax[$tax_name], 'HUF' ).'</td>'.
          '  </tr>'.
          '  <tr>'.
          '    <td>&nbsp;</td>' .
          '    <td style="border-bottom: 1px solid #000">Számla bruttó végösszege:</td>'.
          '    <td align="right" style="border-bottom: 1px solid #000">'.http::moneyFormat( $sum_net[$tax_name] + $sum_tax[$tax_name], 'HUF' ).'</td>'.
          '  </tr>'.
          '  <tr>'.
          '    <td>&nbsp;</td>' .
          '    <td>Számla fizetendő összege:</td>'.
          '    <td align="right"><b>'.http::moneyFormat( $sum_net[$tax_name] + $sum_tax[$tax_name], 'HUF' ).'</b></td>'.
          '  </tr>'.
          '</table>';
        
        if( 1==1 or
          ( setup::$company->configuration->supplierInfo->is_tourist_tax_receipt ?? 0 )
          and setup::$company->configuration->supplierInfo->is_tourist_tax_receipt
        )
          if( $tourist_tax = db::get( 'tourist_taxes', 'invoice_id='.$invoice['id'] )){
            $html.=
              '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt;margin-top: 50px">'.
              '  <tr><td align="right"><b style="font-size: 14pt">ÁTVÉTELI ELISMERVÉNY<br></b><b>Azonosító</b>: '.$tourist_tax['booking_id'].'-'.$tourist_tax['id'].'</td></tr>'.
              '  <tr><td style="border-top: 1px solid #000;width: 100%"></td></tr>'.
              '  <tr><td><b>Átvevő</b>: '.$invoice['supplier']['name'].' ('.$invoice['supplier']['postalCode'].' '.$invoice['supplier']['city'].
              ', '.$invoice['supplier']['streetName'].' '.$invoice['supplier']['publicPlaceCategory'].' '.$invoice['supplier']['number'].')'.
              ' <b>Adószám</b>: '.$invoice['supplier']['taxNumber'].'</td></tr>'.
              '  <tr><td>igazolom, hogy idegenforgalmi adó (IFA) címén feltüntetett összeget</td></tr>'.
              '  <tr><td><b>Átadó</b>: '.$invoice['customer']['name'].' ('.$invoice['customer']['postalCode'].' '.$invoice['customer']['city'].
              ', '.$invoice['customer']['additionalAddressDetail'].')'.
              ' <b>Adóazonosító</b>: '.$invoice['customer']['taxNumber'].'</td></tr>'.
              '  <tr><td>-tól '.$tourist_tax['payday'].' napon átvettem.</td></tr>'.
              '</table>'.
              '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt">'.
              '  <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>'.
              '  <tr>'.
              '    <td width="50%">&nbsp;</td>'.
              '    <td width="30%">Idegengorgalmi adó (IFA) összege:</td>'.
              '    <td width="20%" align="right"><b>'.http::moneyFormat( $tourist_tax['amount'], 'HUF' ).'</b></td>'.
              '  </tr>'.
              '  <tr><td colspan="3" style="border-top: 1px solid #000;width: 100%"></td></tr>'.
              '</table>'.
              '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt; margin-top: 50px">'.
              '  <tr>'.
              '    <td width="50%">&nbsp;</td>'.
              '    <td width="30%" style="border-top: 1px solid #000">Fizetendő összeg:</td>'.
              '    <td width="20%" align="right" style="border-top: 1px solid #000"><b style="font-size: 14pt">'.
              http::moneyFormat( $sum_net[$tax_name] + $sum_tax[$tax_name] + $tourist_tax['amount'], 'HUF' ).'</b></td>'.
              '  </tr>'.
              '</table>';
          }
        
        $pdf = new \Mpdf\Mpdf( [
          'margin_left' => 10,
          'margin_right' => 10,
          'margin_top' => 30,
          'margin_bottom' => 20,
          'margin_header' => 5,
          'margin_footer' => 5,
          'mode' => 'utf-8'
        ] );

        $pdf->SetHTMLHeader( $header );
        $pdf->SetHTMLFooter( $footer );
        $pdf->WriteHTML( $html );
        $pdf->Output( 'upload/'.$_SESSION['COMPANY'].'/invoice_'.$invoice['id'].'.pdf', 'F' );
        $res = [ 'ok' => true ];
      }else $res = [ 'ok' => false, 'error' => 'Nincs tétel!' ];
    }else $res = [ 'ok' => false, 'error' => 'Nincs számla!' ];
    ob_end_clean();
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'test':
    $res = [ 'ok' => true, 'data' => 'test' ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;

  case 'uploadfile':
    if( $_FILES['myFile'] ?? 0 ){
      $file_type = explode( '.', $_FILES['myFile']['name'] );
      $file_type[1] = strtolower( $file_type[1] );

      if( 'jpg' == $file_type[1] or 'jpeg' == $file_type[1] or 'webp' == $file_type[1] ){
        $file_type[1] = ( 'jpg' == $file_type[1] or 'jpeg' == $file_type[1] )? 'jpg' : $file_type[1];
        if(( $_POST['imageid'] ?? 0 ) and $_POST['imageid'] )
          $pic = $_POST['imageid'];
        else{
          $pic = 1;
          while( file_exists( 'upload/'.$_SESSION['COMPANY'].'/pic'.sprintf( "%04d", $pic ).'.'.$file_type[1] ))
            $pic++;
        }
        $kep = 'upload/'.$_SESSION['COMPANY'].'/pic'.sprintf( "%04d", $pic ).'.'.$file_type[1];
        move_uploaded_file( $_FILES['myFile']['tmp_name'], $kep );

        /*
        meretezo_jpg($_FILES['myFile']['tmp_name'],900,600,$kep.'.jpg',100,1,'');
        meretezo_jpg($_FILES['myFile']['tmp_name'],154,154,$kep.'n.jpg',100,1,'z');
        meretezo_jpg($_FILES['myFile']['tmp_name'],0,93,$kep.'l.jpg',100,1,'y');
        meretezo_jpg($_FILES['myFile']['tmp_name'],320,0,$kep.'i.jpg',100,1,'x');
        $kep.='n.jpg?'.md5(date("YmdHis"));
        */

        if(( $_POST['id'] ?? 0 ) and ( $_POST['tablename'] ?? 0 ) and $table = db::get( $_POST['tablename'], intval( $_POST['id'] )))
          if( $table['image_sequence'] ?? 0 ){
            $sequence = json_decode( $table['image_sequence'], true );
            $sequence[] = $pic;
            db::save( $_POST['tablename'], [['image_sequence', json_encode( $sequence )]], intval( $_POST['id'] ));
          }else
            db::save( $_POST['tablename'], [['image_id', $pic ]], intval( $_POST['id'] ));     
        $res = [ 'ok' => true, 'src' => '/'.$kep, 'id' => $pic ];
      }else $res = [ 'ok' => false ];
    }else $res = [ 'ok' => false ];
    header( 'Content-Type: application/json' );
    header( 'HTTP/1.1 200 OK' );
    echo json_encode( $res );
    exit;
  break;
}
