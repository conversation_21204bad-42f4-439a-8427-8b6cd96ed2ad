  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Popup ajánlók</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új popup</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable( 
          'popups',
          [ 'allapotstr' => ['th' => 'Állapot', 'status' => 'allapot'],
            'name' => ['th' => 'Megnevezés'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés::id',
                'trash-empty:event:Törlés:/popup/del/:id:confirm:where:db'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { datatable, dialog, upload, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd
      switch( ifunction ){
        case 'popups_edit':
          upload()
        break
        case 'popups_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_popup' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/popup',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'A popup módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'popups_edit_close':
          location.replace( '/popups' )
        break
        case 'popups_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( { 
            url: '/modal/popupdel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/popups' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a popup!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új popup',
          label: 'Megnevezés'
        },
        eventManagerFunctions
      } )
    } )
  </script>