<?php
if( $_POST ?? 0 ){
  http::cleanPost();
  if( !( response::$vw->error ?? 0 ) ){
    switch( $_POST['event'] ){

      case 'booking':
        setup::$company->configuration->events->booking->messageTemplate->operator = $_POST['template_operator_booking'];
        setup::$company->configuration->events->booking->messageTemplate->customer = $_POST['template_customer_booking'];
      break;
      case 'advance_payment':
        setup::$company->configuration->events->advancePayment->is = $_POST['is_advance_payment'];
        setup::$company->configuration->events->advancePayment->messageTemplate->operator = $_POST['template_operator_advance_payment'];
        setup::$company->configuration->events->advancePayment->messageTemplate->customer = $_POST['template_customer_advance_payment'];
        setup::$company->configuration->events->advancePayment->deadline = $_POST['advance_payment_deadline'];
      break;
      case 'data_request':
        setup::$company->configuration->events->dataRequest->messageTemplate->operator = $_POST['template_operator_data_request'];
        setup::$company->configuration->events->dataRequest->messageTemplate->customer = $_POST['template_customer_data_request'];
        setup::$company->configuration->events->dataRequest->deadline = $_POST['data_request_deadline'];
      break;
    }
    setup::save_configuration( $_SESSION['COMPANY'], setup::$company->configuration );
  }
}

if( $message_templates = setup::list( DATABASENAME.'.message_templates', 'company_id = '.$_SESSION['COMPANY'].' AND MID(signs, 1, 1) = "2"' ) )
  response::add( 'view', 'templates', $message_templates );
$_POST['data_request_deadline'] = $_POST['data_request_deadline'] ?? setup::$company->configuration->events->dataRequest->deadline ?? '';

$_POST['template_operator_booking'] = $_POST['template_operator_booking'] ?? setup::$company->configuration->events->booking->messageTemplate->operator ?? 0;
$_POST['template_customer_booking'] = $_POST['template_customer_booking'] ?? setup::$company->configuration->events->booking->messageTemplate->customer ?? 0;

$_POST['is_advance_payment'] = $_POST['is_advance_payment'] ?? setup::$company->configuration->events->advancePayment->is ?? 0;
$_POST['template_operator_advance_payment'] = $_POST['template_operator_advance_payment'] ?? setup::$company->configuration->events->advancePayment->messageTemplate->operator ?? 0;
$_POST['template_customer_advance_payment'] = $_POST['template_customer_advance_payment'] ?? setup::$company->configuration->events->advancePayment->messageTemplate->customer ?? 0;
$_POST['advance_payment_deadline'] = $_POST['advance_payment_deadline'] ?? setup::$company->configuration->events->advancePayment->deadline ?? '';