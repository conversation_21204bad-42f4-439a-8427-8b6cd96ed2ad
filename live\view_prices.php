        <style>
          /*
          .toggle{
            position: absolute;
            top: 50%;
            left: 50%;
            margin-right: -50%;
            transform: translate(-50%, -50%);
            &>input{
              display: none;
              &:checked + label:before{
                box-shadow: inset 0px 3px 0 2px rgba(89,202,89,1);
                background: #fff;
              }
            }
            &>label{
              cursor: pointer;
              margin: 10px 10px;
              &:before{
                content: '';
                display: inline-block;
                height: 30px;
                width: 30px;
                background: #59CA59;
                border-radius: 50%;
                z-index: 2;
                transition: box-shadow .4s ease,
                background .3s ease;
              }
            }
          }
          */
          article>h1{
            scroll-snap-align: center;
            position: sticky;
            top: 0px;
            background-color: #fff;
          }
          .prices{
            display: flex;
            flex-flow: row wrap;
            justify-content: space-evenly;
            gap: 1vw;
            width: 100%;
            margin: 3vh 0;
            & b{ color: var(--danger-color) }
            &>div{
              width: calc(25% - 1vw);
              min-width: 300px;
              border-radius: 8px;
              background-color: var(--cart-bg-color);
              &>h1{
                padding: clamp( .25rem, 1vw, 1rem );
                border-radius: 8px 8px 0 0;
                background-color: var(--cart-border-color);
              }
              &>header{
                padding: clamp( .25rem, 1vw, 1rem );
                text-align: center;
                &>span{
                  font-size: 2em;
                  font-weight: bold;
                  &>i{
                    font-weight: normal;
                    margin-left: 10px;
                    font-size: 1rem;
                  }
                }
                &>p{ margin: 0 }
              }
              &>ul{
                min-height: 164px;
                padding: clamp( .25rem, 1vw, 1rem );
                background-color: var(--cart-article-bg-color);
                border: 1px solid var(--cart-bg-color);
                &>li::before{
                  padding-right: .25rem;
                }
              }
              &>footer{
                padding: clamp( .25rem, 1vw, 1rem );
                text-align: center;
              }
              &.comingsoon{
                & li{
                  rotate: 30deg;
                  margin-top: 50px;
                  text-align: center;
                }
              }
            }
          }
          .modules{
            width: 100%;
            margin: 3vh 0;
            border-radius: 8px;
            background-color: var(--cart-bg-color);
            & b{ color: var(--danger-color) }
            &.service>div{
              background-color: var(--link-color);
              border: 0;
              &>div>ul{ min-height: 191px }
            }
            &>h1{
              padding: clamp( .25rem, 1vw, 1rem );
              border-radius: 8px 8px 0 0;
              background-color: var(--cart-border-color);
            }
            &>header{
              padding: clamp( .25rem, 1vw, 1rem );
              text-align: center;
              &>span{
                font-size: 2em;
                font-weight: bold;
                &>i{
                  font-weight: normal;
                  margin-left: 10px;
                  font-size: 1rem;
                }
              }
              &>p{ margin: 0 }
            }
            &>div{
              display: flex;
              flex-flow: row wrap;
              justify-content: space-evenly;
              gap: 1vw;
              padding: clamp( .25rem, 1vw, 1rem );
              border: 1px solid var(--cart-bg-color);
              background-color: var(--btn-bg-color);
              &>div{
                width: calc(25% - 1vw);
                min-width: 300px;
                border-radius: 8px;
                background-color: var(--cart-bg-color);
                &>h1{
                  padding: clamp( .25rem, 1vw, 1rem );
                  border-radius: 8px 8px 0 0;
                  background-color: var(--cart-border-color);
                }
                &>header{
                  padding: clamp( .25rem, 1vw, 1rem );
                  text-align: center;
                  &>span{
                    font-size: 2em;
                    font-weight: bold;
                    &>i{
                      font-weight: normal;
                      margin-left: 10px;
                      font-size: 1rem;
                    }
                  }
                  &>p{ margin: 0 }
                }
                &>ul{
                  min-height: 218px;
                  padding: clamp( .25rem, 1vw, 1rem );
                  background-color: var(--cart-article-bg-color);
                  border: 1px solid var(--cart-bg-color);
                  &>li::before{
                    padding-right: .25rem;
                  }
                }
                &>footer{
                  padding: clamp( .25rem, 1vw, 1rem );
                  text-align: center;
                  &>p{ margin: 0 }
                }
              }
            }
          }
          details{
            &>summary{
              color: var(--link-color);
              &:hover{ color: var(--link-hover-color) }
            }
            &>div{
              position: absolute;
              left: 5%;
              width: 90%;
              padding: clamp( 0.25rem, 1vw, 1rem );
              border-radius: 8px;
              border: 1px solid var(--dark);
              text-align: left;
              color: var(--light);
              background-color: var(--link-color);
              & b{ color: var(--danger-color) }
            }
          }
        </style>
        <h1>Csomagok és árak<span></span></h1>
        <form>
          <ul class="formbox" style="justify-content: center">
            <li class="form toggle col2">
              <input type="checkbox" name="period" id="period" checked>
              <label for="period"><span>Éves</span><span>Havi</span></label>
            </li>
          </ul>
        </form>
        <div class="prices">
          <div>
            <h1>#free</h1>
            <header>
              <span>0 Ft</span>
              <p>Mindig ingyen!</p>
            </header>
            <ul>
              <li style="--icon:var(--icon-ok)">Szobafoglalási rendszer</li>
              <li style="--icon:var(--icon-ok)">Naptár (szobatükör)</li>
              <?php /*
              <li style="--icon:var(--icon-ok)">Vendég adatok kezelése</li>
              <li style="--icon:var(--icon-ok)">NTAK, VISA adatküldés</li>
              */ ?>
              <li style="--icon:var(--icon-ok)">Lakóegység kezelés</li>
              <li style="--icon:var(--icon-ok)">Kimutatások, statisztikák</li>
              <li style="--icon:var(--icon-ok)">Foglalási szabályok</li>
              <li style="--icon:var(--icon-ok)">Árazási szabályok</li>
              <li style="--icon:var(--icon-ok)">iCal szinkron</li>
            </ul>
            <footer>
              <a class="btn" href="/registration/free">Ingyenes regisztráció</a>
              <?php /*
              <details>
                <summary>részletek</summary>
                <div>

                </div>
              </details>
              */ ?>
            </footer>
          </div>
        </div>
        <div class="modules">
          <div>
            További kiegészítések hamarosan érkeznek!
            <?php /*
            <div>
              <h1>#számla</h1>
              <header>
                <span data-price="+ 2 490 Ft<i>+ áfa/hó</i>|+ 24 900 Ft<i>+ áfa/év</i>">+ 2 490 Ft<i>+ áfa/hó</i></span>
                <p>Számláz jól és kényelmesen!</p>
              </header>
              <ul>
                <li style="--icon:var(--icon-ok)">Számla készítés</li>
                <li style="--icon:var(--icon-ok)">Előleg kezelés</li>
                <li style="--icon:var(--icon-ok)">NAV adatküldés</li>
                <li style="--icon:var(--icon-ok)">IFA átvételi</li>
                <li style="--icon:var(--icon-ok)">Számlák nyílvántartása</li>
              </ul>
              <footer>
                <details>
                <summary>részletek</summary>
                <div>
                  <h2>NAV kompatibilis számlázó program szállás-szolgáltatás tevékenységhez</h2>
                  <p>
                    A mi számlázó programunk optimalizált szállás-szolgáltatás tevékenységre, és egyben megoldja az IFA átvételének
                    igazolását a törvényi előírásoknak megfelelően.
                  </p>
                  <strong>Főbb Funkciók:</strong>
                  <ul>
                    <li>
                      <strong>IFA átvételi bizonylat kezelése:</strong> A program megoldja az IFA átvételének igazolását a törvényi előírásoknak megfelelően.
                    </li>
                    <li><strong>Előleg kezelése:</strong> A program kezeli az előleget és képes végszámlát készíteni az előlegből.</li>
                    <li><strong>Figyelmeztetések:</strong> A program figyelmeztet, ha végszámlákat kell elkészíteni.</li>
                    <li>
                      <strong>Idegenforgalmi adó kedvezmények:</strong>
                      A program nyilvántartja az idegenforgalmi adó kedvezményeket és az átvételi bizonylatra felvezeti az adó átvételét.
                    </li>
                    <li><strong>NAV jóváhagyás:</strong> A program NAV jóváhagyással rendelkezik, és a szabályoknak megfelelően kezeli az IFA bizonylatolást.</li>
                  </ul>
                </div>
              </details>
              </footer>
            </div>
            <div>
              <h1>#adó</h1>
              <header>
                <span data-price="+ 990 Ft<i>+ áfa/hó</i>|+ 9 900 Ft<i>+ áfa/év</i>">+ 990 Ft<i>+ áfa/hó</i></span>
                <p>Nyugalom, kiszámoljuk!</p>
              </header>
              <ul>
                <li style="--icon:var(--icon-ok)">IFA havi bevallás</li>
                <li style="--icon:var(--icon-ok)">ÁFA segédlet</li>
                <li style="--icon:var(--icon-ok)">TFH bevallás</li>
              </ul>
              <footer>
                Csak a <b>#számla</b> modullal eggyütt!<br>
                <details>
                  <summary>részletek</summary>
                  <div>
                    <h2>IFA és TFH bevallás elkészítése</h2>
                    <p>
                      A számlázási adatok alapján havonta elkészítjük az IFA bevallást. Kiszámoljuk a bevalláshoz szükséges Turizmusfejlesztési hozzájárulást
                      (TFH) és biztosítjuk az adatokat a havi ÁFA bevalláshoz, amennyiben szükséges.
                    </p>
                  </div>
                </details>
              </footer>
            </div>
            */ ?>
          </div>
        </div>
        A feltöntetett ára a 27% áfát nem tartalmazzák!
        <script type="module">
          import {$, $$} from '/shared/js/spritzer/index.js'
          $( '#period' ).addEventListener( 'change', ( e ) => {
            const prices = $$( '[data-price]' )
            prices ? prices.forEach( price => {
              let insertPrice = price.dataset.price.split( '|' )
              price.innerHTML = insertPrice[ e.target.checked ? 0 : 1 ]
            } ) : null
          } )
        </script>
