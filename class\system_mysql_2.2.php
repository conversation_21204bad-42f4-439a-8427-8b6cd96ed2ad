<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kezelő mysql
 *
 * @method `query();`          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>
 * @method `save();`           UPDATE / INSERT gyorshívás
 * @method `get();`            SELECT LIMIT 1 gyorshívás
 * @method `list();`           tábla sorainak listázása
 * @method `del();`            tábla sorainak törlése, állapot váltása
 * @method `get_piece();`      tábla feltételnek megfelelő sorainak száma
 * @method `get_columnName();` Tábla mezőnevek kiolvasása
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2017, Tánczos Róbert
 * 
 * @version 2.2.1
 * @since 2.2.1 2025.05.28 columns paraméter hozzáadása `get();` _refparms megszüntetése
 * @since 2.2.0 2023.07.11 bővítés `get();`, `list();`, `del();`, `get_piece() és OBJECT_TYPE;` 
 * @since 2.1.0 2022.08.16 bővítés `get_columnName();`
 * @since 2.0.0 2022.05.05 Új verzió követés. Fájlnévben jelölve. upin() kivezetés, optimalizálás 
 * @since 1.4.1 2022.04.16 `save();` hiba javítás (insert_id hiánya)
 * @since 1.4.0 2021.10.10 bővítés `save();`
 * @since 1.3.1 2019.07.14 egyszerűsítés, optimalizálás
 * @since 1.3.0 2018.07.05 bővítés `upin();`
 * @since 1.2.0 2018.01.08 javítás php 5.2x -> 5.3x `valtozokontrol();`
 * @since 1.1.0 2017.07.30 hibajelentés logfileba + project_id
 * @since 1.0.0 2017.01.15
 */

define( 'OBJECT_TYPE', true );

class mysql{

  private static $con = null;
  private static $loggedQuery = false;
  private static $loggedTable = '';

  /** Kapcsolódás az adatbázishoz
   *
   */
  private static function _connect(){
    if( is_null( self::$con )){
      mysqli_report( MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT );
      require_once 'config_mysql.php';
      self::$con = new mysqli( DATABASE['servername'],
                               DATABASE['username'],
                               DATABASE['pwd'],
                               DATABASE['dbname'],
                               DATABASE['port']
                             );
      self::$con->set_charset( 'utf8mb4' );
      self::$loggedQuery = defined( 'LOGEDDATABASEQUERY' )? LOGEDDATABASEQUERY : false;
      self::$loggedTable = defined( 'LOGEDDATABASETABLE' )? LOGEDDATABASETABLE : '';
    }
  }

  /** Adatbázis kérés
   * @param  string      $query SQL utasítás
   * @param  array       $parms adatkötés értékei
   * @param  string      $types adatkötés tipusai
   * @return false|array eredmény ha hiba akkor 0
   */
  public static function query( $query, $parms = 0, $types = 0 ){  
    self::_connect();
    if( self::$loggedQuery and ( !self::$loggedTable or strpos( $query, self::$loggedTable ) !== false ))
      user_error( 'DB QUERY: '.$query.(( $parms )? ' PARAMS: '.json_encode( $parms ) : '' ));
    if( $parms ){
      if( $stmt = self::$con->prepare( $query )){
        if( !$types )
          $types = str_repeat( 's', count( $parms ));
        $stmt->bind_param( $types, ...$parms );
        $stmt->execute();
      }
      return $stmt;
    }else
      return self::$con->query( $query );
  }

  /** UPDATE / INSERT gyorshívás
   * @param  string           $table tábla neve
   * @param  array            $data  adatok [['mező neve',érték,'tipus'],[...],...] Ha a tipus string akkor elhagyható
   * @param  array|int|string $id 0 INSERT
   *                              int UPDATE id mező értéke
   *                              array UPDATE rekord mutató ['mező neve',érték,'tipus'] Ha a tipus string akkor elhagyható
   *                              string UPDATE akkor WHERE feltétel pl. 'product_id IN(45,67,33) AND status=1'
   * @return false|int|string   azonositó, ha hiba akkor false
   */
  public static function save( $table, $data, $where = 0 ){
    $query = (( $where )? 'UPDATE ' : 'INSERT INTO ' ).$table.' SET ';
    $type  = '';
    $first = '';
    $value = [];
    $res = false;
    if( $data and is_array( $data )){
      foreach( $data as $field ){
        $query  .= $first.$field[0].'=?';
        $value[] = $field[1];
        $type   .= $field[2] ?? 's';
        $first = ',';
      }
      if( $where )
        switch( gettype( $where )){
          case 'array':
            $query  .= ' WHERE '. $where[0].'=?';
            $value[] = $where[1];
            $type   .= $where[2] ?? 's';
          break;
          case 'string':
            $query.= ' WHERE '.$where;
          break;
          case 'integer':
            $query  .= ' WHERE id=?';
            $value[] = $where;
            $type   .= 'i';
          break;
        }
      if( $res = self::query( $query, $value, $type ))
        $res = $res->insert_id;
    }
    return $res;
  }

  /** SELECT LIMIT 1 gyorshívás
   * @param  string         $table   tábla neve
   * @param  integer|string $where   id vagy WHERE összeállítás pl. 'name LIKE "Balaton%" AND status=1'
   * @param  integer        $obj     0: array, 1: object
   * @param  integer|string $columns egyéni mezők megadása pl. 'name, MAX(ev) ev', ha nincs akkor 0
   * @return false|array|object      eredmény ha nincs akkor false
   */
  public static function get( $table, $where = 0, $obj = 0, $columns = 0 ){
    $query = 'SELECT '.( $columns ? $columns : '*' ).' FROM '.$table;
    if( $where )
      $query.= ' WHERE '. (( is_numeric( $where ))? 'id=' : '' ). $where;
    $query.= ' LIMIT 1';
    $res = self::query( $query );
    if( $obj )
      return $res ? $res->fetch_object() : $res;
    else
      return $res ? $res->fetch_assoc() : $res;
  }
  
  /** tábla sorainak listázása
   * @param  string         $table   tábla neve
   * @param  integer|string $where   WHERE összeállítás pl. 'name LIKE "Balaton%" AND status=1', ha nincs akkor 0
   * @param  integer|string $orderby ORDER BY összeállítás pl. 'name, status DESC', ha nincs akkor 0
   * @param  integer        $limit   maximális sorok száma, ha nincs akkor 0
   * @param  integer        $offset  induló sor, ha nincs akkor 0
   * @param  integer|string $columns egyéni mezők megadása pl. 'name, MAX(ev) ev', ha nincs akkor 0
   * @return false|array             eredmény ha hiba akkor false
   */
  public static function list( $table, $where = 0, $orderby = 0, $limit = 0, $offset = 0, $columns = 0 ){
    $query = 'SELECT '.( $columns ? $columns : '*' ).' FROM '.$table;
    if( $where )
      $query.= ' WHERE '.$where;
    if( $orderby )
      $query.= ' ORDER BY '.$orderby;
    if( $limit )
      if( $offset )
        $query.= ' LIMIT '.$offset.','.$limit;
      else
        $query.= ' LIMIT '.$limit;
    if( $res = self::query( $query ))
      $res = $res->fetch_all( MYSQLI_ASSOC );
    return $res ? ( count( $res ) ? $res : false ) : false;
  }

  /** tábla sorainak törlése, állapot váltása
   * @param  string         $table  tábla neve
   * @param  integer|string $where  id vagy WHERE összeállítás pl. 'name LIKE "Balaton%" AND status=1'
   * @param  null|integer   $status ha meg van adva, akkor nem DELETE hanem UPDATE és a status mező változik
   * @return false|true             eredmény ha hiba akkor false
   */
  public static function del( $table, $where = null, $status = null ){
    if( $where ?? 0 ){
      if( $status ?? 0 )
        $query = 'UPDATE '.$table.' SET status='.$status;
      else
        $query = 'DELETE FROM '.$table;
      if( $where == 'ALL' )
        $query.= '';
      elseif( $where )
        $query.= ' WHERE '. (( is_int( $where ))? 'id=' : '' ). $where;
      return self::query( $query );
    }
  }

  /** tábla feltételnek megfelelő sorainak száma
   * @param  string         $table tábla neve
   * @param  integer|string $where WHERE összeállítás pl. 'name LIKE "Balaton%" AND status=1', ha nincs akkor 0
   * @return integer               eredmény 0 vagy a sorok száma
   */
  public static function get_piece( $table, $where = 0 ){
    $query = 'SELECT COUNT(*) piece FROM '. $table;
    if( $where )
      $query.= ' WHERE '. $where;
    if( $res = self::query( $query ))
      $res = $res->fetch_assoc();
    return $res ? $res['piece'] : 0;
  }

  /** Tábla mezőnevek kiolvasása
   * @param  string         $database adatbázis neve
   * @param  string         $table    tábla neve
   * @return false|array              eredmény oszlopok tömbje pl. ['id', 'nev', 'status']
   */
  public static function get_columnName( $database, $table ){
    $column = null;
    $query = 'SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_SCHEMA=? AND TABLE_NAME=?';
    if( $res = self::query( $query, [$database, $table], 'ss' ))
      if( $res = $res->get_result())
        if( $res = $res->fetch_all( MYSQLI_ASSOC ))
          foreach( $res as $rec )
            $column[] = $rec['COLUMN_NAME'];
    return $res ? ( $column ?? false ) : false;
  }
}
