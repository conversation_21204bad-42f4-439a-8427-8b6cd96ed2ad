<header>
  <h6><PERSON><PERSON><PERSON><PERSON> - <span style="color:var(--danger-color)"><?= http::$route[2] ?></span></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <?php foreach( response::$vw->view->foglallakoegysegek as $foglallakoegyseg ){ ?>
  <form name="form_data_modosit-le">
    <input type="hidden" name="foglallakoegyseg_id" value="<?= $foglallakoegyseg->foglallakoegyseg_id ?>">
    <input type="hidden" name="lakoegyseg_id" value="<?= $foglallakoegyseg->lakoegyseg_id ?>">
    <ul class="formbox">
      <li class="form col5">
        <input type="text" placeholder="" value="<?= $foglallakoegyseg->name ?>" disabled>
        <label>Lakóegység</label>
      </li>
      <li class="form col3">
        <input type="text" name="erkezes" placeholder="" value="<?= $foglallakoegyseg->erkezes ?>" disabled>
        <label>Kezdő nap</label>
      </li>
      <li class="form col2">
        <input type="text" name="ej" placeholder="" value="<?= count( json_decode( $foglallakoegyseg->napokfo ) ) ?>" disabled>
        <label>Nap</label>
      </li>
    </ul>
  </form>
  <?php } ?>
</section>
<footer>
  <button class="callback" name="btn_modositas" title="Zárolás törlése" onclick="return confirm( 'Biztos, hogy törlöd?' )">Törlés</button>
  <button class="close">Kilép</button>
</footer>