  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Számlák</h5>
      </header>
      <article>
        <?= table::datatable( 
          'invoices',
          [ 'invoice_number' => ['th' => 'Számlaszám'],
            'allapotstr' => ['th' => 'Állapot', 'status' => 'allapot'],
            'issue_date' => ['th' => 'Kiállítás kelte'],
            'booking_id' => ['th' => '#'],
            'customer_name' => ['th' => 'Vevő'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés::id',
                'file-pdf:event:PDF::id'
              ]
            ]
          ],
          ['search' => true, 'import' => 'NAV import'],
          true
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { $, datatable, dialog, upload, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let
        ifunction = data[1],
        id = data[2] || null,
        fd,
        selects
      switch( ifunction ){
        case 'invoices_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_invoice' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/invoice',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'A számla módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'invoices_edit_close':
          location.replace( '/invoices' )
        break
        case 'invoices_file-pdf':
          event.preventDefault()
          fd = new FormData()
          fd.append( 'invoice_id', id )
          ajax( {
            url: '/modal/invoicepdf',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                dialog( {
                  'type': 'dialog',
                  'isESCKey': true,
                  'iconClose': '--icon-cancel',
                  'title': 'Számla pdf',
                  'content': 'pdf: <a href="/upload/<?= $_SESSION['COMPANY'] ?>/invoice_'+id+'.pdf" target="_blank">invoice_'+id+'.pdf</a>'
                } )
              }else
                dialog( {type: 'status:error', content: 'Sikertelen a pdf generálás! ' + response.error, timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'invoices_import':
          event.preventDefault()
          dialog( {
            id: 'invoices-import',
            type: 'dialog',
            btnCallback: 'Importálás',
            isBtnCallback: true,
            isBtnClose: true,
            isKeepOpen: true,
            title: 'Számlák importálása NAV-tól',
            content:
              '<form name="form_data_honap">' +
              '  <ul class="formbox">' +              
              '    <li class="form col4" style="--toicon:var(--icon-angle-double-down)">' +
              '      <select name="year">' +
              '        <option value="2025">2025</option>' +
              '        <option value="2024">2024</option>' +
              '        <option value="2023">2023</option>' +
              '      </select>' +
              '      <label>Év</label>' +
              '    </li>' +
              '    <li class="form col6" style="--toicon:var(--icon-angle-double-down)">' +
              '      <select name="month">' +
              '        <option value="01">Január</option>' +
              '        <option value="02">Február</option>' +
              '        <option value="03">Március</option>' +
              '        <option value="04">Április</option>' +
              '        <option value="05">Május</option>' +
              '        <option value="06">Június</option>' +
              '        <option value="07">Július</option>' +
              '        <option value="08">Augusztus</option>' +
              '        <option value="09">Szeptember</option>' +
              '        <option value="10">Október</option>' +
              '        <option value="11">November</option>' +
              '        <option value="12">December</option>' +
              '      </select>' +
              '      <label>Hónap</label>' +
              '    </li>' +
              '  </ul>' +
              '</form>',
            eventManagers: {
              'callback': ['', 'invoicesImport']
            },
            eventManagerFunctions: function( data ){
              let ifunction = data[1],
                  id = data[2] || null,
                  fd
              switch( ifunction ){
                case 'invoicesImport':
                  event.preventDefault()
                  fd = new FormData( $( 'form[name="form_data_honap"]' ))
                  ajax( {
                    url: '/modal/invoicesnavimport',
                    body: fd,
                    done: ( back ) => {
                      console.log( 'back', back.response )
                      location.replace( '/invoices' )
                    },
                    fail: ( err ) => {
                      dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                    }
                  } )
                break
              }
            }
          } )
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        eventManagers: {
          'import': ['', 'invoices_import']
        },
        eventManagerFunctions
      } )
    } )
  </script>