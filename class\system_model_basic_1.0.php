<?php
/**
 * megosztott adatb<PERSON><PERSON>s alapvet<PERSON> tábla kezelő
 *
 * @method `save_status();`         signs <PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>llít<PERSON>a
 * @method `get_companyApi();       Vállalkozás beolvasása api kulcs alapján
 * @method `get_userEmail();        <PERSON>lhassználó beolvasása email alapján
 * @method `save_user();`           Megadott felhasználói adatok menté<PERSON>
 * @method `list_staff();`          Munkatársak adatainak beolvasása
 * @method `list_countries();`      Országok listázása
 * @method `list_dictionary();`     Szótárhoz a nyelv beolvasása
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2017, Tánczos Róbert
 * 
 * @version 1.0.0
 * @since 1.0.0 2022.12.31 model szétválasztása és új basic rész indulása
 */

class model_basic extends mysql{
  
  public static function list_dictionary(
    string $sourceLng,
    string $targetLng,
    ?int $project_id = null,
    string $page = ''
  ): array {
    $sql = 'SELECT l.id, lq.term question, IF(la.term IS NULL, "", la.term) answer
              FROM shared.lng l
         LEFT JOIN shared.lng_'.$sourceLng.' lq ON l.id = lq.id
         LEFT JOIN shared.lng_'.$targetLng.' la ON l.id = la.id';
    $where = $type = '';
    $data = [];
    if( $project_id ?? 0 ){
      $where.= ' WHERE (l.project_id = 0';
      if( $project_id ){
        $where.= ' OR l.project_id=?';
        $data['project_id'] = $project_id;
        $type.= 'i';
      }
      $sql.= ')';
    }
    if( $page != '' ){
      $where.= ( $where == '' )? ' WHERE' : ' AND';
      $where.= ' (l.page IS NULL OR l.page=?)';
      $data['page'] = $page;
      $type.= 's';
    }
    $sql.= $where.' ORDER BY l.id';
    if( $type == '' )
      return self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      return self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
  }

  public static function get_dictionary(
    string $sourceLng,
    string $targetLng,
    string $question
  ): string {
    $sql = 'SELECT IF(la.term IS NULL, "", la.term) answer
              FROM shared.lng_'.$sourceLng.' lq
         LEFT JOIN shared.lng_'.$targetLng.' la ON lq.id = la.id
             WHERE LOWER(lq.term)=? LIMIT 1';
    $row = self::query( $sql, [mb_strtolower( $question, 'UTF-8' )], 's' )->get_result()->fetch_assoc();
    return ( $row['answer'] == '' )? $question : $row['answer'];
  }

  public static function save_last_use_dictionary( int $id = 0 ): int {
    return self::save( 'shared.lng', [['last_use', date('Y-m-d H:i:s')]], $id );
  }

  public static function save_new_term( int $id, string $sourceLng, string $question ): int {
    return self::save( 'shared.lng_'.$sourceLng, [['id', $id, 'i'], ['term', $question]] );
  }

  // === ahol van `signs` a táblában ott állítja az első jelzőt 0/1
  public static function save_status( $id, $table, $database = 'shared' ){
    $sql = 'UPDATE '.$database.'.'.$table.' SET signs=CONCAT(IF(MID(signs,1,1)="1","2","1"),SUBSTRING(signs,2)) WHERE id=? LIMIT 1';
    $res = self::query( $sql, [ $id ], 'i' );
    return $res;
  }
  public static function get_companyApi( $apikey ){
    $sql = 'SELECT * FROM shared.companies WHERE api_key=? LIMIT 1';
    if( $apikey != '' )
      $res = self::query( $sql, [ $apikey ] )->get_result()->fetch_assoc();
    else $res = false;
    return $res;
  }
  public static function get_user( $user_id, $object = 0 ){
    return self::get( 'shared.users', $user_id, $object );
  }
  public static function get_userEmail( $email ){
    $sql = 'SELECT * FROM shared.users WHERE email=? LIMIT 1';
    return self::query( $sql, [$email] )->get_result()->fetch_assoc();
  }
  public static function list_usages( $user_id, $project_id ){
    $sql = 'SELECT up.*, u.company_id, u.level_and_rights FROM shared.user_projects up
        INNER JOIN shared.usages u ON u.user_project_id=up.id
             WHERE up.user_id=? AND up.project_id=? ORDER BY u.company_id';
    $res = self::query( $sql, [$user_id, $project_id], 'ii' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) )? $list : 0;
  }
  public static function list_select_company( $company_ids ){
    $sql = 'SELECT id, name FROM shared.companies WHERE id IN('.$company_ids.') ORDER BY id';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    if( $res )
      foreach( $res as $row )
        $list[$row['id']] = $row['name'];
    return ( count( $list ) > 1 )? $list : 0;
  }
  public static function save_configuration( $company_id, $configuration ){
    db::save( 'shared.companies', [ ['configuration', json_encode( $configuration, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES )]
    ], ['id', $company_id, 'i'] ); 
  }
  public static function list_countries( $columns = null ){ // string pl. 'code2,name'
    $sql = 'SELECT '.( $columns ?? '*' ).' FROM shared.administrative_unit_countries ORDER BY name';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) )? $list : 0;
  }
  public static function save_visit( $user_id, $project_id, $company_id ){
    if( self::get( 'shared.user_visits', 0, 'user_id='.$user_id.' AND project_id='.$project_id.' AND company_id='.$company_id ) )
      self::query( 'UPDATE shared.user_visits SET visits=visits+1 WHERE user_id='.$user_id.' AND project_id='.$project_id.' AND company_id='.$company_id );
    else
      self::save( 'shared.user_visits', [['user_id', $user_id, 'i'], ['project_id', $project_id, 'i'], ['company_id', $company_id, 'i']] );
  }

  
 
  public static function save_reg( $data, $reg_id = 0 ){
    if( !$reg_id )
      $data = $data + [['ip', $_SERVER['REMOTE_ADDR']], ['utitkelt', date('Y-m-d H:i:s')]];
    return self::save( 'shared.reg', $data, ( $reg_id )? ['reg_id', $reg_id, 'i'] : 0 );
  }
  public static function list_reg(){
    $sql = 'SELECT * FROM shared.reg ORDER BY reg_id';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }
  public static function list_staff(){
    $sql = 'SELECT r.reg_id, r.neve, r.nevek, rm.jog, rm.utitkelt FROM shared.reg r
            INNER JOIN shared.regmodul rm ON r.reg_id=rm.reg_id
            WHERE rm.projekt_id='.$_SESSION['PROID'].' AND rm.ceg_id='.$_SESSION['REGCEG'].'
            AND MID(rm.jog, 1, 1)<>"" AND MID(rm.jog, 1, 1)<>"0" ORDER BY rm.jog, r.neve, r.nevek';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }
  // === regtax
  public static function get_regtax( $reg_id, $projekt_id, $ceg_id = 0 ){
    $sql = 'SELECT * FROM shared.regtax WHERE reg_id=? AND projekt_id=? AND ceg_id=? LIMIT 1';
    $res = self::query( $sql, [$reg_id, $projekt_id, $ceg_id], 'iii' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function save_regtax( $data, $regtax_id = 0 ){
    return self::save( 'shared.regtax', $data, ( $regtax_id )? ['regtax_id', $regtax_id, 'i'] : 0 );
  }
  
  public static function list_regmodulReg( $reg_id, $projekt_id ){
    $sql = 'SELECT * FROM shared.regmodul WHERE reg_id=? AND projekt_id=? ORDER BY ceg_id';
    $res = self::query( $sql, [$reg_id, $projekt_id], 'ii' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) )? $list : 0;
  }
  public static function get_regmodul( $data ){
    if( count( $data ) == 3 ){
      $sql = 'SELECT * FROM shared.regmodul WHERE reg_id=? AND projekt_id=? AND ceg_id=? LIMIT 1';
      $res = self::query( $sql, $data, 'iii' )->get_result()->fetch_assoc();
    }else{
      $sql = 'SELECT * FROM shared.regmodul WHERE reg_id=? AND projekt_id=? ORDER BY ceg_id LIMIT 1';
      $res = self::query( $sql, $data, 'ii' )->get_result()->fetch_assoc();
    }
    return $res;
  }
  public static function get_authModulDb( $data ){
    $sql = 'SELECT regmodul_id, jog, ceg_id FROM shared.regmodul WHERE reg_id=? AND projekt_id=? ORDER BY ceg_id';
    if( $res = self::query( $sql, $data, 'ii' )->get_result() ){
      $_SESSION['REGCEGDB'] = $res->num_rows;
      $rec = $res->fetch_assoc();
      $res->close();
      $res = $rec ?? 0;
    }
    return $res;
  }
  public static function get_regmodul1( $reg_id, $projekt_id = 0, $ceg_id = 0 ){
    $sql = 'SELECT * FROM shared.regmodul WHERE reg_id=?';
    $types = 'i';
    $data[] = $reg_id;
    if( $projekt_id ){
      $types.= 'i';
      $sql.=' AND projekt_id=?';
      $data[] = $projekt_id;
      if( $ceg_id ){
        $types.= 'i';
        $sql.=' AND ceg_id=?';
        $data[] = $ceg_id;
      }
    }
    $sql.= ' LIMIT 1';
    $res = self::query( $sql, $data, $types )->get_result()->fetch_assoc();
    return $res;
  }
  public static function save_regmodul( $data, $regmodul_id = 0 ){
    return self::save( 'shared.regmodul', $data, ( $regmodul_id )? ['regmodul_id', $regmodul_id, 'i'] : 0 );
  }
  public static function save_authModul( $data ){
    $sql = 'INSERT INTO shared.regmodul SET reg_id=?, projekt_id=?, utitkelt="'.date('Y-m-d H:i:s').'"';
    $res = self::query( $sql, $data, 'ii' );
    return $res;
  }
  public static function save_authModulUp( $regmodul_id ){
    $sql = 'UPDATE shared.regmodul SET utitkelt="'.date('Y-m-d H:i:s').'" WHERE regmodul_id=?';
    $res = self::query( $sql, [$regmodul_id], 'i' );
    return $res;
  }
  public static function save_authLog( $data ){
    $sql = 'INSERT INTO shared.reglog SET reg_id=?, projekt_id=?, ip="'.$_SERVER['REMOTE_ADDR'].'"';
    $res = self::query( $sql, $data, 'ii' );
    return $res;
  }
  
  
  public static function get_authCegLink( $link ){
    if( substr( $link, 0, 4 ) == 'http')
      $sql = 'SELECT * FROM shared.regceg WHERE apidomain=? LIMIT 1';
    else
      $sql = 'SELECT * FROM shared.regceg WHERE clink=? LIMIT 1';
    $res = self::query( $sql, [ $link ] )->get_result()->fetch_assoc();
    return $res;
  }
  

  public static function get_authRegTax( $reg_id ){
    $sql = 'SELECT * FROM shared.regtax WHERE reg_id=? LIMIT 1';
    $res = self::query( $sql, [$reg_id], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
}