<link href="/shared/js/spritzer/ui/ui-library.css" rel="stylesheet">
<link href="/shared/js/spritzer/ui/ui-upload.css" rel="stylesheet">
<header>
  <h6>Szolgáltatás</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_service">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <input type="hidden" name="image_id" value="<?= $_POST['image_id'] ?>">
    <input type="hidden" name="selects" value='<?= ( $_POST['selects'] ?? 0 )? json_encode( $_POST['selects'] ) : '[]' ?>'>
    <ul class="formbox">
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="status" placeholder="">
          <option value="1"<?=( $_POST['status'] == '1' )? ' selected' : '' ?>>Nem látható</option>  
          <option value="K"<?=( $_POST['status'] == 'K' )? ' selected' : '' ?>>Adat bekérő</option>
          <option value="T"<?=( $_POST['status'] == 'T' )? ' selected' : '' ?>>Csak tájékoztat</option>
        </select>
        <label>Állapot</label>
      </li>
      <li class="form col5">
        <input type="text" name="sequence" placeholder="" value="<?= $_POST['sequence'] ?? '' ?>">
        <label>Sorrend</label>
      </li>
      <li class="form col0">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?? '' ?>">
        <label>Megnevezés</label>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="context_night" placeholder="">
          <option value="0"<?=( $_POST['context_night'] == '0' )? ' selected' : '' ?>>Nincs</option>  
          <option value="E"<?=( $_POST['context_night'] == 'E' )? ' selected' : '' ?>>Egyezik</option>
          <option value="M"<?=( $_POST['context_night'] == 'M' )? ' selected' : '' ?>>Max érték</option>
        </select>
        <label>Éjszakával kapcsolat</label>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="context_guest" placeholder="">
          <option value="0"<?=( $_POST['context_guest'] == '0' )? ' selected' : '' ?>>Nincs</option>  
          <option value="E"<?=( $_POST['context_guest'] == 'E' )? ' selected' : '' ?>>Egyezik</option>
          <option value="M"<?=( $_POST['context_guest'] == 'M' )? ' selected' : '' ?>>Max érték</option>
        </select>
        <label>Létszámmal kapcsolat</label>
      </li>
      <li class="form col5">
        <input type="text" name="price" placeholder="" value="<?= $_POST['price'] ?? '' ?>">
        <label>Ár</label>
      </li>
      <li class="form col5">
        <input type="text" name="unit" placeholder="" value="<?= $_POST['unit'] ?? '' ?>">
        <label>Mértékegység</label>
      </li>
      <li class="form col0">
        <input type="text" name="link" placeholder="" value="<?= $_POST['link'] ?? '' ?>">
        <label>Külső link ahová léphetnek</label>
      </li>
    </ul>
    <hr>
    <b>Választható lehetőségek</b>
    <div class="selects-box">
    <?php if( count( $_POST['selects'] ) ){ ?>
      <?php foreach( $_POST['selects'] as $key => $element ){ ?>
      <ul class="formbox selects-element">
        <li class="form col5">
          <input type="text" placeholder="" value="<?= $element['text']?>" disabled>
          <label>Szöveg</label>
        </li>
        <li class="form col2">
          <input type="text" placeholder="" value="<?= $element['price']?>" disabled>
          <label>Ár</label>
        </li>
        <li class="form col3">
          <button class="select-del" data-selectid="<?= $key ?>" name="btn_del" title="Választható tétel törlése">Töröl</button>
        </li>
      </ul>
      <?php } ?>
    <?php } ?>
    </div>
    <ul class="formbox">
      <li class="form col5">
        <input type="text" name="select_text" placeholder="">
        <label>Szöveg</label>
      </li>
      <li class="form col2">
        <input type="text" name="select_price" placeholder="">
        <label>Ár</label>
      </li>
      <li class="form col3">
        <button class="select-add" name="btn_add" title="Választható tétel hozzáadása">Hozzáad</button>
      </li>
    </ul>
    <hr>
    <ul class="formbox">
      <li class="form col0">
        <textarea name="description"><?= $_POST['description'] ?? '' ?></textarea>
        <label>Leírás</label>
      </li>
    </ul>
    <b>Kép</b>
    <ul data-upload='{"maxdb": 1, "tableName": "<?= DATABASENAME ?>.services", "tableId": <?= $_POST['id'] ?>, "imageId": <?= $_POST['image_id'] ?>}'>
      <li>
        <span class="box__icon" style="--icon:var(--icon-upload)"></span>
        <input class="box__input" id="foto" type="file" name="foto[]" data-multiple-caption="{count} kiválasztott fájl">
        <label for="foto">
          <strong>Válassz<?= ( 0 )? ' akár több' : ' egy' ?> fájlt</strong>
          <span class="box__dragndrop"> vagy húzd ide</span>
        </label>
      </li>
      <li class="box__uploading">Feltöltés&hellip;</li>
      <li class="box__success">Kész! <a href="#" class="box__restart" role="button">További fájlok?</a></li>
      <li class="box__error">Hiba! <span></span>. <a href="#" class="box__restart" role="button">Próbáld újra!</a></li>
      <li class="form col0" data-library="">
        <div class="only-one-image">
          <?php if( ( $_POST['image_id'] ?? 0 ) and file_exists( 'upload/'.$_SESSION['COMPANY'].'/pic'.sprintf( "%04d", $_POST['image_id'] ).'.jpg' ) ){ ?>
          <img src="/upload/<?= $_SESSION['COMPANY'] ?>/pic<?= sprintf( "%04d", $_POST['image_id'] ).'.jpg?'.md5( date( "YmdHis" ) ) ?>">
          <?php } ?>
        </div>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Szolgáltatás mentése">Módosít</button>
</footer>