<header>
  <h6><PERSON><PERSON><PERSON><PERSON><PERSON></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_period">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <input type="hidden" name="periodDays" value='<?= json_encode( $_POST['periodDays'] ) ?>'>
    <ul class="formbox">
      <li class="form col3">
        <input type="text" name="priority" placeholder="" value="<?= $_POST['priority'] ?>"<?= ( $_POST['priority'] )? '' : ' readonly' ?>>
        <label>Sorrend</label>
      </li>
      <li class="form col7">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?? '' ?>">
        <label>Megnevezés</label>
      </li>
      <?php if( count( $_POST['periodDays'] )){ ?>
      <?php   foreach( $_POST['periodDays'] as $key => $day ){ ?>
      <li class="form col5" style="--toicon:var(--icon-trash-empty)">
        <input type="text" placeholder="" value="<?= $day ?>" disabled>
        <label>Nap(ok)</label>
        <div class="del-days" data-key="<?= $key ?>" title="Nap(ok) törlése"></div>
      </li>
      <?php } } ?>
      <li class="form col5" style="--toicon:var(--icon-plus-circled)">
        <input type="text" placeholder="" value="">
        <label>Nap(ok)</label>
        <div class="add-days" title="Nap(ok) hozzáadása"></div>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Időszak mentése">Módosít</button>
</footer>