<header>
  <h6>Ú<PERSON></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_uj_foglalas">
    <ul class="formbox">
      <li class="form col4">
        <input type="date" name="nap1" placeholder="" value="<?= date( 'Y-m-d' ) ?>">
        <label>Érkezés*</label>
      </li>
      <li class="form col4">
        <input type="date" name="nap2" placeholder="" value="<?= date( 'Y-m-d', strtotime( '+1 day' ) ) ?>">
        <label>Távozás*</label>
      </li>
      <li class="form col2">
        <input type="text" name="ej" placeholder="" value="1" pattern="[1-9]\d{1,2}" maxlength="2">
        <label>Éj</label>
      </li>
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="csatorna">
          <?php foreach( response::$vw->view->csatornak as $csatorna ){ ?>
          <option value="<?= $csatorna->id ?>"><?= $csatorna->type ?> - <?= $csatorna->name ?></option>
          <?php } ?>
        </select>
        <label>Foglalási csatorna</label>
      </li>
      <li class="form col0">
        <input type="text" name="kapcsolattarto" id="kapcsolattarto" placeholder="" value="<?= $_POST['kapcsolattarto'] ?? ''?>">
        <label for="kapcsolattarto">Kapcsolattartó</label>
      </li>
      <li class="form col0">
        <input type="text" name="email" placeholder="" value="<?= $_POST['email'] ?? ''?>">
        <label>Email</label>
      </li>
      <li class="form col0">
        <input type="text" name="telefon" placeholder="" value="<?= $_POST['telefon'] ?? ''?>">
        <label>Telefon</label>
      </li>
      <li class="form col0">
        <input type="text" name="megjegyzes" placeholder="" value="<?= $_POST['megjegyzes'] ?? ''?>">
        <label>Megjegyzés</label>
      </li>
      <li class="form col2" style="min-width: 56px">
        <input type="text" name="vendegfo" placeholder="" value="<?= $_POST['vendegfo'] ?? ''?>">
        <label>Σ fő</label>
      </li>
      <li class="form col4 bgonto" data-to="HUF">
        <input type="text" name="fizetendo" placeholder="" value="<?= $_POST['fizetendo'] ?? ''?>">
        <label>Fizetendő</label>
      </li>
      <li class="form col4 bgonto" data-to="HUF">
        <input type="text" name="eloleg" placeholder="" value="<?= $_POST['eloleg'] ?? ''?>">
        <label>Előleg</label>
      </li>
      <?php if( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 ){ ?>
        <li class="col0 tec">
          <span>Lakóegységek*</span>
        </li>
        <?php foreach( response::$vw->view->szabadlakoegysegek as $lakoegyseg ){ ?>
        <li class="form col5 cheaft" style="text-align:right">
          <input
            type="checkbox"
            id="le<?= $lakoegyseg->id ?>"
            name="lakoegysegek[]" 
            value="<?= $lakoegyseg->id ?>"
            <?= ( count( response::$vw->view->szabadlakoegysegek ) == 1 )? ' checked' : '' ?>
          >
          <label for="le<?= $lakoegyseg->id ?>"><?= $lakoegyseg->name ?></label>
        </li>
      <?php } }else{ ?>
        <li><input type="hidden" name="lakoegysegek[]" value="<?= response::$vw->view->szabadlakoegysegek[0]->id ?>"></li>
      <?php } ?>
    </ul>
  </form>
</section>
<footer>
  <button class="callback" name="btn_mentes" title="Új foglalás adatainak mentése">Mentés</button>
  <button class="close">Kilép</button>
</footer>