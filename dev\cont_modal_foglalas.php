<?php
response::add( 'view', 'countries', model_basic::list_countries( 'code2, name' ));
$szabadlakoegysegek = db::list( 'accommodation_units', 'company_id='.$_SESSION['COMPANY'].' AND MID(signs,1,1)=2', 0, 0, 0, 'id, name' );
response::add( 'view','szabadlakoegysegek', $szabadlakoegysegek );
if( $foglalas = db::get_foglalas( http::$route[3] )){
  response::add( 'view', 'foglalas', $foglalas );
  $_POST['foglalva'] = $foglalas['foglalva'];
  $_POST['csatorna_id'] = $foglalas['csatorna_id'];
  $_POST['kapcsolattarto'] = $foglalas['kapcsolattarto'];
  $_POST['email'] = $foglalas['email'];
  $_POST['telefon'] = $foglalas['telefon'];
  $_POST['megjegyzes'] = $foglalas['megjegyzes'];
  $_POST['vendegfo'] = $foglalas['vendegfo'];
  $_POST['fizetendo'] = $foglalas['fizetendo'];
  $_POST['eloleg'] = $foglalas['eloleg'];
  $_POST['fizetve'] = db::get_fizetve( http::$route[3] )['fizetve'] ?? 0;
  if( $foglalas['invoice_info'] ){
    $invoice_info = json_decode( $foglalas['invoice_info'] );
    $_POST['inv_name'] = $invoice_info->name ?? '';
    $_POST['inv_countryCode'] = $invoice_info->countryCode ?? 'HU';
    $_POST['inv_zip'] = $invoice_info->zip ?? '';
    $_POST['inv_city'] = $invoice_info->city ?? '';
    $_POST['inv_address'] = $invoice_info->address ?? '';
    $_POST['inv_taxNumber'] = $invoice_info->taxNumber ?? '';
    $_POST['inv_communityVatNumber'] = $invoice_info->communityVatNumber ?? '';
  }
  if( $foglallakoegysegek = db::list_foglallakoegyseg( http::$route[3] )){
    response::add( 'view', 'foglallakoegysegek', $foglallakoegysegek );
    $_SESSION['NAP'] = $foglallakoegysegek[0]['erkezes'];
  }
  if( $foglalszolgaltatasok = db::list_foglalszolgaltatas( http::$route[3] ))
    response::add( 'view','foglalszolgaltatasok', $foglalszolgaltatasok );
  if( $penz = db::list_penz( http::$route[3] ))
    response::add( 'view', 'fizetesek', $penz );

  function table_fizetesek(){
    if( $fizetesek = db::list_penz( http::$route[3] )){
      foreach( $fizetesek as $key => $row ){
        $fizetesek[$key]['osszeg'] = number_format( $row['osszeg_huf'], 0, '.', ' ' ).' HUF';
        if( $row['osszeg_mas'] > 0 )
        $fizetesek[$key]['osszeg'].= '<br>('.number_format( $row['osszeg_mas'], 2, '.', ' ' ).' '.$row['penznem'];
      }
    }
    return $fizetesek;
  }
  function table_foglalszolgaltatasok(){
    if( $foglalszolgaltatasok = db::list_foglalszolgaltatas( http::$route[3] ))
      foreach( $foglalszolgaltatasok as $key => $row ){
        $foglalszolgaltatasok[$key]['ar'] = number_format( $row['ar'], 0, '.', ' ' ).' HUF';
      }
    return $foglalszolgaltatasok;
  }
}