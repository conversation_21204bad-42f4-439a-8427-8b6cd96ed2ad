<?php
/**
 * Futási hiba kezel<PERSON>, nyomkövető
 *
 * Nyomkövetés lehetősége:
 * @see config_errors.php
 *
 * @method `function start();` Hibake<PERSON><PERSON> indítása (átveszi a hibakezelést)
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2019, <PERSON><PERSON>cz<PERSON>
 *
 * @version 1.4.0
 * @since 1.4.0 2025.03.23 INFO kategória bevezetése DEBUG-nál. Felesleges log üzenet elhagyása, csak a kért info jelenik meg
 * @since 1.3.0 2022.05.10 verziózás bevezetése a fájlnévben. init() helyett start() autoload-nél ne induljon. Optimalizálás
 * @since 1.2.0 2022.04.09 var_export() használata args tömb és objektum esetén; message-ben {} cseréje ||
 * @since 1.1.0 2021.09.11 Felhasználói szűrés lehetősége IP alapján. DEPRECATED külön kategória fájlba kerül
 * @since 1.0.1 2019.02.14 Kód optimalizálás
 * 
 * 
 * 3. Globális kivételhierarchia definiálása
 * Strukturáld a hibákat egyértelmű kivételhierarchiával :
 * 
 * AppException
 * DatabaseException
 * ValidationException
 * ApiException
 * AuthenticationException
 * 
 * 12. Szabványosítsd a hibaválaszokat az API-kban
 * REST API-k esetén mindig konzisztens hibakódokat adjon vissza:
 * {
 *   "error": {
 *     " code ": 401 ,
 *     "message" : "Jogosulatlan" ,
 *     "details" : "A token lejárt"
 *   }
 *}
 */
 
class errors{
  private static $projectId = 0; /**< projekt azonosítás, mert $_SESSION['PROID'] csak később keletkezik a futás során */
  private static $number = 0;    /**< munkameneten belüli sorszám */

  /** Hiba logolás fájlba
   * 
   */
  private static function logError( $error ){
    require_once 'config_errors.php';
    if( isset($error['trace'] ) and $error['trace'] ){
      $trace = '[';
      foreach ( $error['trace'] as $key => $stackPoint ){  
        if( isset( $stackPoint['args'] ))
          foreach( $stackPoint['args'] as $key => $value )
            if( is_array( $value ) or is_object( $value )) $stackPoint['args'][$key] = var_export( $value );
        if( $trace != '[') $trace .= ',';
        $trace.= '{';
        $trace.=    ((!isset($stackPoint['file']))? '' : ((substr($trace,-1) == '{')? '' : ',').
                    '"file": "'.$stackPoint['file'].'"').
                    ((!isset($stackPoint['line']))? '' : (($trace == '{')? '' : ',').
                    '"line": '.$stackPoint['line']).
                    ((!isset($stackPoint['function']))? '' : (($trace == '{')? '' : ',').
                    '"function": "'.$stackPoint['function'].'"').
                    ((!isset($stackPoint['args']))? '' : (($trace == '{')? '' : ',').
                    '"args": "'.implode( ",", $stackPoint['args'] ).'"').
                 '}';
        
      }
      $trace.= ']';
      $error['trace'] = $trace;
    }else unset( $error['trace'] );
    self::$number++;
    $log = '{ "project": '.self::$projectId.','.
             '"num": "'.self::$number.'",'.
             '"when": "'.date( 'Y-m-d H:i:s' ).'",'.
             '"type": '.$error['type'].','.
             (( $error['file'] ?? 0 )? '"file": "'.$error['file'].'",' : '' ).
             (( $error['line'] ?? 0 )? '"line": '.$error['line'].',' : '' ).
             (( $error['method'] ?? 0 )? '"method": "'.$error['method'].'",' : '' ).
             '"message": "'.preg_replace("/\r|\n/", " ", $error['message']).'"'.
             (( $error['trace'] ?? 0 )? ',"trace": '.$error['trace'] : '' ).
           '}';

    switch( $error['type'] ){
      case E_ERROR:
      case E_CORE_ERROR:
      case E_COMPILE_ERROR:
      case E_USER_ERROR:
      case E_RECOVERABLE_ERROR:
      case E_PARSE:
        $category = 'FATAL';
      break;
      case E_WARNING:
      case E_CORE_WARNING:
      case E_COMPILE_WARNING:
      case E_USER_WARNING:
        $category = 'WARNING';
      break;
      case E_DEPRECATED:
      case E_USER_DEPRECATED:
        $category = 'DEPRECATED';
      break;
      case E_NOTICE:
      case E_STRICT:
        $category = 'NOTICE';
      break;
      case E_USER_NOTICE:
        $category = 'DEBUG';
      break;
      default:
        $category = 'NOTICE';
    }
    $timeblock = ( defined( 'LOGINTERVAL' ))? date( LOGINTERVAL ) : '';

    if( $category == 'DEBUG' and substr( $error['message'], 0, 4 ) == 'INFO' ){
      $category = 'INFO';
      $log = '{ "project": '.self::$projectId.','.
               '"when": "'.date( 'Y-m-d H:i:s' ).'",'.
               (( $error['file'] ?? 0 )? '"file": "'.$error['file'].'",' : '' ).
               (( $error['line'] ?? 0 )? '"line": '.$error['line'].',' : '' ).
               preg_replace( "/\r|\n/", " ", substr( $error['message'], 4 )).'"'.
             '}';
    }

    if( $category == 'DEBUG' and substr( $error['message'], 0, 9 ) == 'DB QUERY:' ){
      $category = 'DBQUERIES';
      $log = '{ "project": '.self::$projectId.','.
               '"when": "'.date( 'Y-m-d H:i:s' ).'",'.
               (( $error['file'] ?? 0 )? '"file": "'.$error['file'].'",' : '' ).
               (( $error['line'] ?? 0 )? '"line": '.$error['line'].',' : '' ).
               preg_replace( "/\r|\n/", " ", $error['message'] ).'"'.
             '}';
    }

    $dev = '';
    if( defined( 'DEVELOPERS' ))
      foreach( DEVELOPERS as $developer )
        if(( $_SERVER['REMOTE_ADDR'] ?? 0 ) and $_SERVER['REMOTE_ADDR'] == $developer['ip'] )
          $dev = $developer['signo'].'_';
    error_log( $log."\n", 3, __DIR__.'/../log/'.$dev.$category.'_'.$timeblock.'.txt' );
  }

  /** Hibakezelő beállítása
   * 
   * @param integer $projectId Ha van projekt azonosítás, akkor annak kódja
   */
  public static function start( $projectId = NULL ){
    set_error_handler( function( $num, $str, $file, $line ){
      if( in_array( $num, [E_USER_ERROR, E_RECOVERABLE_ERROR] ) and error_reporting())
        throw new ErrorException( $str, 0, $num, $file, $line );
      elseif( error_reporting()){
        $error['type']    = $num;
        $error['file']    = $file;
        $error['line']    = $line;
        $error['message'] = $str;
        $error['trace']   = array_reverse( debug_backtrace());
        array_pop( $error['trace'] );
        if( count( $error['trace'] ) == 0) $error['trace'] = 0;
        self::logError( $error );
      };
    } );
    set_exception_handler( function( $exception ){
      $error['method']  = __METHOD__;
      $error['type']    = E_ERROR;
      $error['file']    = $exception->getFile();
      $error['line']    = $exception->getLine();
      $error['message'] = $exception->getMessage().$exception->getTraceAsString();
      $error['trace']   = $exception->getTrace();
      self::logError( $error );
    } );
    ini_set( 'log_errors', true );
    self::$projectId = $projectId ?? 0;
  }
}