<?php
if( $_GET['create'] ?? 0 )
  db::save( DATABASENAME.'.children_rules', [['company_id', $_SESSION['COMPANY'], 'i']] );

if( $_POST['btn_mentes'] ?? 0 ){
  http::cleanPost();
  if( !( response::$vw->error ?? 0 )){
    setup::$company->configuration->children_rule_description = $_POST['children_rule_description'] ?? '';
    setup::save_configuration( $_SESSION['COMPANY'], setup::$company->configuration );
    db::save(
      DATABASENAME.'.children_rules',
      [
        ['age', $_POST['age'], 'i'],
        ['price', ( $_POST['is_price'] )? $_POST['price'] : null, 'i'],
        ['cot', ( $_POST['is_cot'] )? $_POST['cot'] : null, 'i'],
        ['extra_bed', ( $_POST['is_extra_bed'] )? $_POST['extra_bed'] : null, 'i']
      ],
      intval( $_POST['id'] )
    );
  }
}

$children_rules = db::list( DATABASENAME.'.children_rules', 'company_id='.$_SESSION['COMPANY'] ); 
response::add( 'view', 'children_rules', $children_rules );