<?php
date_default_timezone_set( 'Europe/Budapest' );
const CLASSVERSIONS = [
  'errors'      => '1.4',
  'http'        => '2.2',
  'mysql'       => '2.2'
];
CONST DATABASENAME = 'tren';
require_once 'shared/class/autoload.php';
errors::start( 37 );

if(( $_GET ?? 0 ) and count( $_GET )){
  $_GET = http::cleanGet();
  switch(  array_keys( $_GET )[0] ){
    case 'pixel':
      $sent_id = intval( substr( $_GET['pixel'], 1 ));
      if( $sent_id and $sent = db::get( 'shared.mail_sents', $sent_id )){
        if( !( $sent['viewed'] ?? 0 )){
          
          // User-Agent elemzés
          $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
          $client_type = isRealEmailOpen( $user_agent );
          
          // HTTP fejlécek elemzése
          //$header_analysis = analyzeHttpHeaders();
          
          // Időzítés és mintázatok vizsgálata (utólagos elemzéshez)
          //$pattern = detectSuspiciousOpenPatterns( $sent_id, $sent['campaign_id'] );
          //if( $pattern !== 'likely_genuine' )
          //  db::save( 'shared.mail_sents', [['pattern_flag', $pattern]], $sent_id );

          if(( time() - strtotime( $sent['sending'] ) > 3 ) and $client_type !== false ){
            user_error( 'INFOuser_agent: '.$client_type.' '.$user_agent );
            $data = [
              ['viewed', date( 'Y-m-d H:i:s' )],
              ['used_ip', $_SERVER['REMOTE_ADDR']],
              ['used_device', $user_agent]
            ];
          
            // Ha gyanús a megnyitás, jelöljük meg
            //if( $client_type === false or $header_analysis['suspicious'] ){
            //  $data[] = ['open_type', 'suspicious'];
            //  $data[] = ['open_notes', json_encode([
            //    'client_type' => $client_type,
            //    'header_analysis' => $header_analysis['reasons'] ?? []
            //  ])];
            //}else
            //  $data[] = ['open_type', 'genuine'];
          
            db::save( 'shared.mail_sents', $data, $sent_id );
          }
        }
      }
      
      // Mindenképp küldjük vissza a képet
      $image = match( $_GET['pixel'][0] ){
        'A' => 'items/tren-all-in-one.png'
      };
      header( 'Content-Type: image/png' );
      readfile( $image );
      exit;
    break;
    case 'link':
      list( $sent_id, $link ) = explode( ',', $_GET['link'] );
      $sent_id = intval( $sent_id );
      if( $sent_id and $sent = db::get( 'shared.mail_sents', $sent_id ))
        if( !( $sent['visited'] ?? 0 ))
          db::save( 'shared.mail_sents', [['visited', date( 'Y-m-d H:i:s' )],['viewed_ip', $_SERVER['REMOTE_ADDR']],['viewed_device', $_SERVER['HTTP_USER_AGENT']]], $sent_id );
      header( "HTTP/1.1 301 Moved Permanently" );
      header( "location: https://" . $link . '?TM' . $sent_id );
      header( "Status: 301 Moved Permanently" );
      exit;
    break;
  }
}

/**
 * Ellenőrzi, hogy valódi email megnyitásról van-e szó a User-Agent alapján
 * 
 * @param string $user_agent A HTTP User-Agent fejléc
 * @return mixed true: valódi email kliens, false: bot/crawler, 'unknown': nem egyértelmű
 */
function isRealEmailOpen($user_agent) {
    // Email kliensek jellemző User-Agent stringjei
    $email_clients = [
        'Thunderbird', 'Outlook', 'Apple Mail', 'iPhone Mail', 'Gmail',
        'YahooMailProxy', 'MSFT-SmtpSvc', 'Microsoft Office', 'Microsoft Outlook',
        'Mozilla/5.0 (Windows NT', 'Mozilla/5.0 (Macintosh', 'Mozilla/5.0 (iPhone'
    ];
    
    // Ismert crawler-ek és előnézeti rendszerek
    $known_bots = [
        'Googlebot', 'bingbot', 'Yahoo! Slurp', 'Baiduspider', 'YandexBot',
        'facebookexternalhit', 'Twitterbot', 'WhatsApp', 'Slack',
        'GoogleImageProxy', 'Proxy', 'crawler', 'spider', 'bot'
    ];
    
    // Bot detektálás
    foreach( $known_bots as $bot )
      if( stripos( $user_agent, $bot ) !== false )
        return false; // Bot által generált kérés
    
    // Email kliens detektálás
    foreach( $email_clients as $client )
      if( stripos( $user_agent, $client ) !== false )
        return true; // Valószínűleg valódi email megnyitás
    
    // Ha nem egyértelmű, további elemzés szükséges
    return 'unknown';
}

/**
 * Gyanús megnyitási mintázatok felismerése
 * 
 * @param int $sent_id Az email küldés azonosítója
 * @param int $campaign_id A kampány azonosítója
 * @return string A felismert mintázat vagy 'likely_genuine' ha nem gyanús
 */
function detectSuspiciousOpenPatterns( $sent_id, $campaign_id ){
  // 1. Ellenőrizzük, hogy a kép betöltése túl gyorsan történt-e a küldés után
  $sent_data = db::get( 'shared.mail_sents', $sent_id );
  if( $sent_data ){
    $sent_time = strtotime( $sent_data['created'] );
    $open_time = strtotime( $sent_data['viewed'] );
    
    // Ha a megnyitás kevesebb mint 3 másodperccel a küldés után történt, gyanús
    if(( $open_time - $sent_time) < 3 )
      return 'suspicious_timing';
  }

  // 2. Ellenőrizzük, hogy ugyanaz az IP cím sok emailt nyitott-e meg rövid idő alatt
  $recent_opens = db::list(
    'shared.mail_sents',
    "campaign_id = $campaign_id AND used_ip = '{$sent_data['used_ip']}' AND viewed > DATE_SUB(NOW(), INTERVAL 5 MINUTE)",
    0,0,0,
    'COUNT(*) as open_count'
  );
  
  if( $recent_opens[0]['open_count'] > 10 )
    return 'suspicious_batch_open';
  
  // 3. Ellenőrizzük, hogy az IP cím ismert proxy vagy VPN-e
  $suspicious_ips = [
      // Ismert proxy/VPN IP címek listája
      // Ezt a listát rendszeresen frissíteni kell vagy API-t használni
  ];
  
  if( in_array( $sent_data['used_ip'], $suspicious_ips ))
    return 'suspicious_ip';
  
  // 4. Ellenőrizzük, hogy a megnyitás időpontja szokatlan-e (pl. éjszaka)
  $hour = date( 'H', $open_time );
  if( $hour >= 1 && $hour <= 5 ) // Hajnali 1-5 óra között
    return 'suspicious_time_of_day';

  return 'likely_genuine';
}

/**
 * HTTP fejlécek elemzése a hamis megnyitások felismeréséhez
 * 
 * @return array Az elemzés eredménye (suspicious: bool, reasons: array)
 */
function analyzeHttpHeaders(){
  $headers = getallheaders();
  $is_suspicious = false;
  $reasons = [];
  
  // Referrer ellenőrzése
  if( isset( $headers['Referer'] )){
    $referer = $headers['Referer'];
    // Ha a referrer egy webmail szolgáltatás, valószínűleg valódi megnyitás
    if( strpos( $referer, 'mail.google.com' ) !== false or strpos( $referer, 'outlook.live.com' ) !== false )
      return ['suspicious' => false, 'reason' => 'webmail_referer'];
    
    // Ha a referrer egy keresőmotor vagy közösségi oldal, valószínűleg nem email megnyitás
    if( strpos( $referer, 'google.com/search' ) !== false or strpos( $referer, 'facebook.com' ) !== false ){
      $is_suspicious = true;
      $reasons[] = 'non_email_referer';
    }
  }
  
  // Accept header ellenőrzése
  if( isset( $headers['Accept'] ))
    // Email kliensek általában specifikus Accept headereket használnak
    if( strpos( $headers['Accept'], 'image/webp' ) === false and strpos( $headers['Accept'], 'image/png' ) === false ){
      $is_suspicious = true;
      $reasons[] = 'unusual_accept_header';
    }
  
  // Accept-Language ellenőrzése
  if( isset( $headers['Accept-Language'] )){
    // Ha nincs Accept-Language header, az gyanús lehet
    if( empty( $headers['Accept-Language'] )){
      $is_suspicious = true;
      $reasons[] = 'missing_accept_language';
    }
  }else{
    $is_suspicious = true;
    $reasons[] = 'missing_accept_language';
  }
  
  // User-Agent konzisztencia ellenőrzése
  if( isset( $headers['User-Agent'] ) and isset( $_SERVER['HTTP_USER_AGENT'] ))
    if( $headers['User-Agent'] !== $_SERVER['HTTP_USER_AGENT'] ){
      $is_suspicious = true;
      $reasons[] = 'inconsistent_user_agent';
    }
  
  // Cache-Control ellenőrzése
  if( isset( $headers['Cache-Control'] ))
    // Böngészők általában küldenek Cache-Control headert
    // Email kliensek nem mindig
    if( strpos( $headers['Cache-Control'], 'no-cache') !== false ){
        // Ez lehet böngésző vagy email kliens is, nem egyértelmű
    }
  
  // X-Forwarded-For ellenőrzése
  if( isset( $headers['X-Forwarded-For'] )){
    // Ha van X-Forwarded-For header, az proxy használatára utalhat
    $is_suspicious = true;
    $reasons[] = 'proxy_detected';
  }
  
  return ['suspicious' => $is_suspicious, 'reasons' => $reasons];
}

/**
 * Időzóna-független dátum generálás MySQL-hez
 * 
 * @return string MySQL kompatibilis dátum/idő string
 */
function getMySQLDateTime() {
  // Létrehozunk egy DateTime objektumot a helyi időzónában
  $date = new DateTime();
  
  // Beállítjuk a MySQL által használt időzónára
  $date->setTimezone(new DateTimeZone('Europe/Budapest'));
  
  // Visszaadjuk a megfelelő formátumban
  return $date->format('Y-m-d H:i:s');
}

/**
 * Időzóna diagnosztika
 * 
 * Ez a függvény segít azonosítani az időzóna problémákat
 */
function diagnoseTimezoneProblem() {
    $output = [];
    
    // PHP időzóna információk
    $output[] = "PHP időzóna beállítás: " . date_default_timezone_get();
    $output[] = "PHP aktuális idő: " . date('Y-m-d H:i:s');
    $output[] = "PHP timestamp: " . time();
    
    // Rendszer időzóna
    $output[] = "Rendszer időzóna: " . exec('date +%Z');
    $output[] = "Rendszer idő: " . exec('date');
    
    // MySQL időzóna információk
    $result = db::query("SELECT @@global.time_zone, @@session.time_zone, NOW(), UNIX_TIMESTAMP()");
    if ($row = $result->fetch_row()) {
        $output[] = "MySQL global időzóna: " . $row[0];
        $output[] = "MySQL session időzóna: " . $row[1];
        $output[] = "MySQL aktuális idő: " . $row[2];
        $output[] = "MySQL timestamp: " . $row[3];
    }
    
    return implode("\n", $output);
}

/**
 * Email küldés tracking pixellel
 * 
 * @param string $to Címzett email címe
 * @param string $subject Email tárgya
 * @param string $body Email tartalma (HTML)
 * @param int $campaign_id Kampány azonosító
 * @return bool Sikeres küldés
 */
function sendEmailWithTracking($to, $subject, $body, $campaign_id) {
    // 1. Mentsük el az email küldést az adatbázisba
    $sent_id = db::save('shared.mail_sents', [
        ['campaign_id', $campaign_id, 'i'],
        ['fromto', $to],
        ['created', getMySQLDateTime()]
    ]);
    
    // 2. Készítsünk egyedi tracking pixelt
    $pixel_code = 'A' . $sent_id; // 'A' prefix + sent_id
    $tracking_url = 'https://yourdomain.com/self_newsleterevent.php?pixel=' . $pixel_code;
    
    // 3. Adjuk hozzá a tracking pixelt az email végéhez
    $body .= '<img src="' . $tracking_url . '" width="1" height="1" alt="" style="display:none">';
    
    // 4. Linkek átalakítása követhető linkekké
    $body = convertLinksToTrackable($body, $sent_id);
    
    // 5. Küldjük el az emailt
    return mailer::speedsend(
        '(#TM' . $sent_id . ') ' . $subject,
        $body,
        'Sender Name <<EMAIL>>',
        $to
    );
}

/**
 * Linkek átalakítása követhető linkekké
 * 
 * @param string $html HTML tartalom
 * @param int $sent_id Küldés azonosító
 * @return string Módosított HTML
 */
function convertLinksToTrackable($html, $sent_id) {
    // DOM parser használata a linkek megtalálásához
    $dom = new DOMDocument();
    @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
    
    $links = $dom->getElementsByTagName('a');
    foreach ($links as $link) {
        $href = $link->getAttribute('href');
        if (!empty($href) && strpos($href, 'mailto:') === false) {
            // Eredeti link mentése az adatbázisba (opcionális)
            // db::save('shared.mail_links', [['sent_id', $sent_id], ['original_url', $href]]);
            
            // Link átalakítása
            $trackable_link = 'https://yourdomain.com/self_newsleterevent.php?link=' . $sent_id . '#' . urlencode($href);
            $link->setAttribute('href', $trackable_link);
        }
    }
    
    return $dom->saveHTML();
}

/**
 * Email megnyitási statisztikák lekérdezése
 * 
 * @param int $campaign_id Kampány azonosító
 * @return array Statisztikák
 */
function getFilteredEmailOpenStats($campaign_id) {
    // Összes elküldött email
    $total = db::count('shared.mail_sents', 'campaign_id = ' . $campaign_id);
    
    // Összes megnyitott email
    $all_opened = db::count('shared.mail_sents', 'campaign_id = ' . $campaign_id . ' AND viewed IS NOT NULL');
    
    // Csak a valószínűleg valódi megnyitások
    $genuine_opened = db::count('shared.mail_sents', 
        'campaign_id = ' . $campaign_id . ' AND viewed IS NOT NULL AND 
         (open_type = "genuine" OR open_type IS NULL) AND
         (pattern_flag = "likely_genuine" OR pattern_flag IS NULL)'
    );
    
    // Gyanús megnyitások
    $suspicious_opened = $all_opened - $genuine_opened;
    
    // Megnyitási arányok
    $all_open_rate = ($total > 0) ? round(($all_opened / $total) * 100, 2) : 0;
    $genuine_open_rate = ($total > 0) ? round(($genuine_opened / $total) * 100, 2) : 0;
    
    // Linkekre kattintások
    $clicked = db::count('shared.mail_sents', 'campaign_id = ' . $campaign_id . ' AND visited IS NOT NULL');
    $click_rate = ($total > 0) ? round(($clicked / $total) * 100, 2) : 0;
    
    return [
        'total_sent' => $total,
        'all_opened' => $all_opened,
        'genuine_opened' => $genuine_opened,
        'suspicious_opened' => $suspicious_opened,
        'clicked' => $clicked,
        'all_open_rate' => $all_open_rate . '%',
        'genuine_open_rate' => $genuine_open_rate . '%',
        'click_rate' => $click_rate . '%'
    ];
}