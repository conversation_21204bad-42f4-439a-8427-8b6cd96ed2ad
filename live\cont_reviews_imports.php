<?php
if( $_POST['btn_frissites'] ?? 0 ){
  if( $_POST['google_provider_key'] ?? 0 ){
    $data = ReviewsApi::get_reviews( 'Google', $_POST['google_provider_key'] );
    if( $data['error'] ?? 0 ){
      response::add( 'status', 'error' );
      response::add( 'message', 'Hiba történt az API hívás során!' );
    }elseif( $data['result'] ?? 0 ){
      $data = $data['result'];
      
      if( $data['user_ratings_total'] ?? 0 )
        if( $google_rating = db::get( DATABASENAME.'.ratings', 'provider_id=2' ))
          if( $data['user_ratings_total'] != $google_rating['reviews_number'] )
            db::save( 
              DATABASENAME.'.ratings',
              [
                ['reviews_number', $data['user_ratings_total'], 'i'],
                ['rating', $data['rating'], 'd']
              ],
              intval( $google_rating['id'] )
            );

      if( $data['reviews'] ?? 0 )
        foreach( $data['reviews'] as $key => $review )
          if(( $review['time'] ?? 0 ) and ( $review['author_name'] ?? 0 )){
            if( db::get(
              DATABASENAME.'.reviews',
              'provider_id=2 AND author_name="'.$review['author_name'].'" AND when_was="'.date( 'Y-m-d H:i:s', $review['time'] ).'"' )
            )continue;

            db::save(
              DATABASENAME.'.reviews',
              [
                ['when_was', date( 'Y-m-d H:i:s', $review['time'] )],
                ['author_name', $review['author_name']],
                ['location', $review['rating'], 'i'],
                ['services', $review['rating'], 'i'],
                ['rooms', $review['rating'], 'i'],
                ['review', $review['text'] ?? ''],
                ['provider_id', 2, 'i'],
                ['signs', '2'],
                ['company_id', $_SESSION['COMPANY'], 'i']
              ]
            );
            
          }
    }
  }
}

if( $google_rating = db::get( DATABASENAME.'.ratings', 'provider_id=2 AND company_id='.$_SESSION['COMPANY'] )){
  $_POST['google_provider_key'] = $google_rating['provider_key'];
  $_POST['google_reviews_visible'] = $google_rating['signs'][0];
  $_POST['google_rating_visible'] = $google_rating['signs'][1];
}
response::add( 'view', 'google_rating', $google_rating );

