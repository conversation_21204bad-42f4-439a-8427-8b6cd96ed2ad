<?php
if( $invoice = db::get_invoice( intval( http::$route[3] ))){
  $filteringCriteria = [];
  if( $invoice['signs'][1] == 2 ) $filteringCriteria['eloleg'] = $invoice['grossAmount'];

  if( $bookings = db::list_bookingNotInvoice( $filteringCriteria ))
    response::add( 'view', 'bookings', $bookings );

  $invoice['type'] = ['', 'Díjbekérő', 'Előlegszámla', 'Számla', 'Strorn<PERSON> számla', 'Módosító számla'][$invoice['signs'][1]];
  response::add( 'view', 'invoice', $invoice );
  
  $_POST['id'] = $invoice['id'];
  $_POST['booking_id'] = $invoice['booking_id'];
  $_POST['payday'] = $invoice['payday'];
}