<?php
if( $invoice = db::get_invoice( intval( http::$route[3] ))){
  if( $bookings = db::list_bookingNotInvoice( $invoice['issue_date'] ))
    response::add( 'view', 'bookings', $bookings );

  $invoice['type'] = ['', '<PERSON><PERSON><PERSON><PERSON>é<PERSON>', 'Előlegszámla', 'Számla', 'Strornó számla', 'Módosító számla'][$invoice['signs'][1]];
  $invoice['grossAmount'] = array_sum( array_column( $invoice['items'], 'price_net' )) * $invoice['exchange_rate'];
  response::add( 'view', 'invoice', $invoice );
  
  $_POST['id'] = $invoice['id'];
  $_POST['booking_id'] = $invoice['booking_id'];
  $_POST['payday'] = $invoice['payday'];
}