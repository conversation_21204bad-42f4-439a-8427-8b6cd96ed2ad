  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új <PERSON>rt<PERSON></a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable( 
          'reviews',
          [ 'allapotstr' => ['th' => 'Állapot', 'status' => 'allapot'],
            'provider' => ['th' => 'Szolgáltató'],
            'rate' => ['th' => 'Érték'],
            'author_name' => ['th' => '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
            'when_was' => ['th' => 'Mikor'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés::id',
                'trash-empty:event:Törlés:/reviewdel/:id:confirm:where:db'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { datatable, dialog, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd
      switch( ifunction ){
        case 'reviews_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_review' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/review',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Az értékelés módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'reviews_edit_close':
          location.replace( '/reviews/ratings' )
        break
        case 'reviews_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( { 
            url: '/modal/reviewdel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/reviews/ratings' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van az ártákelés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új értékelés',
          label: 'Értékelő neve'
        },
        eventManagerFunctions
      } )
    } )
  </script>