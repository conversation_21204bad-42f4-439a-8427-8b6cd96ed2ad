<?php
/**
 * Ügyfél részére nyilvános API elérést biztosító <PERSON>
 *
 * @method `getCurlHandle();` <PERSON><PERSON><PERSON> hívás előkészíté<PERSON>
 * @method `init();`          <PERSON><PERSON> kezel<PERSON> indítás
 * @method `callApi();`        Api hívás
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2025, Tánczos Róbert
 *
 * @version 1.0.0
 * @since 1.0.0 2025.03.08
 */

class ServerApiConnector{

  private static $params;

/**
 * Curl hívás előkészítés
 *
 * Paraméterek alapján beállítja a curl hívás.
 *
 * @return mixed CurlHandle|false
 */
  private static function getCurlHandle(){
    if( $ch = curl_init()){
      $headers = [
        "Content-Type: application/json;charset=UTF-8",
        "Accept: application/".['json' => 'json', 'xml' => 'xml', 'form' => 'x-www-form-urlencoded'][self::$params->mode],
        'Authorization: Bearer '. self::$params->apiKey
      ];

      if( self::$params->timeout ){
        curl_setopt( $ch, CURLOPT_CONNECTTIMEOUT, 0 );
        curl_setopt( $ch, CURLOPT_TIMEOUT, self::$params->timeout );
      }

      curl_setopt( $ch, CURLOPT_HTTPHEADER, $headers );
      curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
      curl_setopt( $ch, CURLOPT_FRESH_CONNECT, true );

      return $ch;
    }else return false;
  }

/**
 * Api kezelő indítás
 *
 * Inicializálja az API hívásokhoz szükséges paramétereket.
 *
 * @param  string $endpoint Api elérési linkje.
 * @param  string $apikey   API kulcs
 * @param  int    $timeout  hívás időkorlátja
 */
  public static function init( $endpoint, $apiKey = '', $timeout = 0, $mode = 'json' ){
    if( !filter_var( $endpoint, FILTER_VALIDATE_URL ))
      throw new InvalidArgumentException( 'Érvénytelen API endpoint: ' . $endpoint );
    if( !is_string( $apiKey ) or strlen( $apiKey ) < 10 )
      throw new InvalidArgumentException( 'Érvénytelen API kulcs.' );
    if( !is_int( $timeout ) or $timeout < 0 )
      throw new InvalidArgumentException( 'Érvénytelen timeout érték.' );

    self::$params = (object) [
      'endpoint' => $endpoint,
      'apiKey' => $apiKey,
      'timeout' => $timeout,
      'mode' => $mode
    ];
  }

/**
 * Api hívás
 *
 * A függvény egy változót vesz fel, amit $data-nak hívnak, és visszaadja azt tisztított változatban.
 * Ha a $data egy tömb, akkor rekurzívan tisztítja az összes értéket és hozzárendeli ugyanahhoz a kulcshoz egy új tömbben.
 * Ha a $data nem tömb, akkor eltávolítja az összes HTML címkét és a felesleges szóközöket a változóból.
 *
 * @param  string $action       API hívás neve 
 * @param  string $method       API hívás típusa GET, POST, DELETE
 * @param  array  $data         API hívás adatai
 * @param  string $tokenization Tokenizációs mód
 * @return json                 JSON formátumban eredmény vagy hibaüzenet. 
 */
  public static function callApi( $action = '', $method = 'GET', $data = [], $tokenization = false ){
    if( $ch = self::getCurlHandle()){
      curl_setopt( $ch, CURLOPT_URL, self::$params->endpoint . $action );
      switch( $method ){
        case 'POST':
          curl_setopt( $ch, CURLOPT_POST, true );
          curl_setopt( $ch, CURLOPT_POSTFIELDS, json_encode( $data ) );
        break;
        case 'GET':
          curl_setopt( $ch, CURLOPT_HTTPGET, true );
          if( count( $data ) )
            switch( $tokenization ){
              case 'base64':
                curl_setopt( $ch, CURLOPT_URL, self::$params->endpoint . $action . '/b64' . base64_encode( json_encode( $data )));
              break;
              case 'hmac':
                $data['signature_expires'] = time() + 3600;
                $signature = hash_hmac( 'sha256', json_encode( $data ), self::$params->apikey );
                curl_setopt( $ch, CURLOPT_URL, self::$params->endpoint . $action . '/mac' . base64_encode( json_encode( $data ) . '::' . $signature ));
              break;
              case 'jwt':
                $data['signature_expires'] = time() + 3600;
                $signature = JWT::encode( $data, self::$params->apikey );
                curl_setopt( $ch, CURLOPT_URL, self::$params->endpoint . $action . '/jwt' . $signature );
              break;
              default:
                curl_setopt( $ch, CURLOPT_URL, self::$params->endpoint . $action . '?' . http_build_query( $data ));
              break;
            }
        break;
        case 'DELETE':
          curl_setopt( $ch, CURLOPT_CUSTOMREQUEST, 'DELETE' );
        break;
      }

      $res = curl_exec( $ch );
      $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

      if( $res === false )
        $res = json_encode( ['error' => ['code' => curl_errno( $ch ), 'message' => curl_error( $ch ), 'details' => '']] );
      elseif( $http_code >= 400 )
        $res = json_encode( ['error' => ['code' => $http_code, 'message' => $res, 'details' => '']] );

      curl_close( $ch );
    }else $res = json_encode( ['error' => ['code' => 0, 'message' => 'Hívás nem lehetséges!', 'details' => '']] );
    return json_decode( $res );
  }
}