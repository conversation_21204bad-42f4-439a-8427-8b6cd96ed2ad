          <section class="flex">
            <?php if( $_SESSION['COMPANY'] ){ ?>
            <div class="cart">
              <header><h5><PERSON><PERSON><PERSON><PERSON> vendégek</h5></header>
              <article>
                <header><h6>Ma</h6></header>
                <?php if( response::$vw->view->maerkezik ){ ?>
                <?= table::datatable( 'maerkezik',
                                      ['foglalas_id' => ['th' => '#'],
                                       'name' => ['th' => 'Lakóegység'],
                                       'kapcsolattarto' => ['th' => 'Foglaló'],
                                       '*1' => ['th' => '', 'icons' => ['edit:link:Foglaláshoz:/naptar/modosit/:id']]] ) ?>
                <?php }else{ ?>
                <ul><li>Nem érkezik vendég!</li></ul>
                <?php } ?>
              </article>
              <article>
                <header><h6>Holnap</h6></header>
                <?php if( response::$vw->view->holnaperkezik ){ ?>
                <?= table::datatable( 'holnaperkezik',
                                      ['foglalas_id' => ['th' => '#'],
                                       'name' => ['th' => 'Lakóegység'],
                                       'kapcsolattarto' => ['th' => 'Foglaló'],
                                       '*1' => ['th' => '', 'icons' => ['edit:link:Foglaláshoz:/naptar/modosit/:id']]] ) ?>
                <?php }else{ ?>
                <ul><li>Nem érkezik vendég!</li></ul>
                <?php } ?>
              </article>
            </div>
            <div class="cart">
              <header><h5>Távozó vendégek</h5></header>
              <article>
                <header><h6>Ma</h6></header>
                <?php if( response::$vw->view->matavozik ){ ?>
                <?= table::datatable( 'matavozik',
                                      ['foglalas_id' => ['th' => '#'],
                                       'name' => ['th' => 'Lakóegység'],
                                       'kapcsolattarto' => ['th' => 'Foglaló'],
                                       '*1' => ['th' => '', 'icons' => ['edit:link:Foglaláshoz:/naptar/modosit/:id']]] ) ?>
                <?php }else{ ?>
                <ul><li>Nem tavozik vendég!</li></ul>
                <?php } ?>
              </article>
              <article>
                <header><h6>Holnap</h6></header>
                <?php if( response::$vw->view->holnaptavozik ){ ?>
                <?= table::datatable( 'holnaptavozik',
                                      ['foglalas_id' => ['th' => '#'],
                                       'name' => ['th' => 'Lakóegység'],
                                       'kapcsolattarto' => ['th' => 'Foglaló'],
                                       '*1' => ['th' => '', 'icons' => ['edit:link:Foglaláshoz:/naptar/modosit/:id']]] ) ?>
                <?php }else{ ?>
                <ul><li>Nem távozik vendég!</li></ul>
                <?php } ?>
              </article>
            </div>
            <div class="cart">
              <header><h5>Foglalások</h5></header>
              <article>
                <header><h6>Ma</h6></header>
                <?php if( response::$vw->view->mafoglalt ){ ?>
                <?php function table_mafoglalt(){ return json_decode(json_encode ( response::$vw->view->mafoglalt ), true ); } ?>
                <?= table::datatable( 'mafoglalt',
                                      ['foglalas_id' => ['th' => '#'],
                                       'name' => ['th' => 'Lakóegység'],
                                       'kapcsolattarto' => ['th' => 'Foglaló'],
                                       '*1' => ['th' => '', 'icons' => ['edit:link:Foglaláshoz:/naptar/modosit/:id']]] ) ?>
                <?php }else{ ?>
                <ul><li>Nem volt foglalás!</li></ul>
                <?php } ?>
              </article>
              <article>
                <header><h6>Tegnap</h6></header>
                <?php if( response::$vw->view->tegnapfoglalt ){ ?>
                <?php function table_tegnapfoglalt(){ return json_decode(json_encode ( response::$vw->view->tegnapfoglalt ), true ); } ?>
                <?= table::datatable( 'tegnapfoglalt',
                                      ['foglalas_id' => ['th' => '#'],
                                       'name' => ['th' => 'Lakóegység'],
                                       'kapcsolattarto' => ['th' => 'Foglaló'],
                                       '*1' => ['th' => '', 'icons' => ['edit:link:Foglaláshoz:/naptar/modosit/:id']]] ) ?>
                <?php }else{ ?>
                <ul><li>Nem volt foglalás!</li></ul>
                <?php } ?>
              </article>
            </div>
            <div class="cart">
              <header><h5>Lakóegység</h5></header>
              <article class="shema1">
                <dl class="flex">
                  <dt><?= number_format( response::$vw->view->dashboard->nights_booked ?? 0, 0, '', ' ' ) ?> éj</dt>
                  <dd>
                    <ul>
                      <li>
                        Kihasználtság
                        <span><?= ( ( response::$vw->view->dashboard->total_nights ?? 0 ) and response::$vw->view->dashboard->total_nights )? number_format( 100 * response::$vw->view->dashboard->nights_booked / response::$vw->view->dashboard->total_nights, 0, '', ' ' ) : '-' ?> %</span>
                      </li>
                      <li>
                        Átlagár/éj
                        <span><?= ( ( response::$vw->view->dashboard->nights_booked ?? 0 ) and response::$vw->view->dashboard->nights_booked )? number_format( response::$vw->view->dashboard->total_revenue /response::$vw->view->dashboard->nights_booked, 0, '', ' ' ) : '-' ?> HUF</span>
                      </li>
                    </ul>
                  </dd>
                </dl>
              </article>
            </div>
            <div class="cart">
              <header><h5>Bevétel</h5></header>
              <article class="shema1">
                <dl class="flex">
                  <dt><?= number_format( response::$vw->view->dashboard->total_revenue, 0, '', ' ' ) ?> HUF</dt>
                  <dd>
                    <ul>
                      <li>
                        Beérkezett<br>
                        <span><?= number_format( response::$vw->view->dashboard->total_paid, 0, '', ' ' ) ?> HUF</span>
                      </li>
                      <li>
                        Várható<br>
                        <span><?= number_format( response::$vw->view->dashboard->total_revenue - response::$vw->view->dashboard->total_paid, 0, '', ' ' ) ?> HUF</span>
                      </li>
                    </ul>
                  </dd>
                </dl>
              </article>
            </div>
            <div class="cart">
              <header><h5>Foglalás</h5></header>
              <article class="shema2">
                <?php if( response::$vw->view->dashboard->channels ?? 0 ){ ?>
                <dl>
                  <dd>
                    <ul>
                      <?php $gradient = []; foreach( response::$vw->view->dashboard->channels as $channel => $night ){ ?>
                      <li><?= $channel ?><span><?= $gradient[] = round( 100 * $night / response::$vw->view->dashboard->nights_booked, 0 ) ?> %</span></li>
                      <?php } ?>
                    </ul>
                  </dd>
                  <dt style="background: linear-gradient(to bottom,
                              var(--danger-color) 0% <?= $gradient[0] ?? 0 ?>%,
                              var(--warning-color) <?= $gradient[0] ?? 0 ?>% <?= ( $gradient[0] ?? 0 ) + ( $gradient[1] ?? 0 ) ?>%,
                              var(--info-color) <?= ( $gradient[0] ?? 0 ) + ( $gradient[1] ?? 0 ) ?>% <?= ( $gradient[0] ?? 0 ) + ( $gradient[1] ?? 0 ) + ( $gradient[2] ?? 0 ) ?>%,
                              var(--success-color) <?= ( $gradient[0] ?? 0 ) + ( $gradient[1] ?? 0 ) + ( $gradient[2] ?? 0 ) ?>% 100%);"></dt>
                </dl>
                <?php }else{ ?>
                  Nincs foglalás!
                <?php } ?>
              </article>
            </div>
            <?php /*
            <div class="cart">
              <header><h5>Gyorsjelentés</h5></header>
              <article>
                <form name="form_flash_report" method="post">
                  <ul class="formbox">
                    <li class="form col0">
                      <input type="text" name="mettol" placeholder="" value="<?= $_POST['mettol'] ?>">
                      <label>Mettől</label>
                    </li>
                    <li class="form col0">
                      <input type="text" name="meddig" placeholder="" value="<?= $_POST['meddig'] ?>">
                      <label>Meddig</label>
                    </li>
                    <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
                      <select name="egyseg" placeholder="">
                        <option value="N"<?= ( $_POST['egyseg'] == 'N' )?' selected':'' ?>>Nap</option>
                        <option value="7"<?= ( $_POST['egyseg'] == '7' )?' selected':'' ?>>Hét</option>
                        <option value="H"<?= ( $_POST['egyseg'] == 'H' )?' selected':'' ?>>Hó</option>
                      </select>
                      <label>Egység</label>
                    </li>
                    <li class="col0">
                      <button class="col0" type="submit" name="btn_flash_report">Gyűjt</button>
                    </li>
                  </ul>
                </form>
                <div class="report">
                  <i>BEVÉTEL</i>
                  <span>8 342 450 HUF</span>

                </div>

                <?php if( $_POST['egyseg'] == 'H' ){ ?>
                <?php if( response::$vw->view->statlist ){ ?>
                <?php   foreach( response::$vw->view->statlist as $egyseg => $ertek ){ ?>
                <?= $egyseg ?> éj: <?= $ertek->ej ?> / <?= $ertek->maxej ?> [<?= round(100*$ertek->ej/$ertek->maxej) ?>%]
                fizetendő: <?= $ertek->fiz ?> / <?= $ertek->maxfiz ?> [<?= round(100*$ertek->fiz/$ertek->maxfiz) ?>%]<br>
                <?php } } } ?>

                <?php if( $_POST['egyseg'] == 'N' ){ ?>
                <?php   $w = date( 'W', strtotime( $_POST['meddig'] ) ) - date( 'W', strtotime( $_POST['mettol'] ) ); ?>
                <div style="margin-top: 20px">
                  <svg height="111" width="<?= $w * 13 + 80 ?>">
                    <g width="<?= $w * 13 + 10 ?>" transform="translate(70, 21)">
                      <?php foreach( response::$vw->view->statlist as $egyseg => $ertek ){ ?>
                      <?php   $elso = $elso ?? date( 'W', strtotime( $egyseg ) ); ?>
                      <rect width="10" height="10"
                            rx="2" ry="2"
                            x="<?= ( date( 'W', strtotime( $egyseg ) ) - $elso ) * 13 ?>"
                            y="<?= ( date( 'N', strtotime( $egyseg ) ) - 1 ) * 13 ?>"
                            style="fill: #<?= ['fff','F9E0BB','FFC26F','C38154','884A39','862B0D','000'][$ertek->ej]?>; outline: <?= ( $egyseg == date('Y-m-d') )? 'red' : 'rgba(27, 31, 35, 0.06)' ?> solid 1px; outline-offset: -1px; shape-rendering: geometricprecision;">
                        <title><?= $egyseg.': '.$ertek->ej ?></title>
                      </rect>
                      <?php } ?>
                    </g>

                    <g transform="translate(0, 19.599999999999998)">
                      <text y="10" x="0" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">Hétfő</text>
                      <text y="23" x="0" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">Kedd</text>
                      <text y="36" x="0" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">Szerda</text>
                      <text y="49" x="0" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">Csütörtök</text>
                      <text y="62" x="0" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">Péntek</text>
                      <text y="75" x="0" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">Szombat</text>
                      <text y="88" x="0" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">Vasárnap</text>
                    </g>

                    <g transform="translate(70, 0)">
                      <?php foreach( response::$vw->view->statlist as $egyseg => $ertek ){ ?>
                      <?php   if( $ho != date( 'n', strtotime( $egyseg ) ) ){ $ho = date( 'n', strtotime( $egyseg ) ); ?>
                      <text x="<?= ( date( 'W', strtotime( $egyseg ) ) - $elso ) * 13 ?>" y="14" style="font-size: 12px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; color: rgb(36, 41, 47);">
                        <?= $ho ?><title><?= $ho ?></title>
                      </text>
                      <?php } } ?>
                    </g>

                  </svg>
                  
                  <div style="font-size: 12px; display: flex; justify-content: flex-start; align-items: baseline;">
                    0
                    <svg width="10" height="10" style="margin-right:4px;margin-left:4px;">
                      <title>0</title>
                      <rect width="10" height="10" rx="2" ry="2" fill="#fff"></rect>
                    </svg>
                    1
                    <svg width="10" height="10" style="margin-right:4px;">
                      <title>1 szoba</title>
                      <rect width="10" height="10" rx="2" ry="2" fill="#F9E0BB"></rect>
                    </svg>
                    2
                    <svg width="10" height="10" style="margin-right:4px;">
                      <title>2 szoba</title>
                      <rect width="10" height="10" rx="2" ry="2" fill="#FFC26F"></rect>
                    </svg>
                    3
                    <svg width="10" height="10" style="margin-right:4px;">
                      <title>3 szoba</title>
                      <rect width="10" height="10" rx="2" ry="2" fill="#C38154"></rect>
                    </svg>
                    4
                    <svg width="10" height="10" style="margin-right:4px;">
                      <title>4 szoba</title>
                      <rect width="10" height="10" rx="2" ry="2" fill="#884A39"></rect>
                    </svg>
                    5
                    <svg width="10" height="10" style="margin-right:4px;">
                      <title>3 szoba</title>
                      <rect width="10" height="10" rx="2" ry="2" fill="#862B0D"></rect>
                    </svg>
                    6
                    <svg width="10" height="10" style="margin-right:4px;">
                      <title>3 szoba</title>
                      <rect width="10" height="10" rx="2" ry="2" fill="#000"></rect>
                    </svg>
                  </div>
                </div>
                <?php } ?>
              </article>
            </div>
            */ ?>
            <?php } ?>
          </section>