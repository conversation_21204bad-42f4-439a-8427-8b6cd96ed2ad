<?php
/** Inputot ellenörz<PERSON> függvények
 * 
 * @method `isVariable();   <PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>ének vizsgálata
 * @method `validNumeric(); Szám szintaktikai ellenőrzése
 * @method `validDate();`   Dátum szintaktikai ellenőrzése
 * @method `validPsw();`    Jelszó szintaktikai ellenőrzése
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2018, Tánczos Róbert
 * 
 * @version 1.0.0
 * @since 1.0.0 2025.04.22 Refaktor és optimalizálás
 * @since 0.1.0 2018.06.24
 */
 
class Valid{
  /** Változó létezés vizsgálat
   * 
   * Értékkel kell rendelkeznie, nem lehet null és üres.
   * 
   * @param  string  $input vizsgálandó változó
   * @return boolean        0 ha megfelelő, 1, ha nincs értéke
   */
  public static function isVariable( $input ){
    return ( isset( $input ) and strlen( $input ))? 0 : 1;
  }

  /** Szám ellen<PERSON>
   * 
   * @param  string $num  szám
   * @param  int    $min  [optional] szám minimális értéke. Megfelel a html min attribútumnak. Alapérték `null`.
   * @param  int    $max  [optional] szám maximális értéke. Megfelel a html max attribútumnak. Alapérték `null`.
   * @param  string $step [optional] szám értékek lépésköze. Megfelel a html step attribútumnak. Alapérték 1. Az `any` beállításnam megfelel a `null`
   * @return boolean                 0 ha megfelelő, 1 ha nincs, 2 ha nem szám, 3 ha kevesebb, 4 ha több
   */
  public static function validNumeric( $num, $min = null, $max = null, $step = 1 ){
    if( self::isVariable( $num )) return 1;
    if( !is_numeric( $num )) return 2;
    $num = $num + 0;
    if( $min !== null and $num < $min ) return 3;
    if( $max !== null and $num > $max )return 4;
    if( $step !== null and $step ){
      $base = $min !== null ? $min : 0;
      if( fmod( abs( $num - $base ), $step ) > 0.000001 ) return 2;
    }
    return 0;
  }
  
  /** Dátum ellenőrzés
   *
   * @param  string $date   dátum
   * @param  string $format dátum formátuma pl. 'Y-m-d'
   * @param  string $min    [optional] dátum minimális értéke. Megfelel a html min attribútumnak. Alapérték `null`.
   * @param  string $max    [optional] dátum maximális értéke. Megfelel a html max attribútumnak. Alapérték `null`.
   * @return boolean                   0 ha megfelelő, 1 ha nincs, 2 ha nem megfelelő dátum, 3 ha kisebb, 4 ha nagyobb
   */
  public static function validDate( $date, $format = 'Y-m-d', $min = null, $max = null ){
    if( self::isVariable( $date )) return 1;
    $parsed_date = date_parse_from_format( $format, $date );
    if( $parsed_date['error_count'] or $parsed_date['warning_count'] ) return 2;  
    if( !checkdate( $parsed_date['month'], $parsed_date['day'], $parsed_date['year'] )) return 2;
    if( isset( $min ) and strtotime( $date ) < strtotime( $min )) return 3;
    if( isset( $max ) and strtotime( $date ) > strtotime( $max )) return 4;
    return 0;
  }

  /** Jelszó szintaktikai ellenőrzése
   * 
   * Jelszó maximum 30 karakter lehet
   * 
   * @param string $psw       jelszó
   * @param int    $minlength [optional] minimum karakter szám. alapérték 8
   * @param string $options   [optional] 0:szám, 1:betű, 2:kisbetű, 3:nagybetű, 4:különböző karakterek. alapérték '00000'
   * @param string $chars     [optional] ékezet nélküli betük és számokon kívűl milyen karakter elfogadott. alapérték ''
   * @return boolean          0 ha megfelelő, 1 ha nincs, 2 ha nem megfelelő karakter, 3 ha rövid, 4 ha hosszú,
   *                          5 ha nem tartalmaz számot, 6 ha nem tartalmaz betűt, 7 ha nem tartalmaz kisbetűt,
   *                          8 ha nem tartalmaz nagybetűt, 9 ha nem tartalmaz különböző karaktereket
   */
  public static function validPsw($psw, $minlength = 8, $options = '00000', $chars = null ){
    if( self::isVariable( $psw )) return 1;
    if( strlen( $psw ) < $minlength ) return 3;
    if( strlen( $psw ) > 30 ) return 4;
    if( isset( $chars ) and !preg_match( '/^[0-9A-Za-z'.$chars.']+$/', $psw )) return 2;
    if( $options != '00000' ){
      if( $options[0]=='1' and !preg_match( '#[0-9]+#', $psw )) return 5; // számot tartalmaz
      if( $options[1]=='1' and !preg_match( '#[a-zA-Z]+#', $psw )) return 6; // betüt tartalmaz
      if( $options[2]=='1' and !preg_match( '#[a-z]+#', $psw )) return 7; // kisbetüt tartalmaz
      if( $options[3]=='1' and !preg_match( '#[A-Z]+#', $psw )) return 8; // nagybetüt tartalmaz
      if( $options[4]=='1' and sizeof( array_unique( str_split( $psw ))) < 2 ) return 9; // nem lehet azonos mindek karakter
    }
    return 0;
  }
}