<?php
if( $accommodationunit = db::get( DATABASENAME.'.accommodation_units', http::$route[3] ) ){
  $_POST['id'] = $accommodationunit['id'];
  $_POST['status'] = $accommodationunit['signs'][0];
  $_POST['area'] = $accommodationunit['area'];
  $_POST['sort_by'] = $accommodationunit['sort_by'];
  $_POST['name'] = $accommodationunit['name'];
  $_POST['building'] = $accommodationunit['building'];
  $_POST['ntak_type'] = $accommodationunit['ntak_type'];
  $_POST['single_beds_number'] = $accommodationunit['single_beds_number'];
  $_POST['double_beds_number'] = $accommodationunit['double_beds_number'];
  $_POST['extra_beds_number'] = $accommodationunit['extra_beds_number'];
  $_POST['description'] = $accommodationunit['description'];
  $_POST['brief'] = $accommodationunit['brief'];
  $_POST['features'] = $accommodationunit['features'];

  if( $accommodationunittype = db::get( DATABASENAME.'.accommodation_unit_types', intval( $accommodationunit['accommodation_unit_type_id'] ) ) )
    response::add( 'view', 'accommodationunittype', $accommodationunittype );
}