<?php
if( $price = db::get( DATABASENAME.'.accommodation_unit_type_prices', http::$route[3] ) ){
  $_POST['id'] = $price['id'];
  $_POST['name'] = $price['name'];
  $_POST['accommodation_unit_type_id'] = $price['accommodation_unit_type_id'];
  $_POST['period_id'] = $price['period_id'];
  $_POST['weekly_priced_days'] = $price['weekly_priced_days'];
  $_POST['weekly_arrival_days'] = $price['weekly_arrival_days'];
  $_POST['locked_nights'] = $price['locked_nights'];
  $_POST['min_booking_period'] = $price['min_booking_period'];
  $_POST['min_days'] = $price['min_days'];
  $_POST['max_days'] = $price['max_days'];
  $_POST['prices'] = ( $price['prices'] ?? 0 )? json_decode( $price['prices'] ) : [];
  
  response::add( 'view','accommodation_unit_types', db::list( DATABASENAME.'.accommodation_unit_types', 'company_id='.$_SESSION['COMPANY'] ));
  response::add( 'view','periods', db::list( DATABASENAME.'.periods', 'company_id='.$_SESSION['COMPANY'] ));
}