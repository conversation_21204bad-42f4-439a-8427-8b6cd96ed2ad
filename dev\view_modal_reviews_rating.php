<header>
  <h6><PERSON><PERSON><PERSON><PERSON><PERSON></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_review">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <ul class="formbox">
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="status" placeholder="">
          <option value="1"<?=( $_POST['status'] == '1' )? ' selected' : '' ?>>Nem látható</option>  
          <option value="2"<?=( $_POST['status'] == '2' )? ' selected' : '' ?>>Látható</option>  
        </select>
        <label>Állapot</label>
      </li>
      <li class="form col5">
        <input type="text" name="when_was" placeholder="" value="<?= $_POST['when_was'] ?? date( 'Y-m-d' ) ?>">
        <label>Mikor</label>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="provider_id" placeholder="">
          <?php foreach( $providers as $provider ){ ?>
            <option value="<?= $provider['id'] ?>"<?=( $_POST['provider_id'] == $provider['id'] )? ' selected' : '' ?>><?= $provider['name'] ?></option>
          <?php } ?>  
        </select>
        <label>Szolgáltató</label>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="trip_purpose_id" placeholder="">
          <?php foreach( $trip_purposes as $key => $trip_purpose ){ ?>
            <option value="<?= $key ?>"<?=( $_POST['trip_purpose_id'] == $key )? ' selected' : '' ?>><?= $trip_purpose ?></option>
          <?php } ?>  
        </select>
        <label>Utazás célja</label>
      </li>
      <li class="form col6">
        <input type="text" name="author_name" placeholder="" value="<?= $_POST['author_name'] ?? '' ?>">
        <label>Értékelő</label>
      </li>
      <li class="form col4 bgonto" data-to="éj">
        <input type="text" name="residency_days" placeholder="" value="<?= $_POST['residency_days'] ?? '' ?>">
        <label>Tartózkodás</label>
      </li>
      <li class="form col4" style="--toicon:var(--icon-angle-double-down)">
        <select name="author_country" placeholder="">
          <option value="">--</option>
          <?php foreach( $countries as $country ){ ?>
            <option value="<?= $country['tld'] ?>"<?=( $_POST['author_country'] == $country['tld'] )? ' selected' : '' ?>><?= $country['name'] ?></option>
          <?php } ?>
        </select>
        <label>Értékelő országa</label>
      </li>
      <li class="form col6">
        <input type="text" name="author_city" placeholder="" value="<?= $_POST['author_city'] ?? '' ?>">
        <label>Értékelő települése</label>
      </li>
      <li class="form col5">
        <input type="text" name="location" placeholder="" value="<?= $_POST['location'] ?? '' ?>">
        <label>Elhelyezkedés</label>
      </li>
      <li class="form col5">
        <input type="text" name="communication" placeholder="" value="<?= $_POST['communication'] ?? '' ?>">
        <label>Kommunikáció</label>
      </li>
      <li class="form col5">
        <input type="text" name="cleanliness" placeholder="" value="<?= $_POST['cleanliness'] ?? '' ?>">
        <label>Tisztaság</label>
      </li>
      <li class="form col5">
        <input type="text" name="value" placeholder="" value="<?= $_POST['value'] ?? '' ?>">
        <label>Ár/érték</label>
      </li>
      <li class="form col5">
        <input type="text" name="checkin" placeholder="" value="<?= $_POST['checkin'] ?? '' ?>">
        <label>Bejelentkezés</label>
      </li>
      <li class="form col5">
        <input type="text" name="accuracy" placeholder="" value="<?= $_POST['accuracy'] ?? '' ?>">
        <label>Valósághűség</label>
      </li>
      <li class="form col5">
        <input type="text" name="services" placeholder="" value="<?= $_POST['services'] ?? '' ?>">
        <label>Szolgáltatások</label>
      </li>
      <li class="form col5">
        <input type="text" name="comfort" placeholder="" value="<?= $_POST['comfort'] ?? '' ?>">
        <label>Kényelem</label>
      </li>
      <li class="form col5">
        <input type="text" name="rooms" placeholder="" value="<?= $_POST['rooms'] ?? '' ?>">
        <label>Szobák</label>
      </li>
      <li class="form col5">
        <input type="text" name="wifi" placeholder="" value="<?= $_POST['wifi'] ?? '' ?>">
        <label>Wifi</label>
      </li>
      <li class="form col0">
        <input type="text" name="title" placeholder="" value="<?= $_POST['title'] ?? '' ?>">
        <label>Vélemény címe</label>
      </li>
      <li class="form col0">
        <textarea name="review"><?= $_POST['review'] ?? '' ?></textarea>
        <label>Vélemény</label>
      </li>
      <li class="form col0">
        <textarea name="negative_review"><?= $_POST['negative_review'] ?? '' ?></textarea>
        <label>Negatív vélemény</label>
      </li>
      <li class="form col0">
        <textarea name="answer"><?= $_POST['answer'] ?? '' ?></textarea>
        <label>Válasz</label>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Értékelés mentése">Módosít</button>
</footer>