<?php
/**
 * SELECT * FROM user_projects up, companies c WHERE up.user_id=c.created_user_id AND up.project_id=37 AND up.user_id NOT IN(1050,7793,10668,10680,10866) ORDER BY `c`.`id` DESC
 *
 * @file mod_model.php
 * @brief <PERSON>t<PERSON><PERSON><PERSON><PERSON>
 */

class db extends mysql{

  public static function list_subscribers( $company_id = null ){
    $company_id = $company_id ?? $_SESSION['COMPANY'] ?? null;
    $sql = 'SELECT u.*,s.consent_start FROM '.DATABASENAME.'.subscribers s
         LEFT JOIN shared.users u ON u.id=s.user_id
             WHERE company_id='.$company_id.' ORDER BY s.consent_start DESC';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function list_services( $company_id = null ){
    $company_id = $company_id ?? $_SESSION['COMPANY'] ?? null;
    $sql = 'SELECT * FROM '.DATABASENAME.'.services
             WHERE MID(signs,1,1) <> "0" AND company_id='.$company_id.' ORDER BY sequence';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function list_szolgaltatas(){
    $sql = 'SELECT * FROM '.DATABASENAME.'.szolgaltatas ORDER BY megnevezes';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function get_szolgaltatas( $szolgaltatas_id ){
    $sql = 'SELECT * FROM '.DATABASENAME.'.szolgaltatas WHERE szolgaltatas_id=? LIMIT 1';
    $res = self::query( $sql, [ $szolgaltatas_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  
  public static function save_csatorna( $adat, $csatorna_id = 0 ){
    return self::save( DATABASENAME.'.csatorna', $adat, ( $csatorna_id )? ['csatorna_id', $csatorna_id, 'i' ] : false );
  }

  public static function list_bookingChannelsCompany( $company_id = null ){
    $company_id = $company_id ?? $_SESSION['COMPANY'] ?? 0;
    $sql = '
      SELECT id, type, name
      FROM '.DATABASENAME.'.booking_channels
      WHERE MID(signs,1,1)=2 AND ('.(( $company_id )? 'company_id='.$company_id : '1=2' ).' OR company_id=0)
      ORDER BY type,name
    ';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }

  public static function list_csatorna( $ceg_id = null ){
    $ceg_id = $ceg_id ?? $_SESSION['COMPANY'] ?? null;
    $sql = '
      SELECT *, 0 db, csatorna_id id, IF(MID(signs,1,1) = 1,"Rejtett","Használható") allapotstr
      FROM '.DATABASENAME.'.csatorna
      WHERE MID(signs,1,1) <> 0'.(( $ceg_id ?? 0 )? ' AND ceg_id='.$ceg_id : '' ).'
      ORDER BY foglalasicsatorna,megnevezes
    ';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }

  public static function get_csatorna( $id ){
    $sql = 'SELECT * FROM '.DATABASENAME.'.booking_channels WHERE id=? LIMIT 1';
    return self::query( $sql, [ $id ], 'i' )->get_result()->fetch_assoc();
  }

  // Ical aktív csatornák listája ( import ellenőrzéshez )
  public static function list_bookingSynchrosIcal(){
    $sql = 'SELECT sz.*, l.company_id FROM '.DATABASENAME.'.szinkronnaptar sz
         INNER JOIN '.DATABASENAME.'.booking_channels cs ON cs.id=sz.csatorna_id
         INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=sz.lakoegyseg_id
             WHERE ellenorzo IS NOT NULL AND link IS NOT NULL AND MID(cs.signs,1,1)="2" AND MID(cs.signs,2,1)="1" AND MID(l.signs,1,1)="2"';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }

  public static function get_szinkronnaptar( $lakoegyseg_id, $csatorna_id ){
    $sql = 'SELECT * FROM '.DATABASENAME.'.szinkronnaptar WHERE lakoegyseg_id=? AND csatorna_id=? LIMIT 1';
    $res = self::query( $sql, [ $lakoegyseg_id, $csatorna_id ], 'ii' )->get_result()->fetch_assoc();
    return $res;
  }

  public static function list_bookingSynchros( $accommodation_unit_id ){
    $sql = '
      SELECT * FROM '.DATABASENAME.'.szinkronnaptar sz
      INNER JOIN '.DATABASENAME.'.booking_channels bc ON bc.id=sz.csatorna_id
      WHERE lakoegyseg_id=? AND MID(bc.signs,1,1) = 2 AND MID(bc.signs,2,1) > 0
    ';
    $res = self::query( $sql, [ $accommodation_unit_id ], 'i' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }

  public static function list_bookingChannelsNonSync( $accommodation_unit_id ){
    $sql = 'SELECT bc.* FROM '.DATABASENAME.'.booking_channels bc
         LEFT JOIN '.DATABASENAME.'.szinkronnaptar sz ON bc.id=sz.csatorna_id AND sz.lakoegyseg_id=?
             WHERE MID(bc.signs,1,1) = 2 AND MID(bc.signs,2,1) > 0 AND sz.csatorna_id IS NULL';
    $res = self::query( $sql, [ $accommodation_unit_id ], 'i' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }

  public static function save_bookingSynchrosTimeUpdate( $szinkronnaptar_id ){
    $sql = 'UPDATE '.DATABASENAME.'.szinkronnaptar SET utolso="'.date('Y-m-d H:i:s').'" WHERE szinkronnaptar_id=? LIMIT 1';
    self::query( $sql, [ $szinkronnaptar_id ], 'i' );
  }

  public static function list_accommodationUnitTypes( $where = null, $company_id = null  ){
    $sql = 'SELECT aut.*, IF((SELECT a.id FROM '.DATABASENAME.'.accommodation_units a WHERE a.accommodation_unit_type_id=aut.id LIMIT 1),1,0) is_trash_empty
            FROM '.DATABASENAME.'.accommodation_unit_types aut
            WHERE aut.company_id='.( $company_id ?? $_SESSION['COMPANY'] );
    if( $where ?? 0 ) $sql .= ' AND '.$where;
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function list_accommondationUnitTypePrices( $company_id = null ){
    $sql = 'SELECT autp.*, aut.name type_name, p.name period_name
            FROM '.DATABASENAME.'.accommodation_unit_type_prices autp
            INNER JOIN '.DATABASENAME.'.accommodation_unit_types aut ON autp.accommodation_unit_type_id = aut.id
            INNER JOIN '.DATABASENAME.'.periods p ON autp.period_id = p.id
            WHERE autp.company_id='.( $company_id ?? $_SESSION['COMPANY'] ).' ORDER BY aut.id';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function get_accommodation_fee( $accommodation_unit_id, $guests, $day, $days ){
    $weekday = date( 'N', strtotime( $day ));
    $day = substr( $day, -5 );
    $sql = 'SELECT * FROM accommodation_unit_prices ap
        INNER JOIN periods pe ON ap.period_id = pe.id
        INNER JOIN prices pr ON ap.price_id = pr.id
        WHERE ap.accommodation_unit_id = ?
          AND pr.minfo<=? AND pr.maxfo>=?
          AND pe.start<=? AND pe.end>=?
          AND pr.minnap<=?
          AND MID(pr.hetnap,?,1)="1" ORDER BY pe.priority DESC LIMIT 1';
    return self::query( $sql, [$accommodation_unit_id, $guests, $guests, $day, $day, $days,$weekday], 'iiissii' )->get_result()->fetch_assoc();
  }

  public static function list_accommodationFee( $accommodation_unit_id, $weekday ){
    $sql = 'SELECT * FROM accommodation_unit_type_prices autp
        INNER JOIN periods p ON autp.period_id = p.id
        INNER JOIN accommodation_units au ON autp.accommodation_unit_type_id = au.accommodation_unit_type_id
        WHERE au.id = ?
          AND MID(autp.weekly_priced_days,?,1)="1" ORDER BY p.priority DESC';
    return self::query( $sql, [$accommodation_unit_id,$weekday], 'ii' )->get_result()->fetch_all( MYSQLI_ASSOC );
  }

  public static function list_curentPrices( $accommodation_unit_id ){
    $sql = 'SELECT autp.*,p.name period_name FROM accommodation_unit_type_prices autp
        INNER JOIN periods p ON autp.period_id = p.id
        INNER JOIN accommodation_units au ON autp.accommodation_unit_type_id = au.accommodation_unit_type_id
        WHERE au.id = ?
        ORDER BY p.priority';
    return self::query( $sql, [$accommodation_unit_id], 'i' )->get_result()->fetch_all( MYSQLI_ASSOC );
  }

  public static function list_accommodationUnits( $where = null, $company_id = null ){
    $sql = 'SELECT au.*, aut.places, aut.name type_name,
                   IF(MID(au.signs,1,1) = 2,"Látható","Rejtve") status_str, MID(au.signs,1,1) status,
                   IF((SELECT fl.foglallakoegyseg_id FROM '.DATABASENAME.'.foglallakoegyseg fl WHERE fl.lakoegyseg_id=au.id LIMIT 1),1,0) is_trash_empty
              FROM '.DATABASENAME.'.accommodation_units au
        INNER JOIN '.DATABASENAME.'.accommodation_unit_types aut ON au.accommodation_unit_type_id = aut.id
             WHERE aut.company_id='.( $company_id ?? $_SESSION['COMPANY'] );
    if( $where ?? 0) $sql .= ' AND '.$where;
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function list_accommodationUnitFeatures( $feature_ids ){
    $sql = 'SELECT * FROM '.DATABASENAME.'.sys_features WHERE id IN('.$feature_ids.')';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function list_reviews( $company_id = null, $where = null ){
    $sql = 'SELECT r.*,p.name provider,
                   IF(MID(r.signs,1,1) = 2,"Látható","Rejtve") allapotstr,
                   MID(r.signs,1,1) status
              FROM '.DATABASENAME.'.reviews r
         LEFT JOIN '.DATABASENAME.'.sys_providers p ON r.provider_id = p.id
             WHERE r.company_id='.( $company_id ?? $_SESSION['COMPANY'] ?? 0 );
    if( $where ?? 0 ) $sql .= ' AND '.$where;
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function list_ratings( $company_id = null, $where = null ){
    $sql = 'SELECT r.*,p.name provider,
                   MID(r.signs,1,1) view_rewiew,
                   MID(r.signs,2,1) view_rating,
                   IF(r.provider_id IN(2,3),5,10) max_rating
              FROM '.DATABASENAME.'.ratings r
        INNER JOIN '.DATABASENAME.'.sys_providers p ON r.provider_id = p.id
             WHERE r.company_id='.( $company_id ?? $_SESSION['COMPANY'] ?? 0 );
    if( $where ?? 0 ) $sql .= ' AND '.$where;
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function list_penz( $foglalas_id = 0 ){
    $sql = 'SELECT *,penz_id id FROM '.DATABASENAME.'.penz';
    if( $foglalas_id )
      $sql.= ' WHERE foglalas_id='.$foglalas_id;
    $sql.= ' ORDER BY mikor';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function get_fizetve( $foglalas_id, $ceg_id = 0 ){
    $sql = 'SELECT SUM( osszeg_huf ) fizetve FROM '.DATABASENAME.'.penz WHERE ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ).' AND foglalas_id=?';
    return self::query( $sql, [ $foglalas_id ], 'i' )->get_result()->fetch_assoc();
  }

  public static function del_fizetve( $penz_id ){
    $sql = 'DELETE FROM '.DATABASENAME.'.penz WHERE penz_id=? LIMIT 1';
    self::query( $sql, [ $penz_id ], 'i' )->get_result();
  }
  
  public static function list_galeria( $ceg_id = 0 ){
    $sql = 'SELECT * FROM '.DATABASENAME.'.galeria WHERE ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ).' ORDER BY neve';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }
  public static function get_galeria( $galeria_id ){
    if( is_numeric($galeria_id)){
      $sql = 'SELECT * FROM '.DATABASENAME.'.galeria WHERE galeria_id=? LIMIT 1';
      $res = self::query( $sql, [ $galeria_id ], 'i' )->get_result()->fetch_assoc();
    }elseif( count( $galeria_id )){
      $galeria_id = implode( ',', $galeria_id );
      $sql = 'SELECT * FROM '.DATABASENAME.'.galeria WHERE galeria_id IN(?)';
      $res = self::query( $sql, [ $galeria_id ] )->get_result()->fetch_all( MYSQLI_ASSOC );
    }else $res = false;
    return $res;
  }

  public static function list_foglalt( $nap = 0, $ceg_id = 0 ){
    $nap = ( $nap )? $nap : date( 'Y-m-d' );
    $sql = 'SELECT fl.*, l.name,lt.places ferohely, DATE_ADD(erkezes,INTERVAL JSON_LENGTH(napokfo) DAY) tavozas,
                   JSON_LENGTH(napokfo) ej, cs.name megnevezes, f.kapcsolattarto, f.foglalva, f.eloleg, f.fizetendo,f.megjegyzes,f.vendegfo,f.email,f.telefon,
                   (SELECT SUM(osszeg_huf) FROM '.DATABASENAME.'.penz p WHERE p.foglalas_id=f.foglalas_id) fizetve
            FROM '.DATABASENAME.'.foglallakoegyseg fl
            INNER JOIN '.DATABASENAME.'.foglalas f ON fl.foglalas_id=f.foglalas_id
            INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
            INNER JOIN '.DATABASENAME.'.accommodation_unit_types lt ON lt.id=l.accommodation_unit_type_id
            LEFT JOIN '.DATABASENAME.'.booking_channels cs ON cs.id=f.csatorna_id
            WHERE lemondva IS NULL
            AND f.ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ).'
            AND DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY)>="'.$nap.'"
            ORDER BY l.sort_by, erkezes';
    return self::query( $sql )->fetch_all( MYSQLI_ASSOC );
  }

  public static function list_foglalErkez( $nap, $ceg_id = 0 ){
    $nap = ( $nap )? $nap : date( 'Y-m-d' );
    $sql = 'SELECT fl.foglalas_id id,fl.*, l.name, lt.places ferohely, DATE_ADD(erkezes,INTERVAL JSON_LENGTH(napokfo) DAY) tavozas,
                   JSON_LENGTH(napokfo) ej, cs.name megnevezes, f.kapcsolattarto, f.foglalva, f.eloleg, f.fizetendo,f.megjegyzes,f.vendegfo,f.email,f.telefon
            FROM '.DATABASENAME.'.foglallakoegyseg fl
            INNER JOIN '.DATABASENAME.'.foglalas f ON fl.foglalas_id=f.foglalas_id
            INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
            INNER JOIN '.DATABASENAME.'.accommodation_unit_types lt ON lt.id=l.accommodation_unit_type_id
            LEFT JOIN '.DATABASENAME.'.booking_channels cs ON cs.id=f.csatorna_id
            WHERE (MID(lemondva,1,1)="0" OR lemondva IS NULL)
            AND f.ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ).'
            AND erkezes="'.$nap.'" AND f.vendegfo>-1
            ORDER BY l.sort_by';
    return self::query( $sql )->fetch_all( MYSQLI_ASSOC );
  }

  public static function list_foglalTavoz( $nap, $ceg_id = 0 ){
    $nap = ( $nap )? $nap : date( 'Y-m-d' );
    $sql = 
      'SELECT fl.foglalas_id id, fl.*, l.name, lt.places ferohely,
              DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY) tavozas, JSON_LENGTH(napokfo) ej, cs.name megnevezes,
              f.kapcsolattarto, f.foglalva, f.eloleg, f.fizetendo, f.megjegyzes, f.vendegfo,f.email,f.telefon
         FROM '.DATABASENAME.'.foglallakoegyseg fl
   INNER JOIN '.DATABASENAME.'.foglalas f ON fl.foglalas_id=f.foglalas_id
   INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
   INNER JOIN '.DATABASENAME.'.accommodation_unit_types lt ON lt.id=l.accommodation_unit_type_id
    LEFT JOIN '.DATABASENAME.'.booking_channels cs ON cs.id=f.csatorna_id
        WHERE (MID(lemondva,1,1)="0" OR lemondva IS NULL)
          AND f.ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ).'
          AND DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY)="'.$nap.'" AND f.vendegfo>-1
     ORDER BY l.sort_by';
    return self::query( $sql )->fetch_all( MYSQLI_ASSOC );
  }

  public static function list_foglalFoglal( $nap, $ceg_id = 0 ){
    $nap = ( $nap )? $nap : date( 'Y-m-d' );
    $sql = 'SELECT fl.foglalas_id id, fl.*, l.name, lt.places ferohely, DATE_ADD(erkezes,INTERVAL JSON_LENGTH(napokfo) DAY) tavozas,
                   JSON_LENGTH(napokfo) ej, cs.name megnevezes, f.kapcsolattarto, f.foglalva, f.eloleg, f.fizetendo,f.megjegyzes,f.vendegfo,f.email,f.telefon
            FROM '.DATABASENAME.'.foglallakoegyseg fl
            INNER JOIN '.DATABASENAME.'.foglalas f ON fl.foglalas_id=f.foglalas_id
            INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
            INNER JOIN '.DATABASENAME.'.accommodation_unit_types lt ON lt.id=l.accommodation_unit_type_id
            LEFT JOIN '.DATABASENAME.'.booking_channels cs ON cs.id=f.csatorna_id
            WHERE f.ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ).'
            AND MID(foglalva,1,10)="'.$nap.'" AND f.vendegfo>-1
            ORDER BY l.sort_by';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ?? [] ) > 0 )? $list : 0;
  }

  public static function get_foglalas( $foglalas_id ){
    $sql = '
      SELECT f.*, MID(foglalva,6,11) foglalvastr, f.foglalas_id id,fl.erkezes,
             DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY) tavozas, MID(DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY),4) tavozasstr,
             MID(erkezes,4) erkezesstr,c.type foglalasicsatorna,c.name megnevezes,
             (SELECT SUM(osszeg_huf) FROM '.DATABASENAME.'.penz p WHERE p.foglalas_id=f.foglalas_id) as fizetve
      FROM '.DATABASENAME.'.foglalas f
      INNER JOIN '.DATABASENAME.'.foglallakoegyseg fl ON f.foglalas_id=fl.foglalas_id
      INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
      LEFT JOIN '.DATABASENAME.'.booking_channels c ON c.id=f.csatorna_id
      WHERE f.foglalas_id=? LIMIT 1
    ';
    $res = self::query( $sql, [ $foglalas_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }
  public static function del_foglalas( $foglalas_id ){
    $sql = 'DELETE FROM '.DATABASENAME.'.foglalas WHERE foglalas_id=? LIMIT 1';
    self::query( $sql, [ $foglalas_id ], 'i' )->get_result();
  }
  
  public static function save_foglalas( $adat, $foglalas_id = 0 ){
    $res = self::save( DATABASENAME.'.foglalas', $adat, ( $foglalas_id )? ['foglalas_id', $foglalas_id, 'i'] : false );
    return $res;
  }
  public static function get_foglalasCsatorna( $csatorna_id, $kulsoazonosito, $ceg_id = 0 ){
    $sql =
      'SELECT f.* FROM '.DATABASENAME.'.foglallakoegyseg fl '.
      'INNER JOIN '.DATABASENAME.'.foglalas f ON fl.foglalas_id=f.foglalas_id'.
      ' WHERE f.ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ).
      ' AND f.csatorna_id=? AND fl.kulsoazonosito=? LIMIT 1';
    return self::query( $sql, [ $csatorna_id, $kulsoazonosito ], 'is' )->get_result()->fetch_assoc();
  }
  public static function save_foglalasCsatorna( $adat, $ceg_id = 0 ){
    $sql = 'INSERT INTO '.DATABASENAME.'.foglalas SET csatorna_id=?, foglalva=?, kapcsolattarto=?, ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] );
    $res = self::query( $sql, $adat, 'iss' )->insert_id;
    return $res;
  }

  public static function list_foglalszolgaltatas( $foglalas_id ){
    $sql = 'SELECT fsz.*,s.name FROM '.DATABASENAME.'.foglalszolgaltatas fsz
            INNER JOIN '.DATABASENAME.'.services s ON fsz.szolgaltatas_id=s.id
            WHERE fsz.foglalas_id=?';
    $res = self::query( $sql, [$foglalas_id], 'i' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }

  public static function save_foglalszolgaltatas( $adat, $foglalszolgaltatas_id = 0 ){
    $res = self::save( DATABASENAME.'.foglalszolgaltatas', $adat, ( $foglalszolgaltatas_id )? ['foglalszolgaltatas_id', $foglalszolgaltatas_id, 'i'] : false );
    return $res;
  }

  public static function get_foglalasMikor( $foglalas_id ){
    $sql = 'SELECT * FROM '.DATABASENAME.'.foglallakoegyseg WHERE foglalas_id=? LIMIT 1';
    return self::query( $sql, [$foglalas_id], 'i' )->get_result()->fetch_assoc();
  }

  public static function del_foglallakoegyseg( $foglallakoegyseg_id, $foglalas_id = null ){
    if( $foglalas_id ?? 0 )
      $sql = 'DELETE FROM '.DATABASENAME.'.foglallakoegyseg WHERE foglalas_id=?';
    else
      $sql = 'DELETE FROM '.DATABASENAME.'.foglallakoegyseg WHERE foglallakoegyseg_id=? LIMIT 1';
    self::query( $sql, [ $foglalas_id ?? $foglallakoegyseg_id ], 'i' )->get_result();
  }

  public static function del_foglallakoegysegek( $foglalas_id ){
    $sql = 'DELETE FROM '.DATABASENAME.'.foglallakoegyseg WHERE foglalas_id=?';
    self::query( $sql, [ $foglalas_id ], 'i' )->get_result();
  }

  public static function list_foglallakoegyseg( $foglalas_id ){
    $sql = 'SELECT fl.*, l.name FROM '.DATABASENAME.'.foglallakoegyseg fl
            INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
            WHERE fl.foglalas_id=?';
    $res = self::query( $sql, [$foglalas_id], 'i' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function save_foglallakoegyseg( $adat, $foglallakoegyseg_id = 0 ){
    $res = self::save( DATABASENAME.'.foglallakoegyseg', $adat, ( $foglallakoegyseg_id )? ['foglallakoegyseg_id', $foglallakoegyseg_id, 'i'] : false );
    return $res;
  }

  public static function save_foglallakoegysegFoglal( $adat ){
    // !!! $adat[0] foglalas_id és a $adat[1] lakoegyseg_id mezőknek kell lennie !!!
    $sql = 'SELECT foglallakoegyseg_id FROM '.DATABASENAME.'.foglallakoegyseg WHERE foglalas_id=? AND lakoegyseg_id=? LIMIT 1';
    if( $foglallakoegyseg_id = self::query( $sql, [$adat[0][1], $adat[1][1]], 'ii' )->get_result()->fetch_assoc())
      $foglallakoegyseg_id = $foglallakoegyseg_id['foglallakoegyseg_id'];
    return self::save_foglallakoegyseg( $adat, $foglallakoegyseg_id );
  }

  public static function list_occupiedAccommodationUnits( $arrival, $departure, $company_id = null ){
    $sql = 'SELECT fl.lakoegyseg_id id FROM '.DATABASENAME.'.foglallakoegyseg fl
            INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
            WHERE l.company_id='.( $company_id ?? $_SESSION['COMPANY'] ).'
              AND (
                erkezes="'.$arrival.'"
                OR (erkezes<"'.$arrival.'" AND DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY)>"'.$arrival.'")
                OR (erkezes>"'.$arrival.'" AND "'.$departure.'">erkezes)
              )';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? array_map( fn( $item ) => $item['id'], $res ) : [];
    return ( count( $list ))? $list : 0;
  }

  public static function list_foglalstat( $ceg_id, $mettol ){
    $sql = 'SELECT f.foglalas_id, erkezes, JSON_LENGTH(napokfo) napok, csatorna_id, fizetendo
              FROM '.DATABASENAME.'.foglalas f
        INNER JOIN '.DATABASENAME.'.foglallakoegyseg fl ON f.foglalas_id=fl.foglalas_id
             WHERE ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY']).'
               AND erkezes>="'.$mettol.'" AND f.vendegfo>-1
               AND (MID(lemondva,1,1)="0" OR lemondva IS NULL)
          ORDER BY f.foglalas_id';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }

  public static function list_foglalAktualis( $lakoegyseg_id, $csatorna_id = 0, $ceg_id = 0 ){
    $sql = 'SELECT erkezes, DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY) tavozas, csatorna_id, fl.kulsoazonosito, f.foglalas_id, fl.foglallakoegyseg_id FROM '.DATABASENAME.'.foglalas f
            INNER JOIN '.DATABASENAME.'.foglallakoegyseg fl ON f.foglalas_id=fl.foglalas_id
            WHERE lakoegyseg_id='.$lakoegyseg_id.'
              AND DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY)>="'.date('Y-m-d').'"
              AND (MID(lemondva,1,1)="0" OR lemondva IS NULL)
              '.(( $ceg_id ?? 0 )? 'AND ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY'] ) : '' ).'       
              '.(( $csatorna_id )? ' AND csatorna_id='.$csatorna_id : '' ).'
            ORDER BY erkezes';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }
  public static function list_foglalasok( &$piece = 0, $where = '', $orderby = null, $limit = null, $offset = null, $ceg_id = 0 ){
    $ceg_id = ( $ceg_id )? $ceg_id : $_SESSION['COMPANY'];
    $counts = 'f.*,l.name,MID(foglalva,6,11) foglalvastr ,f.foglalas_id id, DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY) tavozas, MID(DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY),4) tavozasstr, MID(erkezes,4) erkezesstr,c.type foglalasicsatorna,c.name megnevezes,
    (SELECT SUM(osszeg_huf) FROM '.DATABASENAME.'.penz p WHERE p.ceg_id=? AND p.foglalas_id=f.foglalas_id) as fizetve';
    $sql = ' FROM '.DATABASENAME.'.foglalas f
            INNER JOIN '.DATABASENAME.'.foglallakoegyseg fl ON f.foglalas_id=fl.foglalas_id
            INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
            LEFT JOIN '.DATABASENAME.'.booking_channels c ON c.id=f.csatorna_id
            WHERE f.vendegfo>=0 AND f.ceg_id=?'.( $where );
    
    $query = 'SELECT COUNT(*) piece'.$sql;
    if( $res = self::query( $query, [$ceg_id], 'i' )->get_result())
      $res = $res->fetch_assoc();
    $piece = $res ? $res['piece'] : 0;

    $sql = 'SELECT '.$counts.$sql;
    if( $orderby ?? 0 )
      $sql.= ' ORDER BY '.$orderby;
    else
      $sql.= ' ORDER BY IF(MID(IF(lemondva IS NULL, "0", lemondva),1,1)="0",0,1), lemondva DESC, foglalva DESC';
    if( $limit ?? 0 )
      if( $offset ?? 0 )
        $sql.= ' LIMIT '.(( $offset - 1 ) * $limit ).','.$limit;
      else
        $sql.= ' LIMIT '.$limit;
    $res = self::query( $sql, [$ceg_id, $ceg_id], 'ii' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }
  public static function list_foglalasokPenz( $ceg_id = 0 ){
    $ceg_id = ( $ceg_id )? $ceg_id : $_SESSION['COMPANY'];
    $sql = 'SELECT *,
            (SELECT SUM(osszeg_huf) FROM '.DATABASENAME.'.penz p WHERE p.ceg_id=? AND p.foglalas_id=f.foglalas_id) fizetve,
            (SELECT erkezes FROM '.DATABASENAME.'.foglallakoegyseg fl WHERE fl.foglalas_id=f.foglalas_id ORDER BY erkezes LIMIT 1 ) erkezes
            FROM '.DATABASENAME.'.foglalas f
            WHERE f.ceg_id=?';
    $res = self::query( $sql, [$ceg_id, $ceg_id], 'ii' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }
  public static function list_report( $ceg_id, $mettol, $meddig ){
    $sql = 'SELECT csatorna_id, bc.name, fizetendo revenue,
                   (SELECT SUM(JSON_LENGTH(fl2.napokfo))
                      FROM '.DATABASENAME.'.foglallakoegyseg fl2
                     WHERE fl2.foglalas_id=f.foglalas_id) ej,
                   (SELECT SUM(osszeg_huf) FROM '.DATABASENAME.'.penz p WHERE p.foglalas_id=f.foglalas_id) paid
              FROM '.DATABASENAME.'.foglalas f
        INNER JOIN '.DATABASENAME.'.foglallakoegyseg fl ON f.foglalas_id=fl.foglalas_id
         LEFT JOIN '.DATABASENAME.'.booking_channels bc ON f.csatorna_id=bc.id
             WHERE ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY']).'
               AND fl.erkezes>="'.$mettol.'" AND fl.erkezes<="'.$meddig.'"
               AND (MID(f.lemondva,1,1)="0" OR f.lemondva IS NULL) AND f.vendegfo>-1
          GROUP BY f.csatorna_id, f.foglalas_id
          ORDER BY ej DESC';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ))? $list : 0;
  }
  public static function get_total_night( $ceg_id, $mettol, $meddig ){
    $closed = 0;
    $sql = 'SELECT SUM(JSON_LENGTH(fl.napokfo)) closed_days
              FROM '.DATABASENAME.'.foglalas f
        INNER JOIN '.DATABASENAME.'.foglallakoegyseg fl ON f.foglalas_id=fl.foglalas_id
             WHERE ceg_id='.(( $ceg_id )? $ceg_id : $_SESSION['COMPANY']).'
               AND fl.erkezes>="'.$mettol.'" AND fl.erkezes<="'.$meddig.'"
               AND f.vendegfo=-1';
    if( $res = self::query( $sql )->fetch_assoc())
      $closed = $res['closed_days'];
    $opened = db::get_piece( DATABASENAME.'.accommodation_units', 'company_id='.$_SESSION['COMPANY'] );
    $opened = $opened * abs( strtotime( $meddig ) - strtotime( $mettol )) / ( 24 * 60 * 60 );
    return [ 'closed' => $closed, 'opened' => $opened ];
  }
  // Adott lakóegység adott időszakában lévő napok foglaltsága. Tömbbel tér vissza [0,0,1,1,0] ahol 0 szabad, 1 foglalt.
  public static function get_reserved_days( $start_day, $end_day, $accommodation_unit_id ){
    $days = [];
    $difday = round(( strtotime( $end_day ) - strtotime( $start_day )) / 24 / 60 / 60 );
    for( $i=0; $i<$difday; $i++ ) $days[] = 0;
    foreach( $days as $key => $status ){
      $sql = 'SELECT fl.erkezes, fl.napokfo
                FROM '.DATABASENAME.'.foglallakoegyseg fl
          INNER JOIN '.DATABASENAME.'.foglalas f ON f.foglalas_id=fl.foglalas_id
              WHERE fl.lakoegyseg_id='.$accommodation_unit_id.'
                AND fl.erkezes<="'.date( 'Y-m-d', strtotime( $start_day.' +'.$key.' day' )).'"
                AND DATE_ADD(fl.erkezes, INTERVAL JSON_LENGTH(fl.napokfo) DAY)>"'.date( 'Y-m-d', strtotime( $start_day.' +'.$key.' day' )).'"
                AND f.lemondva IS NULL';
      if( $rows = self::query( $sql )->fetch_all( MYSQLI_ASSOC )){
        foreach( $rows as $row )
          if( $row['erkezes'] == date( 'Y-m-d', strtotime( $start_day.' +'.$key.' day' ))){
            $napokfo = json_decode( $row['napokfo'], true );
            foreach( $napokfo as $nap => $fo )
              if( date( 'Y-m-d', strtotime( $start_day.' +'.( $key + $nap ).' day' )) <= $end_day )
                $days[( $key + $nap )] = ( $days[( $key + $nap )] )? 2 : 1;
          }else $days[$key] = 1;
      }
    }
    return $days;
  }
  // egy foglalás minden adata
  public static function get_booking_info( $booking_id ){
    if( $booking = self::get( DATABASENAME.'.foglalas', 'foglalas_id='.$booking_id )){
      $sql = 'SELECT *, DATE_ADD(erkezes, INTERVAL JSON_LENGTH(napokfo) DAY) tavozas
                FROM '.DATABASENAME.'.foglallakoegyseg fl
          INNER JOIN '.DATABASENAME.'.accommodation_units l ON l.id=fl.lakoegyseg_id
               WHERE fl.foglalas_id=? ORDER BY erkezes, tavozas';
      if( $res = self::query( $sql, [ $booking_id ], 'i' )->get_result()->fetch_all( MYSQLI_ASSOC )){
        $lakoegysegek = [];
        $key = '';
        foreach( $res as $row )
          if( $row['erkezes'].$row['tavozas'] != $key ){
            $lakoegysegek[] = [
              'erkezes' => $row['erkezes'],
              'tavozas' => $row['tavozas'],
              'lakoegyseg' => $row['name']
            ];
            $key = $row['erkezes'].$row['tavozas'];
          }else
            $lakoegysegek[ count( $lakoegysegek ) - 1 ]['lakoegyseg'].= ', '. $row['name'];
        $booking['accommodation_units'] = $lakoegysegek;
      }

      $sql = 'SELECT s.name, IF(mennyi = 0, ar, mennyi*ar) dij
                FROM '.DATABASENAME.'.foglalszolgaltatas fs
          INNER JOIN '.DATABASENAME.'.services s ON s.id=fs.szolgaltatas_id
               WHERE fs.foglalas_id=?';
      if( $res = self::query( $sql, [ $booking_id ], 'i' )->get_result()->fetch_all( MYSQLI_ASSOC ))
        $booking['services'] = $res;
    }
    return $booking;
  }
  public static function save_blocked( $accommodation_unit_id, $start_day, $napokfo ){
    $foglalas_id = db::save_foglalas(
      [ ['ceg_id', $_SESSION['COMPANY'], 'i'],
        ['reg_id', $_SESSION['USER'], 'i'],
        ['foglalva', date( 'Y-m-d H:i:s' )],
        ['vendegfo', -1, 'i']
      ]
    );
    db::save_foglallakoegysegFoglal(
      [ ['foglalas_id', $foglalas_id, 'i'],
        ['lakoegyseg_id', $accommodation_unit_id, 'i'],
        ['erkezes', $start_day],
        ['napokfo', json_encode( $napokfo, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES )]
      ]
    );
  }

  public static function list_invoices( $where = 0 ){
    $sql = 'SELECT *, MID(created,1,10) issue_date FROM '.DATABASENAME.'.invoices WHERE company_id='.$_SESSION['COMPANY'];
    if( $where ) $sql.= ' AND '.$where;
    $sql.= ' ORDER BY id DESC';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }
  
  public static function get_invoice( $where ){
    $sql = 'SELECT *, MID(created,1,10) issue_date FROM '.DATABASENAME.'.invoices WHERE company_id='.$_SESSION['COMPANY'];
    if( is_integer( $where )) $sql.= ' AND id='.$where;
    else $sql.= ' AND '.$where;
    $sql.= ' LIMIT 1';
    if( !$res = self::query( $sql )->fetch_assoc()) return 0;
    $res['supplier'] = json_decode( $res['supplier'], true );
    $res['customer'] = json_decode( $res['customer'], true );
    $res['items'] = self::list_invoiceItems( $res['id'] );
    foreach( $res['items'] as $key => $item ){
      if( $item['unit'] == 'PIECE' )$res['items'][$key]['unitName'] = 'db';
    }
    return $res;
  }

  public static function save_invoice( $data, $invoice_id = 0 ){
    return self::save( DATABASENAME.'.invoices', $data, intval( $invoice_id ));
  }

  public static function list_invoiceItems( $invoice_id ){
    $sql = 'SELECT ii.*, tr.name tax_name, tr.rate tax_rate FROM '.DATABASENAME.'.invoice_items ii
        INNER JOIN '.DATABASENAME.'.tax_rates tr ON tr.id=ii.tax_rate_id
             WHERE ii.invoice_id='.$invoice_id.'
          ORDER BY ii.price_net DESC';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) > 0 )? $list : 0;
  }

  public static function save_invoiceItem( $data, $id = 0 ){
    return self::save( DATABASENAME.'.invoice_items', $data, intval( $id ));
  }

  public static function get_taxCate( $case ){
    $sql = 'SELECT id FROM '.DATABASENAME.'.tax_rates WHERE company_id='.$_SESSION['COMPANY'].' AND name=? LIMIT 1';
    return self::query( $sql, [ $case ] )->get_result()->fetch_assoc();
  }

  public static function list_bookingNotInvoice( $date ){
    user_error( $date );
    $sql = 'SELECT DISTINCT f.foglalas_id, MID(foglalva, 3, 8) foglalva, fizetendo, eloleg, kapcsolattarto
            FROM '.DATABASENAME.'.foglalas f
            INNER JOIN '.DATABASENAME.'.foglallakoegyseg fl ON f.foglalas_id=fl.foglalas_id AND DATE_ADD(erkezes,INTERVAL JSON_LENGTH(napokfo) DAY)>=?
            WHERE f.vendegfo>=0 AND foglalva<=? AND f.ceg_id='.$_SESSION['COMPANY'].' ORDER BY foglalas_id DESC';
    return self::query( $sql, [ $date, $date ] )->get_result()->fetch_all( MYSQLI_ASSOC );
  }
}