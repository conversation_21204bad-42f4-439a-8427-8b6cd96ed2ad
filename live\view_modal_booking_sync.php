<header>
  <h6>Lakóeg<PERSON><PERSON><PERSON> szinkron<br><?= response::$vw->view->accommodationunit->name ?></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <?php if(( response::$vw->view->bookingChannelsNonSync ?? 0 ) or ( response::$vw->view->bookingSynchros ?? 0 )){ ?>
  <?php   if( response::$vw->view->bookingChannelsNonSync ?? 0 ){ ?>
  <form name="form_add_channel">
    <ul class="formbox">
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="csatorna_id" placeholder="">
          <?php foreach( response::$vw->view->bookingChannelsNonSync as $bookingChannel ){ ?>
          <option value="<?= $bookingChannel->id ?>"><?= $bookingChannel->type.' '.$bookingChannel->name ?></option>
          <?php } ?>
        </select>
        <label>Új csatorna</label>
      </li>
      <li class="icons col0">
        <button name="btn_add_channel" title="Csatorna hozzáadása a szinkronhoz">Hozzáad</button>
      </li>
    </ul>
  </form>
  <hr>
  <?php   } ?>
  <?php   if( response::$vw->view->bookingSynchros ?? 0 ){ ?>
  <?php     foreach( response::$vw->view->bookingSynchros as $csatorna ){ ?>
  <form name="form_edit_channel">
    <input type="hidden" name="szinkron_id" value="<?= $csatorna->szinkronnaptar_id ?>">
    <ul class="formbox">
      <li class="form col4">
        <input type="text" placeholder="" value="<?= $csatorna->type ?>" disabled>
        <label>Csatorna</label>
      </li>
      <li class="form col6">
        <input type="text" placeholder="" value="<?= $csatorna->name ?>" disabled>
        <label>Megnevezés</label>
      </li>
      <?php if( $csatorna->description ?? 0 ){ ?>
      <li class="form col0">
        <textarea disabled><?= html_entity_decode( $csatorna->description ) ?></textarea>
        <label>Leírás</label>
      </li>
      <?php } ?>
      <?php if( $csatorna->signs[1] == '1' ){ ?>
      <li class="form col0">
        <input type="text" placeholder="" value="<?= 'https://tren.hu/upload/ical/'.ical::get_export_filename( http::$route[3] ) ?>" disabled>
        <label>Link a közvetítő oldalnak (küldés)</label>
      </li>
      <li class="form col0">
        <input type="text" placeholder="" value="<?= $csatorna->link ?>" name="link">
        <label>Kapott link a közvetítő oldaltól (fogadás)</label>
      </li>
      <li class="form col35">
        <input type="text" placeholder="" value="<?= substr( $csatorna->utolso ?? '', 2, -3 ) ?>" disabled>
        <label>Utolsó küldés</label>
      </li>
      <li class="form col15">
        <?php /*
        <a class="btn send" style="--icon:var(--icon-arrows-cw)" href="" title="Frissítés"></a>
        */ ?>
      </li>
      <li class="form col35">
        <input type="text" placeholder="" value="<?= substr( $csatorna->utolso ?? '', 2, -3 ) ?>" disabled>
        <label>Utolsó fogadás</label>
      </li>
      <li class="form col15">
        <?php /*
        <a class="btn receive" style="--icon:var(--icon-arrows-cw)" href="" title="Frissítés"></a>
        */ ?>
      </li>
      <?php } ?>
      <li class="col0">
        <?php if( $csatorna->signs[1] == '1' ){ ?>
        <button name="btn_save_channel" title="Csatorna beállítások mentése">Mentés</button>
        <?php } ?>
        <a class="btn del_channel" data-szinkronnaptarid="<?= $csatorna->szinkronnaptar_id ?>" title="Csatorna beállítások törlése">Törlés</a>
      </li>
    </ul>
  </form>
  <hr>
  <?php } } }else{ ?>
    <p>Előbb válassz ki csatornákat ehhez a beállításhot!</p>
    <a href="/foglalasicsatorna">Tovább a csatornákhoz</a>
  <?php } ?>
</section>
<footer>
  <button class="close">Kilép</button>
</footer>