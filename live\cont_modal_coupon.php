<?php
if( $coupon = db::get( 'coupons', http::$route[3] ) ){
  $_POST['id'] = $coupon['id'];
  $_POST['name'] = $coupon['name'];
  $_POST['code'] = $coupon['code'];
  $_POST['status'] = $coupon['signs'][0];
  $_POST['fixed'] = $coupon['signs'][1];
  $_POST['multiple'] = $coupon['signs'][2];
  $_POST['validity_start'] = $coupon['validity_start'];
  $_POST['validity_end'] = $coupon['validity_end'];
  $_POST['booking_period_start'] = $coupon['booking_period_start'];
  $_POST['booking_period_end'] = $coupon['booking_period_end'];
  $_POST['weekly_start_day'] = $coupon['weekly_start_day'];
  $_POST['weekly_stop_day'] = $coupon['weekly_stop_day'];
  $_POST['min_booking_days'] = $coupon['min_booking_days'];
  $_POST['discount'] = $coupon['discount'];
  $_POST['description'] = $coupon['description'];
}