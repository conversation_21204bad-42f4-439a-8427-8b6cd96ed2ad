@charset "UTF-8";
/* Layout - minden oldara kiterjedő */

body{
  & main{
    container: main-size / inline-size;
    & > header{
      display: flex;
      justify-content: space-between;
      flex-flow: row wrap;
      align-items: center;
      position: relative;
      width: 100%;
      min-height: 3rem;
      background-color: var( --header-bg-color );
      & .header-nav {
        display: flex;
        justify-content: space-between;
        flex-flow: row wrap;
        align-items: center;
        width: 320px;
        padding: 0;
        margin: 0;
        list-style: none;
        & li{
          position: relative;
          &.logo{
            width: 220px;
            padding-right: 2rem;
            & a{
              & img{
                height: calc( 3rem - 2px );
                margin-left: 1rem;
              }
            }
          }             
          &.minimalize{
            width: 100px;
            height: 30px;
            display:contents;
            &>i:before{
              margin-right: 0;
              font-size: 2rem;
            }  
          }
        }
      }
      & .header-links {
        &>li{
          display: inline-block;
          &>a{
            position: relative;
            display: block;
            min-height: 3rem;
            padding: 1rem .5rem;
            font-size: 1rem;
            font-weight: 600;
            color: var(--header-link-color);
            &:hover{
              text-decoration: none;
              color: var(--header-link-hover-color);
            }
          }
          & details{
            &[open],
            &:hover{
              cursor: pointer;
              background-color: var(--secondary);
            }
            & summary{
              display: flex;
              align-items: center;
              padding-right: 1rem;
              gap: .2rem;
              & div{
                &.avatar{
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 2.4rem;
                  height: 2.4rem;
                  margin-left: .5rem;
                  border-radius:50%;
                  background-color:#fff;
                  &>img{ width: 90% }
                }
              }
            }
            &>div{
              position: absolute;
              right: 0;
              padding: 1rem;
              background-color: var(--secondary);
            }
          }
        }
      }
    }
    & > .wrapper{
      display: flex;
      flex-flow: row wrap;
      &>nav{
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        justify-content: space-evenly;
        width: 220px;
        min-height: calc(100vh - 3rem);
        background-color: var(--dashboard-bg-color);
        & > label.tabs{
          order: -1;
          position: relative;
          padding: 8px;
          margin-bottom: .4rem;
          margin-top: .4rem;
          color: var(--link-color);
          cursor: pointer;
          &:hover{ background: var(--dashboard-hover-bg-color) }
          &:has(input:checked){
            background: var(--dashboard-active-bg-color);
            + ul{ display: block }
          }
          & + ul{ display: none }
          & > input{
            width: 0;
            height: 0;
            position: absolute;
            opacity: 0;
          }
          & > i{ color: var(--danger-color) }
        }
        & > ul{
          width: 100%;
          &>li{
            position: relative;
            display: block;
            &>div{
              text-align: center;
              margin: 4px 0;
            }
            &>a{
              position: relative;
              display: block;
              padding: 7px 12px;
              font-weight: 600;
              color: var(--dashboard-color);
              &:hover{
                text-decoration: none;
                background-color: var(--dashboard-hover-bg-color);
                color: var(--dashboard-hover-color);
              }
              &>i{
                padding-right: .5rem;
                &.abb:first-child{ display: none }
                &.arrow{
                  float: right;
                  &:after{
                    display: inline-block;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    content: var(--icon-left-open);
                    font-family: FontIcon;
                    font-style: normal;
                    font-weight: normal;
                    line-height: 1;
                    margin-left: 1em;
                  }
                }
              }
            }
            & .nav-second{
              display: none;
              background: var(--dashboard-sec-bg-color);
              &>li{
                position: relative;
                display: block;
                &>a{
                  position: relative;
                  display: block;
                  padding: 7px 10px 7px 30px;
                  font-weight: 600;
                  color: var(--dashboard-sec-color);
                  &:hover{
                    text-decoration: none;
                    color: var(--dashboard-sec-hover-color);
                    background-color: var(--dashboard-sec-hover-bg-color);
                  }
                }
              }
            }
            &.nav__aktiv{
              border-left-width: 4px;
              border-left-style: solid;
              border-left-color: var(--dashboard-active-border-color);
              background-color: var(--dashboard-active-bg-color);
              & .nav-second{
                display: block;
                min-width: 216px;
                &>li>a>span{
                  &:first-child{ display: none }
                  &:last-child{ display: inline }
                }
                & a.nav__aktiv{
                  color: var(--dashboard-active-color) !important;
                  &>i.arrow:after{ content: var(--icon-right-open) }
                }
              }
              &.nav-second__aktiv .nav-second{
                min-width: 216px;
                &>li>a>span{
                  &:first-child{ display: none }
                  &:last-child{ display: inline }
                }
              }
              &>a{
                color: var(--dashboard-active-color);
                &>i.arrow:after{ content: var(--icon-right-open) }
              }
            }
          }
        }
        &.mininav{
          width: 60px;
          & > label.tabs{
            &>span{ display: none }
          }
          & > ul{
            &>li{
              &>a>span{
                display: none;
                &:first-child{ display: inline }
                &:last-child{ display: none }
              }
              &.nav__aktiv .nav-second{
                min-width: 56px;
                &>li>a{
                  padding-left: 20px;
                  &>span{
                    &:first-child{ display: inline }
                    &:last-child{ display: none }
                  }
                  /*&.nav__aktiv{ background-color: var(--dashboard-active-bg-color) }*/
                }
              }
            }
          }
          & i{
            &.arrow:after,
            &.icon + span, nav.min.inav i.abb + span{ display: none }
            &.abb{ display: inline }
          }
        }
        &.zeronav{ display: none }
      }
      &>section{
        flex: 1 1 300px;
        font-size: clamp(.875rem, 1.4vw, 1.125rem );
        padding: clamp( 1rem, 2vw, 2rem ) clamp( 1rem, 2vw, 2rem ) 0;
      }
    }
  }
}

@container main-size ( max-width: 480px ){
  .header-nav { width: 100% }
}
@container main-size ( max-width: 600px ){
  section { flex-grow: 1 }
}

td>span{
  padding: .25rem;
  border-radius: .25rem;
  &[data-status_str="1"],
  &[data-allapotstr] { background-color: var( --warning-color ) }
  &[data-status_str="2"],
  &[data-allapotstr="0"] { background-color: var( --success-color ) }
  &[data-status_str="0"],
  &[data-allapotstr="1"] { background-color: var( --danger-color ) }
}

body#accommodationunittipes{
  & dialog{ max-width: 480px }
}

body#accommodationunits{
  & dialog{ max-width: 480px }
}

body#foglalas{
  & .cart{ width: 100% }
  & dialog{
    max-width: 480px;
    & li.icons{
      text-align: right;
      &>button{ float: left }
    }
  }
  & section.container{ width: calc(100% - 220px ) }
  & table{
    & td.icons{
      z-index: 2;
      position: sticky;
      right: 0px;
      background-color: var(--table-bg-color);
    }
  }
}

body#naptar{
  & dialog{
    max-width: 480px;
    & li.icons{
      text-align: right;
      &>button{ float: left }
    }
  }
  & section.container{
    width: calc(100% - 220px);
    & .cart{ width: 100% }
  }
  & [data-datatable="naptar"]{
    & table{
      border-spacing: 0 !important;
      border-collapse: collapse !important;
      &>thead{
        &>tr{
          &>th{
            position: sticky;
            top: 0px;
            &.celfix{
              left: 0px;
              z-index: 2;
            }
          }
        }
      }
      &>tbody{
        &>tr{
          &>td{
            min-height: 64px;
            border: 1px solid var(--table-border-color);
            position: relative;
            &>div{
              display:flex;
              justify-content: space-between;
              flex-flow: row wrap;
              align-items: center;
              height: 90%;
              z-index: 1;
              position: absolute;
              top: 5%;
              font-size: .9rem;
              line-height: initial;
              padding: .4em;
              &.notpay{ border: 1px solid red }
              &.statusZ{ background-color: var(--danger-color) }
              &.statusF{ background-color: var(--info-color) }
              &.statusB{ background-color: var(--success-color) }
              &.statusK{ background-color: var(--work-dark) }
              &>div{
                width: 100%;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
            &.kiemelt{ background-color: var(--work-lighter) }
          }
          &>th{
            &.szoba{
              z-index: 2;
              position: sticky;
              left: 0px;
              display: flex;
              align-items: center;
              height: max( 4rem , 88px );
              min-width: 6rem;
              border-right: 1px solid rgba(0, 0, 0, 0.3);
              background-color: var(--table-thead-bg-color);
              &>a{
                margin-left: 10px;
                &>i{ font-size: 2.6rem }
              }
            }
          }
        }
      }
    }
  }
  & .foginfo{
    background-color: var(--danger-color);
    color: var(--btn-color);
  }
  & .foglaltF{ background-color: var(--warning-color) }
  & .foglaltS{
    background-color: var(--lighter-color);
    &.foglalhato:hover{ background-color: var(--success-color) }
  }
  & .foglaltET {
    margin-left: -17%;
    border-radius: 10px/50px;
  }
  & .foglalt {
    margin-left: 42%;
    border-radius: 10px/50px;
  }
}

body#generates,
body#settings_profile{
  & .cart{
    width: 100%;
    max-width: 480px;
  }
}

body#settings_parameter{
  & .cart{
    width: 100%;
    max-width: 560px;
  }
}

body#settings_subscription{
  & .cart{
    width: 100%;
    max-width: 480px;
    & li>div{
      height: 200px;
      padding: 0.5rem;
      border: solid var(--btn-hover-bg-color);
      margin-bottom: 1rem;
      &>h5{
        text-align: center;
        font-size: 1.2rem;
        margin-bottom: 1rem;
      }
    }
  }
}

body#dashboard{
  & [name="form_flash_report"]{ width:calc(7 * clamp(0.875rem, 1.4vw, 1.125rem )) }
  & .cart .shema1{
    max-width: 304px;
    &>dl{
      &>dt{
        width: 100%;
        text-align: center;
        font-weight: bold;
        font-size: 2rem;
        color: var(--danger-color);
      }
      &>dd{
        width: 100%;
        &>ul{
          display: flex;
          gap: clamp(0.875rem, 1.4vw, 1.125rem );
          width: 100%;
          &>li{
            display: flex;
            flex-flow: row wrap;
            width: calc(50% - clamp(0.875rem, 1.4vw, 1.125rem)/2);
            &:last-child{ justify-content: end }
            &>span{
              display: inline-block;
              width: 100%;
              text-align: end;
              color: var(--danger-color)
            }
          }
        }
      }
    }
  }
  & .cart .shema2{
    max-width: 304px;
    &>dl{
      display: flex;
      &>dt{
        min-width: 20%;
        background-repeat: no-repeat;
      }
      &>dd{
        width: 80%;
        &>ul{
          &>li{
            display: flex;
            justify-content: space-between;
            &>span{
              display: inline-block;
              width: 30%;
              text-align: right;
              padding-right: 1rem;
            }
          }
        }
      }
    }
  }
}

body#website_documents{
  & .cart{ max-width: 710px }
}
body#website_galleries{
  & dialog{
    max-width: 99vw;
    & [data-upload]{ margin-top: .25rem }
  }
}
body#pricing_children_rules{
  & .cart{ max-width: 480px }
}
body#invoices,
body#message_messages,
body#reviews_ratings{
  & dialog{ max-width: 480px }
}
body#reviews_imports{
  & .cart{ max-width: 480px }
  & li.line-rating{
    display: flex;
    justify-content: center;
    gap: 0.3rem;
    padding-top: 1rem;
    &>img{ height: 1rem }
  }
}
body#pricing_periods,
body#pricing_prices,
body#popups,
body#services{
  & dialog{ max-width: 480px }
  & .selects-box{
    margin-bottom: .25rem;
    ul{ margin-top: .25rem }
  }
}
body#popups,
body#coupons{
  & dialog{ max-width: 680px }
}
body#automation{
  & article{
    background-color: initial !important;
    &>details{
      padding: .5rem;
      margin-top: .5rem;
      border-radius: .5rem;
      &[open]{ background-color: var(--cart-article-bg-color) }
    }
  }
}
body#login{
  & article{
    & .carts{
      &>.cart{
        position: relative;
        width: clamp( 300px, calc(50% - .5vw), calc(50% - .5vw));
        & h1{ padding-bottom: 1em }
      }
    }
  }
}