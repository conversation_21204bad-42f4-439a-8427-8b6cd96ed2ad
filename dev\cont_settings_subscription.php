<?php
if( $service_contracts = setup::list( 'shared.service_contracts', 'company_id = '.$_SESSION['COMPANY'].' AND project_id = '.$_SESSION['PROID'].' AND (termination_date IS NULL OR termination_date > NOW())' ) ){
  // van szerződése
  $_SESSION['SERVICE_RIGHTS'] = [];
  foreach( $service_contracts as $key => $contract )
    foreach( json_decode( $contract['fee_packages'], true ) as $package )
      if( $service_fee_package = setup::get( 'shared.service_fee_packages', $package, OBJECT_TYPE ) ){
        if( $service_fee_package->rights ?? 0 )
              foreach( str_split( $service_fee_package->rights ) as $key2 => $right )
                if( $right ) $_SESSION['COMPANY_RIGHTS'][ $key2 ] = $right;
        $service_contracts[ $key ]['packages'][] = $service_fee_package;
      }
}
response::add( 'view', 'service_contracts', $service_contracts ?? 0 );