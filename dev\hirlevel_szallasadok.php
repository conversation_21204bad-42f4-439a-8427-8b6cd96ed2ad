<?php
date_default_timezone_set( 'Europe/Budapest' );
const CLASSVERSIONS = [
  'errors'      => '1.4',
  'model_basic' => '1.0',
  'mysql'       => '2.2',
  'mailer'      => '1.1'
];
const SENDER_EMAIL = 'TREN ALL-IN-ONE<<EMAIL>>';
const DATABASENAME = 'tren';
require_once 'shared/class/autoload.php';
errors::start( 37 );

$html_head = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" lang="en">
<head>
<title></title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<!--[if !mso]><!-->
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<!--<![endif]-->
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="robots" content="no index">
<!--[if mso | ie]>
<style>
.cta_border{
padding: 0px !important;
border-bottom: 11px solid #1A73E8 !important;
border-top: 13px solid #1A73E8 !important;
border-right: 25px solid #1A73E8 !important;
border-left: 25px solid #1A73E8 !important;
}
</style>
<![endif]-->
</head>
<body>';

if( $template = db::get( 'shared.mail_campaigns', 1 ))
  if( $subs = db::list( 'shared.mail_sents', 'sending IS NULL LIMIT 100' ))
    foreach( $subs as $sub ){

      $body = str_replace( '[SENTID]', $sub['id'], $template['body'] );

      $ok = mailer::speedsend(
        '(#TM'.$sub['id'].') '.$template['subject'],
        $html_head.$body.'</body></html>',
        $sub['fromto'],
        SENDER_EMAIL
      );
      if( gettype( $ok ) == 'boolean' )
        db::save( 'shared.mail_sents', [['sending', date( 'Y-m-d H:i:s' )]], intval( $sub['id'] ));
      echo $sub['fromto'].'<br>';
      sleep( 2 );
    }
