  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Google értékelés import</h5>
      </header>
      <article>
        <form name="form_google_rating" method="post">
          <ul class="formbox">
            <li class="form col7">
              <input type="text" name="google_provider_key" placeholder="" value="<?= $_POST['google_provider_key'] ?? '' ?>">
              <label>Google place ID</label>
            </li>
            <li class="form col3">              
              <input type="submit" name="btn_frissites" title="Értékelések frissítése" value="Frissítés"></input>
            </li>
            <li class="form col5 chebef">
              <input type="checkbox" name="google_rating_visible" id="google_rating_visible" checked>
              <label for="google_rating_visible">Vélemény látható</label>
            </li>
            <li class="form col5 chebef">
              <input type="checkbox" name="google_reviews_visible" id="google_reviews_visible" checked>
              <label for="google_reviews_visible">Értékelés látható</label>
            </li>
            <li class="form col0 line-rating">
              <?php if( response::$vw->view->google_rating ?? 0 ){ ?>
              <img src="/items/google.png" alt="Google értékelés">
              <span>5/<b><?= round( response::$vw->view->google_rating->rating, 1 ) ?></b></span>
              <i style="--icon:var(--icon-star)"></i>
              <span><?= response::$vw->view->google_rating->reviews_number ?> vélemény</span>
              <?php } ?>
            </li>
          </ul>
        </form>
      </article>
    </div>
  </section>
  <script type="module">
    import { dialog, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd
      switch( ifunction ){
        case 'reviews_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_review' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/review',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Az értékelés módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'reviews_edit_close':
          location.replace( '/reviews' )
        break
        case 'reviews_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( { 
            url: '/modal/reviewdel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/reviews' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van az ártákelés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új értékelés',
          label: 'Értékelő neve'
        },
        eventManagerFunctions
      } )
    } )
  </script>