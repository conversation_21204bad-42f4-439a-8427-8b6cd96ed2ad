<?php
/**
 * Http vezérlés. Változók, url
 *
 * Változók lekezel<PERSON>e, elsődleges ellenőrzése, tisztítása (POST és GET).
 * Útválasztó feldolgozás, mely a linket dolgozza fel, darabolja
 *
 * @method `cleanPost();`      Megtisztítja az adot változó(k) értékeit (html tegek, szóközök) a POST-on belül
 * @method `cleanGet();`       Megtisztítja az adot változó(k) értékeit (html tegek, szóközök) a GET-en belül
 * @method `csrfToken();`      CSRF token generálása
 * @method `csrfCheck();`      CSRF token ellenőrzése
 * @method `collection();`     Tömb változóból elemek kiemelése tömb változóba / pl. adatbázis beolvasás mezőit POST-ba tesszük
 * @method `init();`           Inicializálás
 * @method `route();`          Hívás feldolgozása, útválasztó beállítása
 * @method `gourl();`          Adott linkre átirányítás
 * @method `s2u();`            Szöveg keresőbarát linké alakítása
 * @method `moneyFormat();`    Pénzösszeg formázás
 * @method `lastModified();`   Utolsó módosítás időpontja
 * 
 * <AUTHOR> Róbert <<EMAIL>>
 * @copyright Copyright (c) 2018, Tánczos Róbert
 * 
 * @version 2.2.2
 * @since 2.2.2 2025.05.01 Kód optimalizálás, kiegészítés
 * @since 2.2.1 2023.01.29 Kód optimalizálás
 * @since 2.2.0 2022.09.26 `lastModified();` fügvény bevezetése
 * @since 2.1.0 2022.08.16 `collection();`, `collectionData();` és `moneyFormat();` fügvény bevezetése
 * @since 2.0.0 2022.06.26 Új verzió követés. Új néven. Fájlnévben jelölve. Optimalizálás. `reset();`, `clean();`, `init();` megszüntetése
 * @since 1.4.0 2021.09.12 `cleanPost();` és `s2u();` fügvény bevezetése
 * @since 1.3.0 2020.04.15 `route();` fügvény új `$path` paramétere
 * @since 1.2.0 2020.02.29 `clean();` kiegészítése: ha `$key == 0`, akkor minden mezőre végrehajtódik
 * @since 1.0.0 2018.06.22
 */

class http{
  public static $url_request = false;
  public static $path        = '';
  public static $url_domain  = false;
  public static $route       = false;

  /** Tisztítja a bemeneti adatokat különböző szűrési módszerekkel. 
   *
   * @param  mixed  $data    A tisztítandó bemeneti adat
   * @param  string $filter  Szűrési mód: 'basic' (alap), 'html' (HTML engedélyezett), 'email', 'url', 'int', 'float'
   * @return mixed           A bemeneti adat tisztított változata
   */
  private static function _cleanInputs( $data, $filter = 'basic' ){
    if( is_array( $data )){
      $clean = [];
      foreach( $data as $key => $value ){
        $cleanKey = self::_sanitizeKey( $key );
        $clean[$cleanKey] = self::_cleanInputs( $value, is_array( $filter )? $filter[$key] ?? 'basic' : $filter );
      }
      return $clean;
    }
    
    switch ( $filter ){
      case 'html': return self::_sanitizeHtml( $data );
      case 'email': return filter_var( $data, FILTER_SANITIZE_EMAIL );
      case 'url': return filter_var( $data, FILTER_SANITIZE_URL );
      case 'int': return filter_var( $data, FILTER_SANITIZE_NUMBER_INT );
      case 'float': return filter_var( $data, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION );
      case 'basic':
      default: return trim( strip_tags( $data ));
    }
  }

  /** Kulcsok tisztítása
   * 
   * @param  string $key  A tisztítandó kulcs
   * @return string       A tisztított kulcs
   */
  private static function _sanitizeKey( $key ){
    return preg_replace( '/[^a-zA-Z0-9_]/', '', $key );
  }

  /** HTML tartalom biztonságos tisztítása
   * 
   * @param  string $html  A tisztítandó HTML
   * @return string        A tisztított HTML
   */
  private static function _sanitizeHtml( $html ){
    $dangerousTags = ['script', 'iframe', 'object', 'embed', 'style'];
    $dangerousAttrs = ['onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout', 'onkeydown', 'onkeyup'];
    
    foreach ($dangerousTags as $tag){
      $html = preg_replace( '/<\s*' . $tag . '[^>]*>.*?<\s*\/\s*' . $tag . '\s*>/is', '', $html );
      $html = preg_replace( '/<\s*' . $tag . '[^>]*>/is', '', $html );
    }
    
    foreach ($dangerousAttrs as $attr)
      $html = preg_replace( '/\s*' . $attr . '\s*=\s*(["\']).*?\1/i', '', $html );
    
    return trim( $html );
  }

  /** POST változó(k) tisztítása
   * 
   * @param  string|array $key     Melyik érték(ek) (mező neve vagy nevek tömbje)
   * @param  string       $filter  Szűrési mód(ok)
   * @return mixed                 Tisztított érték vagy tömb
   */
  public static function cleanPost( $key = 0, $filter = 'basic' ){
    if( $key === 0 ){
      $_POST = isset( $_POST )? self::_cleanInputs( $_POST, $filter ) : [];
      return $_POST;
    }
    
    if( is_array( $key )){
      foreach( $key as $k )
        $_POST[$k] = self::cleanPost( $_POST[$k], is_array( $filter )? $filter[$k] ?? 'basic' : $filter );
      return $_POST;
    }
  
    if( $_POST[$key] ?? 0 ){
      $_POST[$key] = self::_cleanInputs( $_POST[$key], $filter );
      return $_POST[$key];
    }
    
    return '';
  }

  /** GET változó(k) tisztítása
   * 
   * @param  string|array $key     Melyik érték(ek) (mező neve vagy nevek tömbje)
   * @param  string       $filter  Szűrési mód
   * @return mixed                 Tisztított érték vagy tömb
   */
  public static function cleanGet( $key = 0, $filter = 'basic' ){
    if( $key === 0 ){
      $_GET = isset( $_GET )? self::_cleanInputs( $_GET, $filter ) : [];
      return $_GET;
    }
    
    if( is_array( $key )){
      foreach( $key as $k )
        $_GET[$k] = self::cleanPost( $_GET[$k], is_array( $filter )? $filter[$k] ?? 'basic' : $filter );
      return $_GET;
    }
    
    if( isset( $_GET[$key] )){
      $_GET[$key] = self::_cleanInputs( $_GET[$key], $filter );
      return $_GET[$key];
    }
    
    return '';
  }

  /** CSRF token generálása
   *
   * @return string html input sort ad vissza 
   */
  public static function csrfToken(){
    $_SESSION['CSRF_TOKEN'] = bin2hex( random_bytes( 32 ));
    return '<input name="csrf_token" value="'.$_SESSION['CSRF_TOKEN'].'" type= "hidden">';    
  }

  /** CSRF token ellenőrzése
   *
   * @return boolean Érvényes esetben true, ha nem, akkor false
   */
  public static function csrfCheck(){
    $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
    return ( !$token or $token !== $_SESSION['CSRF_TOKEN'] )? false : true;
  }

  /** tömbből értékpárok kiemelése kulcs feltételekszerint
   * 
   * pl. adatbázis beolvasás mezőit POST-ba tesszük
   * 
   * @param  array  $array   amiből kiemelünk / kulcs érték párok
   * @param  array  $keys    amit kiemelünk / mezőnevek felsorolva
   * @param  array  $nokeys  amit kizárunk a kiemelésből / mezőnevek felsorolva
   * @return array           amibe kiemeltünk / kulcs érték párok
   */
  public static function collection( $array, $keys = 0, $nokeys = [] ){
    if( !is_array( $array ) or empty( $array )) return [];
    if( $keys ){
      $validKeys = array_diff( $keys, $nokeys );
      return array_intersect_key( $array, array_flip( $validKeys ));
    }else
      return array_diff_key( $array, array_flip( $nokeys ));
  }

  /** DEPRECATED tömbből értékpárok kiemelése kulcs megadás szerint
   * 
   * pl. POST-ból adatbázis tömbformába tesszük (minden 's')
   * 
   * @param  array  $array   amiből kiemelünk / kulcs érték párok
   * @param  array  $nokeys  amit kizárunk a kiemelésből / mezőnevek felsorolva
   * @return array           amibe kiemeltünk / [[kulcs, érték],...]
   */
  public static function collectionData( $array, $nokeys = [] ){
    return self::collection( $array, 0, $nokeys );
  }

  /** Alapértékek beállítása
   *
   * Domain beállítása
   * 
   */
  public static function init(){
    $scheme = filter_input( INPUT_SERVER, 'REQUEST_SCHEME', FILTER_SANITIZE_URL ) ?? 'http';
    $host = filter_input( INPUT_SERVER, 'HTTP_HOST', FILTER_SANITIZE_URL ) ?? '';
    if( preg_match( '/^[a-zA-Z0-9-_.]+(\.[a-zA-Z0-9-_.]+)*$/', $host ))
      self::$url_domain = htmlspecialchars( $scheme, ENT_QUOTES, 'UTF-8' ) . '://' . htmlspecialchars( $host, ENT_QUOTES, 'UTF-8' );
    else
      self::$url_domain = '';
  }

  /** Hívás feldolgozása, útválasztó beállítása
   * 
   * $_SERVER['REQUEST_URI'] változóból kinyeri az útvonalat,
   * majd a / jelek mentén tömbbe szedi az elemeket. Törli az útvonal elemeiből azokat az elemeket, amelyek '.'-ot tartalmaznak.
   * Ha megadtunk $path paramétert, akkor az első elemet kiveszi a tömbből. Ha a tömb üres, akkor visszaadja a $default paramétert.
   * 
   * @param array  $default alapértelmezett út pl. ['home']
   * @param string $path    alkönyvtárban van a gyökér akkor a neve, különben false. Levágjul az url-ből, hogy ne zavarjon
   * @return array          az útvonal tömbje
   */
  public static function route( $default = false, $path = false ){
    
    $url = filter_input( INPUT_SERVER, 'REQUEST_URI', FILTER_SANITIZE_URL );
    $url = ( strpos( $url, 'index.php' ) !== false )? substr( $url, 0, strpos( $url, 'index.php' )) : $url;
    $url = trim( $url, '/' );
    $url = ( strpos( $url, '?' ) !== false )? substr( $url, 0, strpos( $url, '?' )) : $url;
    $url = htmlspecialchars( $url, ENT_QUOTES, 'UTF-8' );
    self::$url_request = '/'.$url;
    $url = explode( '/', $url );
    foreach( $url as $k => $e ) if( strlen( $e ) == 0 ) unset( $url[$k] );
    if( $path ){
      array_shift( $url );
      self::$path = $path;
    }
    self::$route = ( count( $url ))? $url : $default;
  }

  /** DEPRECATED URL útválasztó beállítása
   * 
   */
  public static function route2( $default = false, $path = false ){
    self::route( $default, $path );
  }

  /** Átirányítás adott linkre (301)
   * 
   * @param string link
  */
  public static function gourl( $link = '' ){
    
    
  }

  /** String URL-baráttá alakítása
   * 
   * @param  string  $url nyers url
   * @return string       keresőbarát url
   */
  public static function s2u( $url ){
    if( !is_string( $url )) return '';    
    $url = trim( $url );
    $url = mb_strtolower( $url, 'UTF-8' );
    $replacements = [
      'á' => 'a', 'é' => 'e', 'í' => 'i', 'ó' => 'o', 'ö' => 'o', 'ő' => 'o', 
      'ú' => 'u', 'ü' => 'u', 'ű' => 'u',
      'ä' => 'a', 'ë' => 'e', 'ï' => 'i', 'ö' => 'o', 'ü' => 'u',
      'à' => 'a', 'è' => 'e', 'ì' => 'i', 'ò' => 'o', 'ù' => 'u',
      'â' => 'a', 'ê' => 'e', 'î' => 'i', 'ô' => 'o', 'û' => 'u',
      ' ' => '-', '.' => '', ',' => '', ':' => '-', ';' => '',
      '!' => '', '?' => '', '/' => '', '\\' => '', '\'' => '',
      '"' => '', '(' => '', ')' => '', '[' => '', ']' => '',
      '{' => '', '}' => '', '@' => '', '#' => '', '$' => '',
      '%' => '', '^' => '', '&' => '', '*' => '', '+' => '',
      '=' => '', '|' => '', '<' => '', '>' => '', '´' => ''
    ];
    if( function_exists( 'transliterator_transliterate' ))
      $url = transliterator_transliterate( 'Any-Latin; Latin-ASCII', $url );
    $url = strtr( $url, $replacements );
    $url = preg_replace( '/-{2,}/', '-', $url );
    return $url;
  }

  /** Pénzösszeg formázás
   * 
   * @param  mixed  $amount    formázandó összeg (szám vagy numerikus string)
   * @param  string $currency  pénznem
   * @param  string $params    paraméterek 1: tizedesjel '-' nincs azaz egész szám, '.' vagy ',' alapértelmezett: '-'
   *                                       2: 'B' pénznem elötte, 'A' pénznem utánna alapértelmezett: 'A'
   *                                       3: '1' szóköz cserélve &nbsp; -re '0' nincs csere alapértelmezett: '1'
   * @return string            formázott összeg pénznemmel
   */
  public static function moneyFormat( $amount, $currency = 'Ft', $params = '-A1' ){
    if( empty( $amount ) or $amount == 0 ) return '';
    $amount = is_numeric( $amount )? $amount + 0 : 0;
    if( $amount == 0 ) return '';
    
    $decimalChar = $params[0] ?? '-';
    $currencyPosition = $params[1] ?? 'A';
    $useNbsp = isset( $params[2] )? ( int )$params[2] : 1;
    
    if( $decimalChar != '-' )
      $amount = number_format( $amount, 2, $decimalChar, ' ' );
    else
      $amount = number_format( round( $amount ), 0, ',', ' ' );
    $formattedAmount = ( $currencyPosition != 'A' )? $currency . ' ' . $amount : $amount . ' ' . $currency;
    if( $useNbsp )
      $formattedAmount = str_replace(' ', '&nbsp;', $formattedAmount);
    return $formattedAmount;
  }

  /** Fájl utolsó módosítás időpontja
   * @param  string $url elérési út linkje
   * @return string|false időpont 'Y-m-d H:i:s' formátumban vagy false hiba esetén
   */
  public static function lastModified( $url ){
    $curl = curl_init();
    curl_setopt_array( $curl, [
      CURLOPT_URL => $url,
      CURLOPT_HEADER => 1,
      CURLOPT_NOBODY => 1,
      CURLOPT_RETURNTRANSFER => 1,
      CURLOPT_TIMEOUT => 5,
      CURLOPT_SSL_VERIFYPEER => true,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_MAXREDIRS => 3
    ] );
    
    $curl_result = curl_exec( $curl );
    if( $curl_result !== false ){
      $headers = explode( "\n", $curl_result );
      foreach( $headers as $header )
        if( stripos( $header, 'Last-Modified:' ) !== false ){
          curl_close( $curl );
          $last_modified = explode( 'Last-Modified:', $header, 2 );
          return date( 'Y-m-d H:i:s', strtotime( trim( $last_modified[1] )));
        }
    }
    curl_close( $curl );
    return false;
  }
}