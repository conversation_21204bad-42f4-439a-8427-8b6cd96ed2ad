<header>
  <h6>Napok zárolása</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_blocking">
    <ul class="formbox">
      <li class="form col5">
        <input type="date" name="nap1" placeholder="" value="<?= date( 'Y-m-d' ) ?>">
        <label>Mettől</label>
      </li>
      <li class="form col5">
        <input type="date" name="nap2" placeholder="" value="<?= date( 'Y-m-d', strtotime( '+1 day' ) ) ?>">
        <label>Meddig</label>
      </li>
      <?php if( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 ){ ?>
      <li class="col0 tec">
        <span>Lakóegységek</span>
      </li>
      <?php   foreach( response::$vw->view->szabadlakoegysegek as $lakoegyseg ){ ?>
        <li class="form col5 cheaft" style="text-align:right">
          <input
            type="checkbox"
            id="le<?= $lakoegyseg->id ?>"
            name="lakoegysegek[]" 
            value="<?= $lakoegyseg->id ?>"
            <?= ( count( response::$vw->view->szabadlakoegysegek ) == 1 )? ' checked' : '' ?>
          >
          <label for="le<?= $lakoegyseg->id ?>"><?= $lakoegyseg->name ?></label>
        </li>
      <?php } }else{ ?>
        <li><input type="hidden" name="lakoegysegek[]" value="<?= response::$vw->view->szabadlakoegysegek[0]->id ?>"></li>
      <?php } ?>
    </ul>
  </form>
</section>
<footer>
  <button class="callback" name="btn_mentes" title="Zárolás adatainak mentése">Mentés</button>
  <button class="close">Kilép</button>
</footer>