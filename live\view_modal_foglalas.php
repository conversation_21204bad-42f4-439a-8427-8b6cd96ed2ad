<header>
  <h6>Foglalás - <span style="color:var(--danger-color)"><?= http::$route[3] ?? '' ?><?= ( response::$vw->view->foglalas->lemondva ?? 0 )? ' LEMONDVA' : '' ?></span></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_foglalas_modosit">
    <ul class="formbox">
      <li class="form col4">
        <input type="text" id="csatorna" placeholder="" value="<?= response::$vw->view->foglalas->megnevezes ?>" disabled>
        <label for="csatorna">Csatorna</label>
      </li>
      <li class="form col6">
        <input type="text" id="foglalva" placeholder="" value="<?= $_POST['foglalva'] ?>" disabled>
        <label for="foglalva">Foglalás időpontja</label>
      </li>
      <li class="form col0">
        <input type="text" name="kapcsolattarto" id="kapcsolattarto" placeholder="" value="<?= $_POST['kapcsolattarto'] ?? ''?>">
        <label for="kapcsolattarto">Kapcsolattartó</label>
      </li>
      <li class="form col0">
        <input type="text" name="email" placeholder="" value="<?= $_POST['email'] ?? ''?>">
        <label>Email</label>
      </li>
      <li class="form col0">
        <input type="text" name="telefon" placeholder="" value="<?= $_POST['telefon'] ?? ''?>">
        <label>Telefon</label>
      </li>
      <li class="form col0">
        <input type="text" name="megjegyzes" placeholder="" value="<?= $_POST['megjegyzes'] ?? ''?>">
        <label>Megjegyzés</label>
      </li>
    </ul>
    <details>
      <summary>Számlázási adatok</summary>
      <ul class="formbox" style="margin-bottom: clamp(.25rem, 4%, 1rem)">
        <li class="form col0">
          <input type="text" name="inv_name" placeholder="" value="<?= $_POST['inv_name'] ?? ''?>">
          <label>Számlázási név</label>
        </li>
        <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
              <select name="inv_countryCode" placeholder="">
                <?php foreach( response::$vw->view->countries as $country ){ ?>
                <option value="<?= $country->code2 ?>"<?= ( $_POST['inv_countryCode'] == $country->code2 )?' selected':'' ?>><?= $country->name ?></option>
                <?php } ?>
              </select>
              <label>Ország</label>
            </li>
        <li class="form col3">
          <input type="text" name="inv_zip" placeholder="" value="<?= $_POST['inv_zip'] ?? ''?>">
          <label>Irányítószám</label>
        </li>
        <li class="form col7">
          <input type="text" name="inv_city" placeholder="" value="<?= $_POST['inv_city'] ?? ''?>">
          <label>Település</label>
        </li>
        <li class="form col0">
          <input type="text" name="inv_address" placeholder="" value="<?= $_POST['inv_address'] ?? ''?>">
          <label>Utca, házszám</label>
        </li>
        <li class="form col5">
          <input type="text" name="inv_taxNumber" value="<?= $_POST['inv_taxNumber'] ?? '' ?>" placeholder="">
          <label>Adószám</label>
        </li>
        <li class="form col5">
          <input type="text" name="inv_communityVatNumber" value="<?= $_POST['inv_communityVatNumber'] ?? '' ?>" placeholder="">
          <label>Közösségi adószám</label>
        </li>
      </ul>
    </details>
    <ul class="formbox">
      <li class="form col2" style="min-width: 56px">
        <input type="text" name="vendegfo" placeholder="" value="<?= $_POST['vendegfo'] ?? ''?>">
        <label>Σ fő</label>
      </li>
      <li class="form col3">
        <input type="text" name="fizetendo" placeholder="" value="<?= $_POST['fizetendo'] ?? ''?>">
        <label>Fizetendő</label>
      </li>
      <li class="form col25">
        <input type="text" name="eloleg" placeholder="" value="<?= $_POST['eloleg'] ?? ''?>">
        <label>Előleg</label>
      </li>
      <li class="form col25">
        <input type="text" placeholder="" value="<?= $_POST['fizetve'] ?? ''?>" disabled>
        <label>Fizetve</label>
      </li>
      <li class="col0 icons">
        <button name="btn_modositas" title="Foglalás adatainak módosításának mentése">Módosít</button>
        <?php /*
        <a class="btn" style="--icon:var(--icon-mail-alt)" itle="Üzenetváltás(ok)" onclick="return confirm( 'MÉG NEM ÉL a FUNKCIÓ!' )"></a>
        <a class="btn" href="<?= http::$path?>/naptar/fizet/<?= http::$route[2] ?>" style="--icon:var(--icon-money)" title="Fizetés(ek)"></a>
        */ ?>
        <?php if( !isset( response::$vw->view->foglalas->lemondva )){ ?>
        <a class="btn lemond" style="--icon:var(--icon-cancel-circled)" title="Foglalás lemondása" onclick="return confirm( 'Biztos, hogy lemond?' )"></a>
        <?php } ?>
        <a class="btn torol" style="--icon:var(--icon-trash-empty)" title="Foglalás törlése (végleges, teljes eltávolítás)" onclick="return confirm( 'Biztos, hogy törlöd?' )"></a>
      </li>
    </ul>
  </form>
  <hr>
  <h4 style="display:flex;justify-content: space-between; margin-bottom: 0.25rem;">
    Foglalt lakóegységek
    <?php if( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 ){ ?>
    <a class="btn plussz-le" style="--icon:var(--icon-plus-circled)" title="További lakóegység hozzáadása a foglaláshoz"> Lakóegység</a>
    <?php } ?>
  </h4>
  <?php foreach( response::$vw->view->foglallakoegysegek as $foglallakoegyseg ){ ?>
  <form name="form_data_modosit-le">
    <input type="hidden" name="foglallakoegyseg_id" value="<?= $foglallakoegyseg->foglallakoegyseg_id ?>">
    <input type="hidden" name="lakoegyseg_id" value="<?= $foglallakoegyseg->lakoegyseg_id ?>">
    <input type="hidden" name="vendegfo" value="<?= $_POST['vendegfo'] ?? ''?>">
    <ul class="formbox">
      <li class="form col45" style="--toicon:var(--icon-angle-double-down)">
        <select name="ujlakoegyseg_id" placeholder="">
          <?php foreach( response::$vw->view->szabadlakoegysegek as $lakoegyseg ){ ?>
          <option value="<?= $lakoegyseg->id ?>"<?=( $foglallakoegyseg->lakoegyseg_id == $lakoegyseg->id )?' selected':'' ?>><?= $lakoegyseg->name ?></option>
          <?php } ?>
        </select>
        <label>Lakóegység</label>
      </li>
      <li class="form col4">
        <input type="date" name="erkezes" placeholder="" value="<?= $foglallakoegyseg->erkezes ?>"<?= ( response::$vw->view->foglalas->lemondva ?? 0 )? ' disabled' : '' ?>>
        <label>Érkezés</label>
      </li>
      <li class="form col15">
        <input type="text" name="ej" placeholder="" value="<?= count( json_decode( $foglallakoegyseg->napokfo ) ) ?>"<?= ( response::$vw->view->foglalas->lemondva ?? 0 )? ' disabled' : '' ?>>
        <label>Éj</label>
      </li>
      <li class="icons col0">
        <?php if( !isset( response::$vw->view->foglalas->lemondva )){ ?>
        <button name="btn_modositas-le" title="Lakóegység foglalás adatainak módosításának mentése">Módosít</button>
        <?php   if( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 ){ ?>
        <a class="btn torolle" style="--icon:var(--icon-trash-empty)" title="Lakóegység foglatságának törlése" onclick="return confirm( 'Biztos, hogy törlöd?' )"></a>
        <?php   } ?>
        <?php /*
        <a class="btn" style="--icon:var(--icon-login)" title="Vendégek érkeztetése a lakóegységbe" onclick="return confirm( 'MÉG NEM ÉL a FUNKCIÓ!' )"></a>
        */ ?>
        <?php } ?>
      </li>
    </ul>
  </form>
  <?php } ?>
  <hr>
  <h4 style="display:flex;justify-content: space-between; margin-bottom: 0.25rem;">Szolgáltatások <a class="btn plussz-ser" style="--icon:var(--icon-plus-circled)" title="További szolgáltatás hozzáadása a foglaláshoz">Szolgáltatás</a></h4>
  <?php if( response::$vw->view->foglalszolgaltatasok ?? 0 ){ ?>
  <?= table::datatable(
    'foglalszolgaltatasok',
    [
      'name' => ['th' => 'Megnevezés'],
      'ar' => ['th' => 'Összeg'],
      '*1' => ['th' => '', 'icons' => ['trash-empty:event:Törlés:id']]
    ]
  ) ?>
  <?php } ?>
  <h4>Pénzmozgások</h4>
  <?php if( response::$vw->view->fizetesek ?? 0 ){ ?>
  <?= table::datatable( 'fizetesek',
                        ['mikor' => ['th' => 'Mikor'],
                        'osszeg' => ['th' => 'Összeg'],
                         'egyeb' => ['th' => 'Egyéb'],
                            '*1' => ['th' => '', 'icons' => ['trash-empty:event:Törlés:id']]] ) ?>
  <?php }else{ ?>
  <ul><li class="col0 tac">Még nem történt fizetés!</li></ul>
  <?php } ?>
  <ul  class="formbox" style="margin-bottom: .25rem;">
    <?php if( !( response::$vw->view->foglalas->fizetendo ?? 0 ) or !response::$vw->view->foglalas->fizetendo ){ ?>
    <li class="col0 tac" style="background-color: var(--danger-color); border-radius: .5rem">Nincs megadva szállásdíj!</li>
    <?php }elseif( response::$vw->view->foglalas->fizetve == response::$vw->view->foglalas->fizetendo ){ ?>
    <li class="col0 tac" style="background-color: var(--success-color); border-radius: .5rem">Rendezve</li>
    <?php }elseif( response::$vw->view->foglalas->fizetve < response::$vw->view->foglalas->fizetendo ){ ?>
    <li class="col0 tac" style="background-color: var(--danger-color); border-radius: .5rem">Tartozás: <?= response::$vw->view->foglalas->fizetendo - response::$vw->view->foglalas->fizetve ?> HUF</li>
    <?php }else{ ?>
    <li class="col0 tac" style="background-color: var(--warning-color); border-radius: .5rem">Túlfizetés: <?= response::$vw->view->foglalas->fizetve - response::$vw->view->foglalas->fizetendo ?> HUF</li>
    <?php } ?>
  </ul>
  <form name="form_data_fizet">
    <ul class="formbox">
      <li class="form col4">
        <input type="date" name="mikor" placeholder="" value="<?= date('Y-m-d') ?>">
        <label>Mikor</label>
      </li>
      <li class="form col6">
        <input type="text" name="egyeb" placeholder="">
        <label>Egyéb</label>
      </li>
      <li class="form col33">
        <input type="text" placeholder="" name="osszeg_huf">
        <label>Összeg HUF</label>
      </li>
      <li class="form col33">
        <input type="text" placeholder="" name="osszeg_mas">
        <label>Összeg</label>
      </li>
      <li class="form col33">
        <input type="text" name="penznem" placeholder="">
        <label class="tac">Pénznem</label>
      </li>
      <li class="col0">
        <button type="submit" name="btn_fizet">Mentés</button>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
</footer>