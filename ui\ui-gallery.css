@charset "UTF-8";

[data-gallery]{
  & > div{
    &[popover]{
      position:fixed;
      justify-content: center;
      width: 100%;
      max-width: 100dvw;
      height: auto;
      background-color: #000;
      &:popover-open{ display: flex }
      & > img{
        width:100%;
        height: 100%;
        object-fit: contain;
      }
      & > div{
        width: 50%;
        height: 100%;
        z-index: calc(infinity);
        position: absolute;
        display: flex;
        align-items: center;
        &.prev{
          left: 0;
          justify-content: flex-start;
        }
        &.next{
          right: 0;
          justify-content: flex-end;
        }
        &:hover{ cursor: pointer }
        & > b{
          display: flex;
          width: 3rem;
          height: 3rem;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
          font-size: 2rem;
          color: #fff;
          background-color: #999;
          opacity: .5;
          &.close{
            position:absolute;
            z-index: 1;
            top: 1rem;
            right: 1rem;
          }
        }
      }
      &::backdrop{ backdrop-filter: blur( 3px ) }
    }
  }
}