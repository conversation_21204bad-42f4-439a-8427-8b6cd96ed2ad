<?php
if( $gallery = db::get( DATABASENAME.'.galleries', http::$route[3] ) ){
  $_POST['id'] = $gallery['id'];
  $_POST['name'] = $gallery['name'];
  $_POST['status'] = $gallery['signs'][0];
  $_POST['description'] = $gallery['description'];
  $_POST['sequence'] = json_decode( $gallery['image_sequence'] ?? '[]', true );
  $_POST['sequence'] = array_map( fn( $value ): int => intval( $value ), $_POST['sequence'] );
}