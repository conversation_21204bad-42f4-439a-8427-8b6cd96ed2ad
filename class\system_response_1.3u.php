<?php
/**
 * <PERSON><PERSON><PERSON><PERSON>, meg<PERSON><PERSON><PERSON> (view) előkészítése
 * 
 * Megjelenítéshez az információk $vw tömbben kerülnek összeállításra és a megjelenítés ebből dolgozik. Blokkonkén és ha szükséges
 * azon belül kulcsonként szelektálhatók az adatok.
 * Kiemelt Blokkok:
 * - **view** Megjelenítendő adatok
 * - **error** Megjelenítendő üzenet ami hiba
 * - **message** Megjelenítendő üzenet ami megjegyzés
 * - **meta** Oldal meta elemei
 * - **body** Oldal body azonosítója
 * - **menu** Oldal menü elemeinek megadása pl. ['log' => 1, ...]
 * 
 * @method `add();`         Tartalmat hozzáad a megjelenítéshez `$vw`
 * @method `alert();`       Megjelenítendő üzenetet öszzeállít
 * @method `minify_html();` HTML tömörítő
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2018, Tánczos Róbert
 * 
 * @version 1.3.0
 * @since 1.3.0 2022.06.26 Új verzió követés. Új néven. Fájlnévben jelölve. Optimalizálás.
 * @since 1.2.0 2021.09.11 Optimalizálás, `view()`, `minify_html()` hozzáadása
 * @since 1.1.1 2020.02.29 Optimalizálás, `change()` deprecated
 * @since 1.0.0 2018.06.22
 */

class response{
  
  public static $vw;
  private static $array = [];
  
/**
 * Tartalmat hozzáad a megjelenítéshez `$vw`
 *
 * @param string $block   Választott blokk név ami a tömb indexe lesz. Előre deffiniált blokkokat lásd fenn.
 * @param string $key     Blokkon belüli szint megadás ami nem kötelező.
 * @param mixed  $content Tartalom ami tárolásra kerül. Ha nincs megadva, akkor nincs $key alábontás és a 
 *                        tartalom a második paraméter lesz.
 */
  public static function add( $block, $key, $content = null ){
    if( !isset( self::$vw )) self::$vw = new stdClass();
    if( $content ?? 0 )
      self::$array[$block][$key] = $content;   
    else
      self::$array[$block] = $key;
    $json = json_encode( self::$array );
    if( $json === false ) user_error( "JSON encode error: " . json_last_error_msg());
    else self::$vw = json_decode( json_encode( self::$array ), false );
  }

  public static function alert( $block = 'error', $key = 0, $field = 0, $timing = 0 ){
    $timing = ( $timing > 0 ) ? ' data-timing="'.$timing.'"' : '';
    if( $key ){
      if( substr( $key, 0, 4 ) == 'form' ){
        if( $field ) $return = self::$vw->error->{$key}->{$field} ?? '';
        else $return = ( isset( self::$vw->{$block}->{$key} ) )? implode( '#', self::$vw->{$block}->{$key} ) : '';
      }else $return = self::$vw->{$block}->{$key} ?? '';
    }else{
      $return = self::$vw->{$block} ?? '';
      if( is_object( $return ) )
        if( $return->form ?? 0 ){
          $return = '';
          foreach( self::$vw->{$block}->form as $value )
            $return.= $value.'<br>';
        } 
    }
    return ( isset( $return ) and $return != '' )? '<span class="'.$block.'"'.$timing.'>'.$return.'</span>' : '';
  }
/**
 * html tömörítés
 *
 * @param  string $buffer teljes html string
 * @return string         teljes html tömörített string
 */
  public static function minify_html( $buffer ){
    $search = array( '/\>[^\S ]+/s','/[^\S ]+\</s','/(\s)+/s' );
    $replace = array( '>','<','\\1' );
    if( preg_match( "/\<html/i", $buffer ) == 1 && preg_match( "/\<\/html\>/i",$buffer ) == 1 )
      $buffer = preg_replace( $search, $replace, $buffer );
    return $buffer;
  }
}