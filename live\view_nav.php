        <nav<?=( $_SESSION['MININAV'] )? (( $_SESSION['MININAV'] > 1 )? ' class="zeronav"' : ' class="mininav"' ) : '' ?>>
          <?php if( !in_array( http::$route[0], ['tocreate', 'generates', 'subscription'] )){ ?>
            <label class="tabs">
              <input type="radio" name="tabs"<?= ( !( response::$vw->navtabs ?? 0 ) or response::$vw->navtabs == 1 )? ' checked' : '' ?>>
              <i style="--icon:var(--icon-keyboard)"></i>
              <span>Vezérlő</span>
            </label>
            <ul class="nav-nav">
              <li<?= ( http::$route[0] == 'dashboard' )? ' class="nav__aktiv"' : '' ?>>
                <a href="<?= http::$path ?>/dashboard" title="Műszerfal"><i style="--icon:var(--icon-gauge)"></i><span>Műszerfal</span></a>
              </li>
              <li<?= ( http::$route[0] == 'naptar' )? ' class="nav__aktiv"' : '' ?>>
                <a href="<?= http::$path ?>/naptar" title="Foglalási naptár / Szobatükör"><i style="--icon:var(--icon-calendar)"></i><span>Foglalási naptár</span></a>
              </li>
              <li<?= ( http::$route[0] == 'foglalas' )? ' class="nav__aktiv"' : '' ?>>
                <a href="<?= http::$path ?>/foglalas" title="Foglalások"><i style="--icon:var(--icon-key)"></i><span>Foglalások</span></a>
              </li>
              <?php /*
              <li<?=(http::$route[0] == 'penz') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/penz" title="Pénzmozgások"><i style="--icon:var(--icon-money)"></i><span>Pénzmozgások</span></a>
              </li>
              */ ?>
              <?php if( setup::is_right( 0 )){ ?>
              <li<?=( http::$route[0] == 'invoices' )? ' class="nav__aktiv"' : '' ?>>
                <a href="<?= http::$path ?>/invoices" title="Számlák"><i style="--icon:var(--icon-stackoverflow)"></i><span>Számlák</span></a>
              </li>
              <?php } ?>
              <?php if( setup::is_right( 1 )){ ?>
              <li<?=( http::$route[0] == 'message' )? ( ( http::$route[1] ?? 0 )?' class="nav__aktiv nav-second__aktiv"':' class="nav__aktiv"' ):''?>>
                <a title="Üzenetek"><i style="--icon:var(--icon-mail-alt)"></i><span>Üzenetek</span><i class="arrow"></i></a>
                <ul class="nav-second">
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'template' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/message/templates" title="Sablonok">
                      <span>Sab</span><span>Sablonok</span>
                    </a>
                  </li>
                  <?php if( setup::is_right( 2 )){ ?>
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'subscribers' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/message/subscribers" title="Feliratkozók">
                      <span>Fir</span><span>Feliratkozók</span>
                    </a>
                  </li>
                  <?php } ?>
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'messages' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/message/messages" title="Üzenetek">
                      <span>Üzi</span><span>Üzenetek</span>
                    </a>
                  </li>
                </ul>
              </li>
              <?php } ?>
              <?php if( setup::is_right( 12 ) or setup::is_right( 4 )){ ?>
              <li<?=( http::$route[0] == 'reviews' )? ( ( http::$route[1] ?? 0 )?' class="nav__aktiv nav-second__aktiv"':' class="nav__aktiv"' ):''?>>
                <a title="Értékelések"><i style="--icon:var(--icon-star)"></i><span>Értékelések</span><i class="arrow"></i></a>
                <ul class="nav-second">
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'ratings' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/reviews/ratings" title="Vélemények">
                      <span>Vél</span><span>Vélemények</span>
                    </a>
                  </li>
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'imports' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/reviews/imports" title="Importálás">
                      <span>Imp</span><span>Importálás</span>
                    </a>
                  </li>
                </ul>
              </li>
              <?php } ?>
            </ul>
            <label class="tabs">
              <input type="radio" name="tabs"<?= ( response::$vw->navtabs == 2 )? ' checked' : '' ?>>
              <i style="--icon:var(--icon-sliders)"></i>
              <span>Gépház</span>
            </label>
            <ul class="nav-nav">
              <li<?=(http::$route[0] == 'accommodationunittypes') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/accommodationunittypes" title="Lakóegységek"><i style="--icon:var(--icon-bed)"></i><span>Lakóegységek</span></a>
              </li>
              <?php if( setup::is_right( 12 ) or setup::is_right( 14 )){ ?>
              <li<?=( http::$route[0] == 'pricing' )? ( ( http::$route[1] ?? 0 )?' class="nav__aktiv nav-second__aktiv"':' class="nav__aktiv"' ):''?>>
                <a title="Árképzés"><i style="--icon:var(--icon-tags)"></i><span>Árképzés</span><i class="arrow"></i></a>
                <ul class="nav-second">
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'prices' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/pricing/prices" title="Árak">
                      <span>Ár</span><span>Árak</span>
                    </a>
                  </li>
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'periods' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/pricing/periods" title="Időszakok">
                      <span>Idő</span><span>Időszakok</span>
                    </a>
                  </li>
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'children_rules' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/pricing/children_rules" title="Gyerek szabályok">
                      <span>Gys</span><span>Gyermek szabályok</span>
                    </a>
                  </li>
                </ul>
              </li>
              <?php } ?>
              <li<?=(http::$route[0] == 'services') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/services" title="Szolgáltatások"><i style="--icon:var(--icon-food)"></i><span>Szolgáltatások</span></a>
              </li>
              <?php if( setup::is_right( 5 )){ ?>
              <li<?=(http::$route[0] == 'automation') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/automation" title="Automatizálás"><i style="--icon:var(--icon-android)"></i><span>Automatizálás</span></a>
              </li>
              <?php } ?>
              <?php /*
              <li<?=(http::$route[0] == 'foglalasicsatorna') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/foglalasicsatorna" title="Csatornák"><i style="--icon:var(--icon-exchange)"></i><span>Csatornák</span></a>
              </li>
              <li<?=(http::$route[0] == 'channelmanager') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/channelmanager" title="Channel manager"><i style="--icon:var(--icon-exchange)"></i><span>Channel manager</span></a>
              </li>
              <li<?=(http::$route[0] == 'seo') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/seo" title="SEO"><i style="--icon:var(--icon-search)"></i><span>SEO</span></a>
              </li>
              <li<?=(http::$route[0] == 'pagemanager') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/pagemanager" title="Saját webolda oldalainak kezelése"><i style="--icon:var(--icon-stackoverflow)"></i><span>Saját oldalkezelő</span></a>
              </li>
              <li<?=(http::$route[0] == 'blogmanager') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/blogmanager" title="Blogkezelő"><i style="--icon:var(--icon-newspaper)"></i><span>Blogkezelő</span></a>
              </li>
              <li<?=(http::$route[0] == 'bannermanager') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/bannermanager" title="Bannerkezelő"><i style="--icon:var(--icon-clone)"></i><span>Bannerkezelő</span></a>
              </li>
              */ ?>
              <?php if( setup::is_right( 12 )){ ?>
              <li<?=( http::$route[0] == 'website' )? ( ( http::$route[1] ?? 0 )?' class="nav__aktiv nav-second__aktiv"':' class="nav__aktiv"' ):''?>>
                <a title="Saját honlap"><i style="--icon:var(--icon-globe)"></i><span>Saját honlap</span><i class="arrow"></i></a>
                <ul class="nav-second">
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'galleries' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/website/galleries" title="Galériák">
                      <span>Gal</span><span>Galériák</span>
                    </a>
                  </li>
                  <?php if( setup::is_right( 13 )){ ?>
                  <li>
                    <a<?=( ( http::$route[1] ?? 0 ) and http::$route[1] == 'documents' )? ' class="nav__aktiv"':'' ?> href="<?= http::$path?>/website/documents" title="PDF dokumentumok">
                      <span>Doc</span><span>Dokumentumok</span>
                    </a>
                  </li>
                  <?php } ?>
                </ul>
              </li>
              <?php } ?>
              <?php if( setup::is_right( 8 )){ ?>
              <li<?=(http::$route[0] == 'coupons') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/coupons" title="Kuponok"><i style="--icon:var(--icon-percent)"></i><span>Kuponok</span></a>
              </li>
              <?php } ?>
              <?php if( setup::is_right( 9 )){ ?>
              <li<?=(http::$route[0] == 'popups') ? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path?>/popups" title="Popup ajánló"><i style="--icon:var(--icon-clone)"></i><span>Popup-ok</span></a>
              </li>
              <?php } ?>
              <li<?=(http::$route[0] == 'settings') ? ((isset(http::$route[1]))?' class="nav__aktiv nav-second__aktiv"':' class="nav__aktiv"'):''?>>
                <a title="Beállítások"><i style="--icon:var(--icon-cog)"></i><span>Beállítások</span><i class="arrow"></i></a>
                <ul class="nav-second">
                  <li>
                    <?php /*
                    <a<?=(isset(http::$route[1]) and http::$route[1] == 'color') ? ' class="nav__aktiv"':''?> href="<?= http::$path?>/settings/color" title="Színek">
                      <span>Szi</span><span>Színek</span>
                    </a>
                    */ ?>

                    <a<?=(isset(http::$route[1]) and http::$route[1] == 'parameter') ? ' class="nav__aktiv"':''?> href="<?= http::$path?>/settings/parameter" title="Paraméterek">
                      <span>Par</span><span>Paraméterek</span>
                    </a>
                    <a<?=(isset(http::$route[1]) and http::$route[1] == 'profile') ? ' class="nav__aktiv"':''?> href="<?= http::$path?>/settings/profile" title="Profilom">
                      <span>Pro</span><span>Profilom</span>
                    </a>
                    <?php /*
                    <a<?=( http::$route[1] ?? 0 and http::$route[1] == 'subscription' )? ' class="nav__aktiv"' : '' ?> href="<?= http::$path ?>/settings/subscription" title="Előfizetési csomagok">
                      <span>Efi</span><span>Előfizetés</span>
                    </a>
                    */ ?>
                  </li>
                </ul>
              </li>
            </ul>
          <?php } ?>
          <ul class="nav-nav">
            <?php /*
            <li<?=(http::$route[0] == 'faq' )? ' class="nav__aktiv"':''?>>
              <a href="<?= http::$path ?>/faq" title="Gyakran ismételt kérdések"><i style="--icon:var(--icon-help)"></i><span>GYIK</span></a>
            </li>
            */ ?>
            <?php if( $_SESSION['USER'] ){ ?>
              <li<?=(http::$route[0] == 'customerservice' )? ' class="nav__aktiv"':''?>>
                <a href="<?= http::$path ?>/customerservice" title="Ügyfélszolgálat"><i style="--icon:var(--icon-bot)"></i><span>Ügyfélszolgálat</span></a>
              </li>
            <?php } ?>
          </ul>
        </nav>