        <style>
          :-webkit-autofill,
          :-webkit-autofill:hover, 
          :-webkit-autofill:focus {
            -webkit-box-shadow: 0 0 0px 1000px #fff inset;
          }
          article{
            & .carts{
              &>.cart{
                width: clamp( 300px, calc(50% - .5vw), calc(50% - .5vw) );
                & h1{ padding-bottom: 1em }
              }
            }
          }
        </style>
        <div class="carts" style="margin-top: 20vh">
          <div class="cart">
            <?= response::alert('message',0,0,5) ?>
            <?= response::alert() ?>
            <?php if( !( $_POST['reglepes'] ?? 0 ) or http::cleanPost('reglepes') == 1){ ?>
            <h1>Regisztráció 1. lépés</h1>
            <form name="form_registration" method="post" action="<?= http::$path ?>/registration">
              <?= http::csrfToken() ?>
              <input type="hidden" name="reglepes" value="1">
              <ul class="formbox">
                <li class="form col0" data-tooltip="line">
                  <input
                    class="validation"
                    type="email"
                    name="email" id="email"
                    value="<?= $_POST['email'] ?? '' ?>"
                    placeholder=""
                    required autofocus  autocomplete="off"
                  >
                  <label for="email">Email</label>
                  <div class="input-message tooltip">
                    <div hidden class="error"></div>
                    <div class="info">Érvényes emailcímet adjon meg.</div>
                  </div>
                </li>
                <li><button name="btn_registration">Tovább</button></li>
              </ul>
              <center>
                <?php /*
                <a href="#" target="_blank" title="" data-gdprplus="blk|9|lnk|1,doc,Teljes adatkezelési">Adatkezelési tájékoztató</a><br>
                */ ?>
                <a href="<?= http::$path?>/login" title="">Vissza a bejelentkezéshez</a><br>
              </center>
            </form>
            <p style="margin-top: 3rem">Hogyan regisztrálhatsz? Első lépések, beállítások.</p>
            <video controls controlsList="nodownload" loop disablepictureinpicture disableremoteplayback poster="/items/tudastar-videok.jpg">
              <source src="/items/regisztracio-beallitas.mp4" type="video/mp4">
            </video>
            </div>
            <?php }elseif( http::cleanPost( 'reglepes' ) == 2 ){ ?>
            <h1>Regisztráció 2. lépés</h1>
            <form name="form_registration" method="post" action="<?= http::$path ?>/registration">
              <?= http::csrfToken() ?>
              <input type="hidden" name="reglepes" value="2">
              <ul class="formbox">
                <li class="form col0">
                  <input type="email" name="email" id="email" value="<?= $_POST['email'] ?? '' ?>" placeholder="" readonly>
                  <label for="email">Email</label>
                </li>
                <li class="form col0" data-tooltip="line">
                  <input class="validation" type="password" name="psw" id="psw" placeholder="" minlength="8" required>
                  <label for="psw">Jelszó</label>
                  <div class="input-message tooltip">
                    <div hidden class="error"></div>
                    <div class="info">Legalább 8 karakter, szám, kis- és nagybetű</div>
                  </div>
                </li>
                <li class="form col0">
                  <input type="password" name="psw_again" id="psw_again" placeholder="" required>
                  <label for="psw_again">Jelszó újra</label>
                </li>
                <?php /*
                <li class="form col0 chebef">
                  <input type="checkbox" id="aszf" name="aszf" value="1">
                  <label for="aszf">Szerződési feltételeket elfogadom!</label>
                </li>
                */ ?>
                <li class="form col0"><button name="btn_registration">Tovább</button></li>
              </ul>
              <?php /*
              <center>
                <a href="#" target="_blank" title="" data-gdprplus="doc|13">ÁSZF</a>
                <a href="#" target="_blank" title="" data-gdprplus="blk|50">Adatkezelési tájékoztató</a>
              </center>
              */ ?>
            </form>
            <?php }elseif( http::cleanPost( 'reglepes' ) > 2 ){ ?>
            <h1>Sikeres regisztráció!</h1>
            <?php $_SESSION = null; ?>
            <?php   if( http::cleanPost( 'reglepes' ) == 3 ){ ?>
            <p>Most már bejelentkezhetsz!</p>
            <center>
              <a href="<?= http::$path?>/login" title="">Vissza a bejelentkezéshez</a>
            </center>
            <?php }else{ ?>
            <p>Hagyd jóvá az emailben kapott visszaigazoló linket!</p>
            <?php } } ?>
          </div>
        </div>
        <script type="module">
          import{ validation } from '/shared/js/spritzer/index.js'
          window.addEventListener( 'DOMContentLoaded', validation())
        </script>