<?php // On-site Message ?>
<link href="/shared/js/spritzer/ui/ui-library.css" rel="stylesheet">
<link href="/shared/js/spritzer/ui/ui-upload.css" rel="stylesheet">
<header>
  <h6>Popup ajánló</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_popup">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <input type="hidden" name="image_id" value="<?= $_POST['image_id'] ?>">
    <ul class="formbox">
      <li class="form col3" style="--toicon:var(--icon-angle-double-down)">
        <select name="status" placeholder="">
          <option value="1"<?=( $_POST['status'] == '1' )? ' selected' : '' ?>>Nem látható</option>  
          <option value="2"<?=( $_POST['status'] == '2' )? ' selected' : '' ?>>Belépéskor aktív</option>
          <?php /*
          <option value="3"<?=( $_POST['status'] == '3' )? ' selected' : '' ?>>Kilépéskor aktív</option>
          */ ?>
        </select>
        <label>Állapot</label>
      </li>
      <li class="form col7">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?? '' ?>">
        <label>Megnevezés</label>
      </li>
      <li class="form col5">
        <input type="text" name="validity_start" placeholder="" value="<?= $_POST['validity_start'] ?? date( 'Y-m-d' ) ?>">
        <label>Megjelenítés kezdete</label>
      </li>
      <li class="form col5">
        <input type="text" name="validity_end" placeholder="" value="<?= $_POST['validity_end'] ?? $_POST['validity_start'] ?? date( 'Y-m-d' ) ?>">
        <label>Megjelenítés vége</label>
      </li>
      <li class="form col5">
        <input type="text" name="link" placeholder="" value="<?= $_POST['link'] ?? '' ?>">
        <label>Link (/booking)</label>
      </li>
      <li class="form col5">
        <input type="text" name="link_name" placeholder="" value="<?= $_POST['link_name'] ?? '' ?>">
        <label>Link szövege</label>
      </li>
      <?php /*
      <li class="form col0">
        <textarea name="content"><?= $_POST['content'] ?? '' ?></textarea>
        <label>Tartalom</label>
      </li>
      */ ?>
    </ul>
    <b>Kép</b>
    <ul data-upload='{"maxdb": 1, "tableName": "<?= DATABASENAME ?>.popups", "tableId": <?= $_POST['id'] ?>, "imageId": <?= $_POST['image_id'] ?>}'>
      <li>
        <span class="box__icon" style="--icon:var(--icon-upload)"></span>
        <input class="box__input" id="foto" type="file" name="foto[]" data-multiple-caption="{count} kiválasztott fájl">
        <label for="foto">
          <strong>Válassz egy fájlt</strong>
          <span class="box__dragndrop"> vagy húzd ide</span>
        </label>
      </li>
      <li class="box__uploading">Feltöltés&hellip;</li>
      <li class="box__success">Kész! <a href="#" class="box__restart" role="button">További fájlok?</a></li>
      <li class="box__error">Hiba! <span></span>. <a href="#" class="box__restart" role="button">Próbáld újra!</a></li>
      <li class="form col0" data-library="">
        <div class="only-one-image">
          <?php if( ( $_POST['image_id'] ?? 0 ) and file_exists( 'upload/'.$_SESSION['COMPANY'].'/pic'.sprintf( "%04d", $_POST['image_id'] ).'.webp' ) ){ ?>
          <img src="/upload/<?= $_SESSION['COMPANY'] ?>/pic<?= sprintf( "%04d", $_POST['image_id'] ).'.webp?'.md5( date( "YmdHis" ) ) ?>">
          <?php } ?>
        </div>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Popup mentése">Módosít</button>
</footer>