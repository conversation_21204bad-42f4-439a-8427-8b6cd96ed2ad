@charset "UTF-8";
/* Default - minden oldara kiterjedő */

/* <PERSON><PERSON><PERSON><PERSON><PERSON>áv szélessége és magassága */
::-webkit-scrollbar{
  width: .5rem;
  height: .5rem;
}
::-webkit-scrollbar-track{
  background-color: var(--btn-bg-color);
  border-radius: .5rem;
}
::-webkit-scrollbar-thumb{
  background: var(--danger-color);
  border-radius: .5rem;
}
/* Nyilag a gördítő végén */
::-webkit-scrollbar-button{ display: none }

:autofill,
:autofill:hover, 
:autofill:focus{ box-shadow: 0 0 0px 1000px #fff inset }

li{ list-style: none }

a, object,
input:focus,
textarea:focus,
button:focus{ outline: none !important }

a{
  text-decoration: none;
  cursor: pointer;
  color: var(--link-color);
  &:hover{ text-decoration: underline }
}

.error{ color: var( --danger-color ) }

.flex{
  display: flex;
  flex-flow: row wrap;
  align-content: start;
  gap: clamp(.875rem, 1.4vw, 1.125rem );
}

body{
  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  overflow-x: hidden;
  background-color: var( --bg-color );
  color: var( --color );
}
hr{
  color: var(--link-color);
  margin: 1rem 0;
}
.tac{ text-align: center }
.tar{ text-align: right }

.bgonto[data-to]:after,
.bgonto[data-on]:before{
  border-radius: 0 .5rem .5rem 0;
  background-color: var(--form-select-bg-color);
}

.soon{ background-color: var(--warning-color) }
a.soon{
  display: inline-block;
  width: 20px;
  height: 12px;
}
.not-show{ display: none !important }

dialog > div > section{ background-color: var(--cart-article-bg-color) }