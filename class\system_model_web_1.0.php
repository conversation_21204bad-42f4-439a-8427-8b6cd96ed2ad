<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *
 * @method `get_theme();`           <PERSON><PERSON><PERSON> be<PERSON>
 * @method `list_theme();`          Témák adatainak beolvasása
 * @method `get_creative();`        Kreatív adatok beolvasása
 * @method `list_creative();`       Kreatívok adatainak beolvasása
 * @method `list_creativePG();`     Kreatívok adatainak beolvasása oldalhoz
 * @method `save_creativeStatus();` Adott kreatív állapotának változtatása aktív 1/passzív 0
 * @method `del_creative();`        Kreatív törlése
 * @method `get_page();`            Oldal beolvasása
 * @method `list_page();`           Oldalak adatainak beolvasása
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2017, Tánczos Róbert
 * 
 * @version 1.0.0
 * @since 1.0.0 2024.03.02
 */

class model_web extends mysql{
  // === theme
  public static function get_theme( $theme_id ){
    $sql = 'SELECT * FROM shared.themes WHERE id=? LIMIT 1';
    $res = self::query( $sql, [ $theme_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }

  public static function list_theme( $project_id = NULL, $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.themes';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' project_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY id';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }
  // === creative
  public static function get_creative( $creative_id ){
    $sql = 'SELECT * FROM shared.creatives WHERE id=? LIMIT 1';
    $res = self::query( $sql, [ $creative_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }

  public static function list_creative( $project_id = NULL, $partner_id = NULL,  $active = 0, $ot = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $partner_id = ( isset( $partner_id ) )? ( $partner_id )? $partner_id : $_SESSION['COMPANY'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.creatives';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' project_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $partner_id ){
      $sql.= $wa.' partner_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $partner_id;
    }
    if( $active ){
      $sql.= $wa.' MID(signs, 1, 1)="1"';
      $wa = ' AND';
    }
    if( $ot ){
      $sql.= $wa.' MID(signs, 3, 1)=? AND MID(signs, 4, 1)=?';
      $type.= 'ss';
      $data[] = $ot[0];
      $data[] = $ot[1];
    }
    $sql.= ' ORDER BY project_id, partner_id, MID(signs,3,2)';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }

  public static function list_creativePG( $project_id, $partner_id, $page_group ){
    $project_id = ( $project_id )? $project_id : $_SESSION['PROID'];
    $partner_id = ( $partner_id )? $partner_id : $_SESSION['COMPANY'];
    $sql = 'SELECT * FROM shared.creatives
            WHERE MID(signs , 1, 1)="1" AND project_id=? AND company_id=? AND page_group=?
            ORDER BY sequence, id';
    $res = self::query( $sql, [$project_id, $partner_id, $page_group], 'iii' )->get_result()->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? $res : [];
    return ( count( $list ) )? $list : 0;
  }

  public static function save_creative( $creative, $creative_id = 0 ){
    $res = self::save( 'shared.creatives', $creative, ( $creative_id )? ['id', $creative_id, 'i'] : 0 );
    return $res;
  }

  public static function save_creativeStatus( $creative_id ){
    $sql = 'UPDATE shared.creatives SET signs=CONCAT(IF(MID(signs,1,1)="1","0","1"),SUBSTRING(signs,2)) WHERE id=? LIMIT 1';
    $res = self::query( $sql, [ $creative_id ], 'i' );
    return $res;
  }

  public static function del_creative( $creative_id ){
    $sql = 'DELETE FROM shared.creatives WHERE id=? LIMIT 1';
    $res = self::query( $sql, [ $creative_id ], 'i' )->get_result();
    return $res;
  }
  // === page
  public static function get_page( $page, $project_id = 0, $partner_id = 0 ){
    if( is_numeric( $page ) ){
      $sql = 'SELECT * FROM shared.pages WHERE page_id=? LIMIT 1';
      $res = self::query( $sql, [ $page ], 'i' )->get_result()->fetch_assoc();
    }else{
      $project_id = ( $project_id )? $project_id : $_SESSION['PROID'];
      $partner_id = ( $partner_id )? $partner_id : $_SESSION['COMPANY'];
      $sql = 'SELECT * FROM shared.pages WHERE project_id=? AND partner_id=? AND menu_link=? LIMIT 1';
      $res = self::query( $sql, [ $project_id, $partner_id, $page ], 'iis' )->get_result()->fetch_assoc();
    }
    return $res;
  }

  public static function list_page( $project_id = NULL, $partner_id = NULL,  $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $partner_id = ( isset( $partner_id ) )? ( $partner_id )? $partner_id : $_SESSION['COMPANY'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.pages';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' project_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $partner_id ){
      $sql.= $wa.' partner_id=?';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $partner_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY project_id, partner_id, MID(signs,2,1)';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }

  public static function save_page( $page, $page_id = 0 ){
    $res = self::save( 'shared.pages', $page, ( $page_id )? ['page_id', $page_id, 'i'] : 0 );
    return $res;
  }

  public static function save_pageStatus( $page_id ){
    $sql = 'UPDATE shared.pages SET signs=CONCAT(IF(MID(signs,1,1)="1","0","1"),SUBSTRING(signs,2)) WHERE page_id=? LIMIT 1';
    $res = self::query( $sql, [ $page_id ], 'i' );
    return $res;
  }

  public static function del_page( $page_id ){
    $sql = 'DELETE FROM shared.pages WHERE MID(signs, 2, 1)<>"F" AND page_id=? LIMIT 1';
    $res = self::query( $sql, [ $page_id ], 'i' )->get_result();
    return $res;
  }
  // === element
  public static function get_element( $element_id ){
    $sql = 'SELECT * FROM shared.elements WHERE id=? LIMIT 1';
    $res = self::query( $sql, [ $element_id ], 'i' )->get_result()->fetch_assoc();
    return $res;
  }

  public static function list_element( $project_id = NULL, $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.elements';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' (projects->"$[0]"=0 OR JSON_EXTRACT(projects, "$[*]")=?)';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY identifier, MID(signs, 3, 2), id';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }
  // === setting
  public static function get_setting( $setting_id ){
    $sql = 'SELECT * FROM shared.setting WHERE setting_id=? LIMIT 1';
    return self::query( $sql, [ $setting_id ], 'i' )->get_result()->fetch_assoc();
  }

  public static function get_settingIdentifier( $identifier ){
    $sql = 'SELECT * FROM shared.setting WHERE identifier=? LIMIT 1';
    return self::query( $sql, [ $identifier ] )->get_result()->fetch_assoc();
  }

  public static function list_setting( $project_id = NULL, $active = 0 ){
    $project_id = ( isset( $project_id ) )? ( $project_id )? $project_id : $_SESSION['PROID'] : 0; // NULL: 0, 0: SESSION, n: n
    $sql = 'SELECT * FROM shared.setting';
    $wa = ' WHERE'; $type = ''; $data = [];
    if( $project_id ){
      $sql.= $wa.' (projects->"$[0]"=0 OR JSON_EXTRACT(projects, "$[*]")=?)';
      $wa = ' AND';
      $type.= 'i';
      $data[] = $project_id;
    }
    if( $active )
      $sql.= $wa.' MID(signs, 1, 1)="1"';
    $sql.= ' ORDER BY identifier, setting_id';
    if( $type == '' )
      $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    else
      $res = self::query( $sql, $data, $type )->get_result()->fetch_all( MYSQLI_ASSOC );
    if( $res ) $list = $res;
    return ( count( $list ) )? $list : 0;
  }
  // === size
  private static function sizeFormatList( $list ){
    foreach( $list as $key => $size )
      $list[$key] = self::sizeFormat( $size );
    return $list;
  }

  private static function sizeFormat( $size ){
    if( $size['name'][0] == '#' ){
      $rectangle = substr( $size['name'], 1, strpos( $size['name'], ' ' ) );
      $size['name'] = substr( $size['name'], strpos( $size['name'], ' ' ) );
      $size['size_format'] = $size['w'].'-'.$size['h'].'/'.$rectangle;
    }elseif( $size['w'] )
      $size['size_format'] = $size['w'].'x'.$size['h'];
    else
      $size['size_format'] = '';
    return $size;
  }
  
  public static function list_size(){
    $sql = 'SELECT * FROM shared.size ORDER BY name';
    $res = self::query( $sql )->fetch_all( MYSQLI_ASSOC );
    $list = ( $res )? self::sizeFormatList( $res ) : [];
    return ( count( $list ) )? $list : 0;
  }
}