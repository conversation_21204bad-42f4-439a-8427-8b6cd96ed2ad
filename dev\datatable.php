<?php
function table_get( $params ){
  switch( $params['datatable'] ){
    case 'foglalasoklist':
      $foglalas = db::get_foglalas( $params['id'] );
      if( $foglalas['lemondva'] ?? 0 ){
        $foglalas['allapotstr'] = 'Lemondva';
        $foglalas['allapot'] = 1;
      }elseif( !$foglalas['fizetendo'] ){
        $foglalas['allapotstr'] = 'Nincs díj';
        $foglalas['allapot'] = 2;
      }elseif( !$foglalas['fizetve'] ){
        $foglalas['allapotstr'] = 'Nem fizetett';
        $foglalas['allapot'] = 3;
      }elseif( $foglalas['fizetendo'] > $foglalas['fizetve'] ){
        $foglalas['allapotstr'] = 'Tartozás';
        $foglalas['allapot'] = 4;
      }elseif( $foglalas['fizetendo'] < $foglalas['fizetve'] ){
        $foglalas['allapotstr'] = 'Túlfizetés';
        $foglalas['allapot'] = 5;
      }else{
        $foglalas['allapotstr'] = 'Rendezett';
        $foglalas['allapot'] = 0;
      }
      $foglalas['fizetendo'] = number_format( $foglalas['fizetendo'], 0, '.', ' ' );
      $foglalas['fizetve'] = number_format( $foglalas['fizetve'], 0, '.', ' ' );
      $foglalas['csatorna'] = $foglalas['foglalasicsatorna'].'<br>'.$foglalas['megnevezes'];      
      return $foglalas;
    break;
  }
}
function table_foglalasoklist( $params ){
  $where = ' AND f.foglalva>"2023-12-31"';
  if( $_SESSION['RE'] ?? 0 ){
    $re = base64_decode( $_SESSION['RE'] );
    $re = explode( '|', $re );
    $where.= ' AND f.foglalas_id='.$re[1];
    unset( $_SESSION['RE'] );
  }
  if( $foglalasok = db::list_foglalasok(
    $_SESSION['foglalasoklist']['piece'],
    $where,
    $params['order'],
    $params['limit'],
    $params['activePage']
  )){
    foreach( $foglalasok as $key => $foglalas ){
      if( $foglalas['lemondva'] ?? 0 ){
        $foglalasok[$key]['allapotstr'] = 'Lemondva';
        $foglalasok[$key]['allapot'] = 1;
      }elseif( !$foglalas['fizetendo'] ){
        $foglalasok[$key]['allapotstr'] = 'Nincs díj';
        $foglalasok[$key]['allapot'] = 2;
      }elseif( !$foglalas['fizetve'] ){
        $foglalasok[$key]['allapotstr'] = 'Nem fizetett';
        $foglalasok[$key]['allapot'] = 3;
      }elseif( $foglalas['fizetendo'] > $foglalas['fizetve'] ){
        $foglalasok[$key]['allapotstr'] = 'Tartozás';
        $foglalasok[$key]['allapot'] = 4;
      }elseif( $foglalas['fizetendo'] < $foglalas['fizetve'] ){
        $foglalasok[$key]['allapotstr'] = 'Túlfizetés';
        $foglalasok[$key]['allapot'] = 5;
      }else{
        $foglalasok[$key]['allapotstr'] = 'Rendezett';
        $foglalasok[$key]['allapot'] = 0;
      }
      $foglalasok[$key]['fizetendo'] = number_format( $foglalas['fizetendo'] ?? 0, 0, '.', ' ' );
      $foglalasok[$key]['fizetve'] = number_format( $foglalas['fizetve'] ?? 0, 0, '.', ' ' );
      $foglalasok[$key]['csatorna'] = /*$foglalas['foglalasicsatorna'].'<br>'.*/$foglalas['megnevezes'];
      $foglalasok[$key]['is_invoiced'] = setup::is_right( 0 );

      $szla = null;
      if( $invoices = db::list_invoices( 'booking_id='.$foglalas['foglalas_id'] )){
        foreach( $invoices as $invoice )
          switch( $invoice['signs'][1] ){
            case 2: $szla.= '<b title="Előlegszámla: '.$invoice['invoice_number'].'">E</b> '; break;
            case 3: $szla.= '<b title="Számla: '.$invoice['invoice_number'].'">S</b> '; break;
            case 4: $szla.= '<b title="Érvénytelenítő számla: '.$invoice['invoice_number'].'">É</b> '; break;
          }
        if( $szla ?? 0 ) $foglalasok[$key]['allapotstr'] .= '<br>' . $szla;
      }
    }
  }
  return $foglalasok;
}

function table_accommodation_unit_types(){
  return db::list_accommodationUnitTypes();
}

function table_accommodation_units( $params ){
  return db::list_accommodationUnits( $params['where'] );
}

function table_services(){ 
  if( $services = db::list( DATABASENAME.'.services', 'company_id='.$_SESSION['COMPANY'] ) )
    foreach( $services as $key => $row )
      $services[$key]['allapotstr'] = ( $row['signs'][0] )? ( ( $row['signs'][0] == 1 )? 'Rejtett' : ( ( $row['signs'][0] == 'T' )? 'Tájékoztat' : 'Választ' ) ) : 'Törölt';
  return $services;
}

function table_reviews(){
  if( $reviews = db::list_reviews()){
    foreach( $reviews as $key => $row ){
      $sum = $count = 0;
      if( $row['location'] ?? 0 ){
        $sum += $row['location'];
        $count++;
      }
      if( $row['communication'] ?? 0 ){
        $sum += $row['communication'];
        $count++;
      }
      if( $row['cleanliness'] ?? 0){
        $sum += $row['cleanliness'];
        $count++;
      }
      if( $row['value'] ?? 0 ){
        $sum += $row['value'];
        $count++;
      }
      if( $row['checkin'] ?? 0 ){
        $sum += $row['checkin'];
        $count++;
      }
      if( $row['accuracy'] ?? 0 ){
        $sum += $row['accuracy'];
        $count++;
      }
      if( $row['services'] ?? 0){
        $sum += $row['services'];
        $count++;
      }
      if( $row['comfort'] ?? 0 ){
        $sum += $row['comfort'];
        $count++;
      }
      if( $row['rooms'] ?? 0 ){
        $sum += $row['rooms'];
        $count++;
      }
      if( $row['wifi'] ?? 0 ){
        $sum += $row['wifi'];
        $count++;
      }
      $reviews[$key]['rate'] = ( $count )? round( $sum / $count, 1 ) : '-';
    }
  }
  return $reviews;
}

function table_coupons(){ 
  if( $coupons = db::list( DATABASENAME.'.coupons', 'company_id='.$_SESSION['COMPANY'] ) )
    foreach( $coupons as $key => $row )
      $coupons[$key]['allapotstr'] = ( $row['signs'][0] )? ( ( $row['signs'][0] == 1 )? 'Rejtett' : 'Aktív' ) : 'Törölt';
  return $coupons;
}

function table_popups(){ 
  if( $popups = db::list( DATABASENAME.'.popups', 'company_id='.$_SESSION['COMPANY'] ) )
    foreach( $popups as $key => $row )
      $popups[$key]['allapotstr'] = ['Törölt','Rejtett','Belépéskor','Kilépéskor'][$row['signs'][0]];
  return $popups;
}

function table_galleries(){ 
  if( $galleries = db::list( DATABASENAME.'.galleries', 'company_id='.$_SESSION['COMPANY'] ) )
    foreach( $galleries as $key => $row )
      $galleries[$key]['allapotstr'] = ( $row['signs'][0] )? ( ( $row['signs'][0] == 1 )? 'Rejtett' : 'Aktív' ) : 'Törölt';
  return $galleries;
}

function table_messagetemplateslist(){ 
  if( $message_templates = db::list( 'message_templates', 'company_id='.$_SESSION['COMPANY'] ) )
    foreach( $message_templates as $key => $row )
      $message_templates[$key]['allapotstr'] = ( $row['signs'][0] )? ( ( $row['signs'][0] == 1 )? 'Rejtett' : 'Aktív' ) : 'Törölt';
  return $message_templates;
}

function table_subscribers(){ 
  return db::list_subscribers();
}

function table_csatornalist(){
  return db::list_csatorna();
}

function table_messages(){ 
  return db::list( DATABASENAME.'.messages', 'company_id='.$_SESSION['COMPANY'], 'id DESC' );
}

function table_periods(){
  if( $periods = db::list( DATABASENAME.'.periods', 'company_id='.$_SESSION['COMPANY'] ))
    foreach( $periods as $key => $row )
      $periods[$key]['is_trash-empty'] = ( $row['priority'] == 0 ) ? 1 : 0;
  return $periods;
}

function table_accommodation_unit_type_prices(){
  if( $accommodationUnitTypePrices = db::list_accommondationUnitTypePrices()){
    foreach( $accommodationUnitTypePrices as $key => $accommodationUnitTypePrice ){
      $accommodationUnitTypePrices[$key]['guestsprices'] = '';
      foreach( ( $accommodationUnitTypePrice['prices'] ?? 0 )? json_decode( $accommodationUnitTypePrice['prices'], true ) : [] as $price ){
        if( $accommodationUnitTypePrices[$key]['guestsprices'] != '' )
          $accommodationUnitTypePrices[$key]['guestsprices'].= '<br>';
        $accommodationUnitTypePrices[$key]['guestsprices'].= $price[0].' fő '.$price[1].' Ft/éj';
      }
    }
  }
  return $accommodationUnitTypePrices;
}

function table_invoices(){ 
  if( $invoices = db::list_invoices())
    foreach( $invoices as $key => $invoice ){
      $invoices[$key]['allapotstr'] = ['', 'küldésre vár', 'folyamatban', 'számla', 'előleg', 'törlő', 'módosító'][$invoice['signs'][2]];
      $customer = json_decode( $invoice['customer'] );
      $invoices[$key]['customer_name'] = $customer->name ?? '';
    }
  return $invoices;
}