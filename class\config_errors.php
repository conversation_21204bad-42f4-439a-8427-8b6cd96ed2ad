<?php
/**
 * Nyomkövetés:
 * Bárhol a kódban elhelyezhető
 * user_error('<PERSON><PERSON><PERSON><PERSON><PERSON> szöveg: '.$valtozo);
 * user_error('<PERSON><PERSON><PERSON> kód követ<PERSON>e, hogy használja e még valami', E_USER_DEPRECATED);
 * nyomkövetés logja külön fájlba kerül. pl. DEBUG_19071012.txt kérhető saját logolással is. pl. TR_DEBUG_19071012.txt
 *
 * Saját logolás:
 * Saját pillanatnyi user IP-t le kell kérdezni és azt kell megadni. Saját munkamenet esetén saját fájlba kerül. pl. TR_NOTICE_19071012.txt
 */
const DEVELOPERS  = [ ['signo' => 'TR', 'ip' => '*************'],
                      ['signo' => '', 'ip' => '']
                    ];
const LOGINTERVAL = 'ymdH'; // Óránként új fájl