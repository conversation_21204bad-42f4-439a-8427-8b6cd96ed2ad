  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Automatizálás</h5>
      </header>
      <article>
        <details>
          <summary>Foglalási ürlapon foglalás történt</summary>
          <form name="form_automation" method="post" action="<?= http::$path ?>/automation">
            <input type="hidden" name="event" value="booking">
            <h4>Esemény</h4>
            <p>A saját honlapon, vagy bár<PERSON> elhelyezett foglalási ürlapon foglalás történt.</p>
            <hr>
            <h4>Művelet</h4>
            <p>Foglalás rögzítése a rendszerben.</p>         
            <hr>
            <h4>Email küldés</h4>
            <ul class="formbox">
              <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
                <select name="template_operator_booking" placeholder="">
                  <option value="0">Nincs email küldés</option>
                  <?php foreach( response::$vw->view->templates as $template ){ ?>
                  <option value="<?= $template->id ?>"<?= ( $_POST['template_operator_booking'] == $template->id )? ' selected' : '' ?>><?= $template->name ?></option>
                  <?php } ?>
                </select>
                <label>Email a szállásadónak</label>
              </li>
              <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
                <select name="template_customer_booking" placeholder="">
                  <option value="0">Nincs email küldés</option>
                  <?php foreach( response::$vw->view->templates as $template ){ ?>
                  <option value="<?= $template->id ?>"<?= ( $_POST['template_customer_booking'] == $template->id )? ' selected' : '' ?>><?= $template->name ?></option>
                  <?php } ?>
                </select>
                <label>Email a foglalónak</label>
              </li>
              <li><button name="btn_automation">Mentés</button></li>
            </ul>           
          </form>
        </details>

        <details>
          <summary>Nem történt határidőre előleg fizetés</summary>
          <form name="form_automation" method="post" action="<?= http::$path ?>/automation">
            <input type="hidden" name="event" value="advance_payment">
            <h4>Esemény</h4>
            <p>Foglalástól számított megadott napon belül nem történt meg az előleg befizetése.</p>
            <hr>
            <h4>Művelet</h4>
            <p>Foglalás törlése.</p>
            <ul class="formbox">
              <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
                <select name="is_advance_payment" placeholder="">
                  <option value="0">Kikapcsolva</option>
                  <option value="1">Beikapcsolva</option>
                </select>
                <label>Művelet</label>
              </li>
              <li class="form col5 bgonto" data-to="nap">
                <input type="text" name="advance_payment_deadline" value="<?= $_POST['advance_payment_deadline'] ?>" placeholder="">
                <label>Előleg fizetési határidő</label>
                <div class="help">Foglalás időpontjától számított napok</div>
              </li>
            </ul>
            <hr>
            <h4>E-mail küldés</h4>
            <ul class="formbox">
              <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
                <select name="template_operator_advance_payment" placeholder="">
                  <option value="0">Nincs email küldés</option>
                  <?php foreach( response::$vw->view->templates as $template ){ ?>
                  <option value="<?= $template->id ?>"<?= ( $_POST['template_operator_advance_payment'] == $template->id )? ' selected' : '' ?>><?= $template->name ?></option>
                  <?php } ?>
                </select>
                <label>Email a szállásadónak</label>
              </li>
              <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
                <select name="template_customer_advance_payment" placeholder="">
                  <option value="0">Nincs email küldés</option>
                  <?php foreach( response::$vw->view->templates as $template ){ ?>
                  <option value="<?= $template->id ?>"<?= ( $_POST['template_customer_advance_payment'] == $template->id )? ' selected' : '' ?>><?= $template->name ?></option>
                  <?php } ?>
                </select>
                <label>Email a foglalónak</label>
              </li>
              <li><button name="btn_automation">Mentés</button></li>
            </ul>           
          </form>
        </details>

        <details>
          <summary>Vendég adatok nem érkeztek meg</summary>
        </details>
        <details>
          <summary>Számla készült</summary>
        </details>
        <details>
          <summary>Távozás történt</summary>
        </details>
      </article>
    </div>
  </section>
