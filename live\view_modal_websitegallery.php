<link href="/shared/js/spritzer/ui/ui-library.css" rel="stylesheet">
<link href="/shared/js/spritzer/ui/ui-upload.css" rel="stylesheet">
<header>
  <h6>Galéria</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_gallery">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <ul class="formbox">
      <li class="form col3" style="--toicon:var(--icon-angle-double-down)">
        <select name="status" placeholder="">
          <option value="1"<?=( $_POST['status'] == '1' )? ' selected' : '' ?>>Nem látható</option>  
          <option value="2"<?=( $_POST['status'] == '2' )? ' selected' : '' ?>>Látható</option>  
        </select>
        <label>Állapot</label>
      </li>
      <li class="form col7">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?? '' ?>">
        <label>Megnevezés</label>
      </li>
      <li class="form col0">
        <textarea name="description"><?= $_POST['description'] ?? '' ?></textarea>
        <label>Leírás</label>
      </li>
      <li class="col0">
        <details>
          <summary class="btn">Képek feltöltése</summary>
          <ul data-upload='{"maxdb": 50, "tableName": "<?= DATABASENAME ?>.galleries", "tableId": <?= $_POST['id'] ?>}'>
            <li>
              <span class="box__icon" style="--icon:var(--icon-upload)">max <?= (20-count($kids ?? []))?> db</span>
              <input class="box__input" id="foto" type="file" name="foto[]" data-multiple-caption="{count} kiválasztott fájl" multiple>
              <label for="foto"><strong>Válassz fájlt</strong><span class="box__dragndrop"> vagy húzd ide</span></label>
            </li>
            <li class="box__uploading">Feltöltés&hellip;</li>
            <li class="box__success">Kész! <a href="#" class="box__restart" role="button">További fájlok?</a></li>
            <li class="box__error">Hiba! <span></span>. <a href="#" class="box__restart" role="button">Próbáld újra!</a></li>
          </ul>
        </details>
      </li>
      <li class="col0" data-library="">
        <input type="hidden" name="sequence" value="<?= json_encode( $_POST['sequence'] ) ?>">
        <?php if( count( $_POST['sequence'] ) ){ ?>
        <?php   foreach( $_POST['sequence'] as $pic ){ ?>
        <?php     if( file_exists('upload/'.$_SESSION['COMPANY'].'/pic'.sprintf( "%04d", $pic ).'.jpg' ) ){ ?>
        <div draggable="true" data-key="<?= $pic ?>">
          <a style="--icon:var(--icon-trash-empty)"
              title = "Törlés"
            onclick = "return confirm( 'Biztos törlöd?' )"></a>
          <img src="/upload/<?= $_SESSION['COMPANY'] ?>/pic<?= sprintf( "%04d", $pic ).'.jpg?'.md5( date( "YmdHis" ) ) ?>" title="<?= $pic ?>">
        </div>
        <?php } } } ?>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Galéria mentése">Módosít</button>
</footer>