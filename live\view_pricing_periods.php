  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új időszak</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable( 
          'periods',
          [ 'name' => ['th' => 'Megnevezés'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés',
                'trash-empty:event:Törlés:::confirm:where:is_trash-empty'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { $, $$, datatable, dialog, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd, periodDays, li
      switch( ifunction ){
        case 'periods_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_period' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/period',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Adatok módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'periods_edit_close':
          location.replace( '/pricing/periods' )
        break
        case 'periods_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( {
            url: '/modal/perioddel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/pricing/periods' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a sablon!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'periods_edit':
          return {
            'click': [
              ['div.del-days', 'periods_del_days'],
              ['div.add-days', 'periods_add_days']
            ]
          }
        break
        case 'periods_add_days':
          li = event.target.parentNode
          periodDays = JSON.parse( $( 'input[name="periodDays"]' ).value )
          const days = $( 'input', li ).value
          
          $( 'input', li ).value = ''
          periodDays.push( days )
          $( 'input[name="periodDays"]' ).value = JSON.stringify( periodDays )
          li.insertAdjacentHTML(
            'beforebegin',
            `
            <li class="form col5" style="--toicon:var(--icon-trash-empty)">
              <input type="text" placeholder="" value="${days}" disabled>
              <label>Nap(ok)</label>
              <div class="del-days" data-key="${periodDays.length - 1}" title="Nap(ok) törlése"></div>
            </li>
            `
          )
        break
        case 'periods_del_days':
          li = event.target.parentNode
          periodDays = JSON.parse( $( 'input[name="periodDays"]' ).value )
          const key = event.target.dataset.key
          periodDays.splice( key, 1 )
          $( 'input[name="periodDays"]' ).value = JSON.stringify( periodDays )
          li.remove()
          li = $$( 'div.del-days' )
          for( let i = 0; i < li.length; i++ )
            li[i].dataset.key = i
        break
      }
    }

    window.addEventListener('DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új sablon',
          label: 'Megnevezés'
        },
        eventManagerFunctions
      } )
    } )
  </script>