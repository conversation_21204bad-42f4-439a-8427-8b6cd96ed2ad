<?php
/**
 * <PERSON><PERSON> küld<PERSON>
 *
 * @method `body_replace();` <PERSON><PERSON> lecserélése a szövegben
 * @method `speedsend();`    Gyo<PERSON> email küldés
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2017, T<PERSON><PERSON><PERSON> Róbert
 * 
 * @version 1.1.0
 * @since 1.1.0 2025.05.08 embed bevezet<PERSON>e a mellékletek hozzáadásához
 * @since 1.0.1 2025.04.22 Optimalizálás, kiegészítés `speedsend();`
 * @since 1.0.0 2022.08.08
 */

require_once './shared/app/PHPMailer/PHPMailer.php';
require_once './shared/app/PHPMailer/SMTP.php';

class mailer extends PHPMailer{

/**
 * Constructor
 * @param bool $exceptions Külső kivételeket dobjuk?
 */
  public function __construct( $exceptions = null ){
    parent::__construct( $exceptions );
  }
/**
 * <PERSON><PERSON> lecserélése a szövegben
 * @param  mixed  $search  Mit cserélünk. string vagy tömb
 * @param  mixed  $replace Mire. string vagy tömb
 * @param  string $body    Email szövege
 */
  public static function body_replace( $search, $replace, $body ){
    return str_replace( $search, $replace, $body );
  }
  
/**
 * Gyors email küldés
 * @param  string $subject Email tárgya
 * @param  string $body    Email szövegben
 * @param  mixed  $to      Címzett(ek) email címe 'Neve <<EMAIL>>,email2.domain2.tld'
 * @param  string $from    Küldő email címe
 * @param  string $reply   Válasz email címe
 * @param  array  $embeds  Mellékletek elérési útja, azonosítója, fájl neve vesszővel elválasztva ['a1,b1,c1', 'a2,b2,c2']
 *                         a1 = elérési út, b1 = azonosító, c1 = fájl néve pl. ['items/logo.png,logo,logo.png']
 */
  public static function speedsend( $subject, $body, $to, $from, $reply = null, $embeds = null ){
    if( $body !='' ){
      $mailer = new PHPMailer;
      $to = explode( ',', $to );
      foreach( $to as $t ){
        $name = false;
        if( strpos( $t, '>' ) ){
          $name = substr( $t, 0, strpos( $t, '<' ) );
          $name = trim( $name );
          $name = ( $name != '')? $name : false;
          $address = substr( $t, strpos( $t, '<' ) +1, -1 );
        }else $address = $t;
        $mailer->AddAddress( $address, $name );
      }
      $name = false;
      if( strpos( $from, '>' ) ){
        $name = substr( $from, 0, strpos( $from, '<' ) );
        $name = trim( $name );
        $name = ( $name != '')? $name : false;
        $address = substr( $from, strpos( $from, '<' ) +1, -1 );
      }else $address = $from;
      $mailer->From = $address;
      if( $name ) $mailer->FromName = $name;
      if( $reply ?? 0 ){
        $name = false;
        if( strpos( $reply, '>' ) ){
          $name = substr( $reply, 0, strpos( $reply, '<' ) );
          $name = trim( $name );
          $name = ( $name != '')? $name : false;
          $address = substr( $reply, strpos( $reply, '<' ) +1, -1 );
        }else $address = $reply;
        $mailer->addReplyTo( $address, $name );
      }
      if( $embeds ?? 0 )
        foreach( $embeds as $embed ){
          $embed = explode( ',', $embed );
          $mailer->AddEmbeddedImage( $embed[0] ?? '', $embed[1] ?? '', $embed[2] ?? '' );
        }

      $mailer->CharSet = "utf-8";
      $mailer->Encoding = '8bit';
      $mailer->isHTML( true ); 
      $mailer->Subject = $subject;
      $mailer->Body = $body;
      $mailer->AltBody = strip_tags( $body );
      if( $mailer->Send() )
        return true;
      else
        return 'error: '.$mailer->ErrorInfo;
    }
  }

}