<!DOCTYPE html>
<html lang="hu" class="js">
  <head>
    <meta charset="utf-8">
    <title><?= response::$vw->meta->title ?? '' ?></title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="handheldfriendly" content="true">
    <meta name="mobileoptimized" content="320">
    <meta name="viewport" content="width=device-width, initial-scale=1,shrink-to-fit=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="author" content="HolMiKi">
    <meta name="google" content="notranslate">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#317EFB">
    <link href="<?= http::$path ?>/shared/js/spritzer/dialog/dialog.css" rel="stylesheet">
    <link href="<?= http::$path ?>/items/css/style.css" rel="stylesheet">
    <?php /*
    <link href="<?= http::$path.response::$vw->css ?>" rel="stylesheet">
    */ ?>
  </head>
  <body id="<?= response::$vw->body ?>" class="logged">
    <main>
      <header>
        <ul class="header-nav">
          <li class="logo"><a href="/" title=""><img src="/items/tren.png"></a></li>
          <li class="minimalize">
            <i style="--icon:var(--icon-menu)"></i>
            <?php if( response::$vw->timeout ?? 0 ){ ?>
            <span class="timeout"><?= response::$vw->timeout ?></span>
            <?php } ?>
          </li>
        </ul>
        <ul class="header-links<?=( $_SESSION['MININAV'] > 0 )? ( ( $_SESSION['MININAV'] > 1 )? ' zeronav' : ' mininav') : '' ?>">
          <li>
            <details>
              <summary>
                <?php if( $_SESSION['COMPANY'] ?? 0 ?:0 and file_exists( 'upload/'.$_SESSION['COMPANY'].'/logo.png' ) ){ ?>
                <div class="avatar"><img src="<?= '/upload/'.$_SESSION['COMPANY'].'/logo.png' ?>"></div>
                <?php } ?>
                <div>
                  <span><?= setup::$company->name ?? 'Anonymus' ?></span><br>
                  <span><?= setup::$user->nickname ?? 'Anonymus' ?></span>
                </div>
              </summary>
              <div>
                <a href="<?= http::$path ?>/logout" title=""><i style="--icon:var(--icon-logout)"></i>Kijelentkezés</a>
              </div>
            </details>
          </li>
        </ul>
      </header>
      <div class="wrapper">
        <?php if( file_exists( 'view_nav.php' ) ) require_once 'view_nav.php' ?>
        <?php if( file_exists( 'view_'.response::$vw->body.'.php' ) ) require_once 'view_'.response::$vw->body.'.php'; ?>
        <script src="/js_dashnav.js"></script>
      </div>
    </main>
    <?php /*
    <script src="//code.tidio.co/hwubnfjtq230jcjttqbrlbghsmazqeda.js" async></script>
    <script type="text/javascript" id="hs-script-loader" async defer src="//js-eu1.hs-scripts.com/*********.js"></script>
    */ ?>
  </body>
</html>
