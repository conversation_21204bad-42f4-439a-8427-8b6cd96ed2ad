<?php
if($_SESSION[REG]>0){
?>
      <h2>Adatlap - <?php echo $_SESSION[REGBECNEV]?></h2>
      <div id="menuful">
        <ul>
          <?php $regisztracio->regmenu($url[1])?>
<?php
  if($_SESSION[REGJOG][0]=='3'){
?>
          <li<?php echo ($url[1]=='rinfok')?' class=fulkiv>Irányítópult':'><a href="/rendszer/rinfok" title="Irányítópult">Irányítópult</a>'?></li>
          <li<?php echo ($url[1]=='riroda')?' class=fulkiv>Iroda':'><a href="/rendszer/riroda" title="Iroda adatai">Iroda</a>'?></li>
<?php
  }
  if($_SESSION[REGJOG][0]<'4'){
?>
          <li<?php echo ($url[0]=='hirdkarb')?' class=fulkiv>Ingatlanok':'><a href="/hirdkarb" title="Ingatlanok felvitele / karbantartása">Ingatlanok</a>'?></li>
<?php
  }
?>
        </ul>
      </div>
<?php
}
?>
      <h2>Hirdetések</h2>
<?php
$def=mysql_fetch_object(mysql_query('SELECT * FROM def')) or die('hiba: '.mysql_error());
$listkomfort=explode(';',$def->komfort);
$listallapot=explode(';',$def->allapot);
$listkilatas=explode(';',$def->kilatas);
$listfutes=explode(';',$def->futes);
$listfelirat=explode(';',$def->felirat);
switch($url[1]){
  case 'szerk':
    if($_SESSION[REG][0]>'0' and $_SESSION[REGJOG][0]<'4'){ // iroda és magán
      if($_POST['gomb_szerk']){
        if($_POST['kategoria'] and $_POST['kategoria'] > 0){ // post ellenőrzés kell
          $rekordk = mysql_fetch_object(mysql_query("SELECT * FROM kategoria WHERE kategoria_id=".$_POST['kategoria']." LIMIT 1")) or die("hiba:" . mysql_error());
          if($_GET['ingatlan_id']>0){
            mysql_query("UPDATE ingatlan SET ertemail='".$_POST['ertemail']."',erttelefon='".$_POST['erttelefon']."',telepules_id='".$_POST['telepules_id']."',
                                             kregio='".$_POST['kregio']."',vregio='".$_POST['vregio']."',ajanlat='".$_POST['ajanlat']."',
                                             komfort='".$_POST['komfort']."',allapot='".$_POST['allapot']."',kilatas='".$_POST['kilatas']."',
                                             kategoria='".$_POST['kategoria']."',kategoriafo='".$rekordk->fneve."',kategoriaal='".$rekordk->kneve."',
                                             csere='".$_POST['csere']."',terhelt='".$_POST['terhelt']."',ar='".str_replace(' ','',$_POST['ar'])."',
                                             tertelek='".$_POST['tertelek']."',teralap='".$_POST['teralap']."',terasz='".$_POST['terasz']."',
                                             belmag='".$_POST['belmag']."',muti='".$_POST['muti']."',szint='".$_POST['szint']."',emelet='".$_POST['emelet']."',
                                             szoba='".$_POST['szoba']."',szobafel='".$_POST['szobafel']."',energtan='".$_POST['energtan']."',
                                             refszam='".$_POST['refszam']."',hol_pontos='".$_POST['hol_pontos']."',hol_pontosk='".$_POST['hol_pontosk']."',
                                             futes='".$_POST['futes']."',leiras='".$_POST['leiras']."',lift='".$_POST['lift']."',egyeb='".$_POST['egyeb']."',
                                             modokelt='".date('Y-m-d H:i')."'
                          WHERE ingatlan_id=".$_GET['ingatlan_id'])or die('hiba: '.mysql_error());
          }else{
            mysql_query("INSERT INTO ingatlan (reg_id,iroda_id,ertemail,erttelefon,telepules_id,kregio,vregio,ajanlat,komfort,allapot,kilatas,
                                         kategoria,kategoriafo,kategoriaal,csere,terhelt,ar,tertelek,teralap,terasz,belmag,muti,szint,emelet,szoba,szobafel,
                                         energtan,refszam,hol_pontos,hol_pontosk,futes,leiras,lift,egyeb,modokelt,rogzkelt)
                                        VALUES ('".$_SESSION['REG']."','".$_SESSION['REGCEG']."',
                                                '".$_POST['ertemail']."','".$_POST['erttelefon']."',
                                                '".$_POST['telepules_id']."','".$_POST['kregio']."','".$_POST['vregio']."','".$_POST['ajanlat']."',
                                                '".$_POST['komfort']."','".$_POST['allapot']."','".$_POST['kilatas']."','".$_POST['kategoria']."',
                                                '".$rekordk->fneve."','".$rekordk->kneve."','".$_POST['csere']."','".$_POST['terhelt']."',
                                                '".str_replace(' ','',$_POST['ar'])."','".$_POST['tertelek']."','".$_POST['teralap']."',
                                                '".$_POST['terasz']."','".$_POST['belmag']."','".$_POST['muti']."',
                                                '".$_POST['szint']."','".$_POST['emelet']."','".$_POST['szoba']."',
                                                '".$_POST['szobafel']."','".$_POST['energtan']."',
                                                '".$_POST['refszam']."','".$_POST['hol_pontos']."','".$_POST['hol_pontosk']."','".$_POST['futes']."',
                                                '".$_POST['leiras']."','".$_POST['lift']."','".$_POST['egyeb']."',
                                                '".date('Y-m-d H:i')."','".date('Y-m-d H:i')."'
                                               )") or die('hiba: '.mysql_error());
            $_GET['ingatlan_id']=mysql_insert_id();
          }
          if($_FILES['foto']['tmp_name'] and $_GET['ingatlan_id']>0){
            /*
            $kdb = count($_FILES['foto']['tmp_name']);
            for($j=0;$j<$kdb;$j++){
              $keptipus=explode('.',$_FILES['foto']['name'][$j]);
              $keptipus[1]=strtolower($keptipus[1]);
              if ('jpg' == $keptipus[1] or 'gif' == $keptipus[1] or 'png' == $keptipus[1] or 'jpeg' == $keptipus[1]){
                $kid=1;
                $kovetkezo=1;
                while ($kid<51){
                  if (file_exists('kepek/'.$_GET['ingatlan_id'].'_'.$kid.'n.jpg')){
                    $kovetkezo=$kid+1;
                  }
                  $kid++;
                }
                $kid=$kovetkezo;
                $kep = 'kepek/'.$_GET['ingatlan_id'].'_'.$kid;
                meretezo_jpg($_FILES['foto']['tmp_name'][$j],106,106,$kep.'n.jpg',100,1,'z');
                meretezo_jpg($_FILES['foto']['tmp_name'][$j],0,93,$kep.'l.jpg',100,1,'y');
                meretezo_jpg($_FILES['foto']['tmp_name'][$j],320,0,$kep.'i.jpg',100,1,'x');
                meretezo_jpg($_FILES['foto']['tmp_name'][$j],900,600,$kep.'.jpg',100,1,'');
                if($_POST['fotosor']) $_POST['fotosor'].=','.$kid;
                else $_POST['fotosor']=$kid;
                mysql_query('UPDATE ingatlan SET fotosor="'.$_POST['fotosor'].'" WHERE ingatlan_id='.$_GET['ingatlan_id']) or die('hiba: '.mysql_error());
              }
            }
            */
          }
        }
      }else $_POST['ajanlat']='E';
      if($_GET['ingatlan_id']>0){
        $eredm = mysql_query("SELECT ingatlan.*,kategoria.fneve,kategoria.kneve,megye_id FROM ingatlan,kategoria,hol_telepules
                               WHERE kategoria=kategoria_id AND ingatlan.telepules_id=hol_telepules.telepules_id AND ingatlan_id=".$_GET['ingatlan_id']." LIMIT 1")
          or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
        if($rekord = mysql_fetch_object($eredm)){
          $_POST['erttelefon']=$rekord->erttelefon;
          $_POST['ertemail']=$rekord->ertemail;
          $_POST['telepules_id']=$rekord->telepules_id;
          $_POST['megye']=$rekord->megye_id;
          $_POST['kregio']=$rekord->kregio;
          $_POST['vregio']=$rekord->vregio;
          $_POST['ajanlat']=$rekord->ajanlat;
          $_POST['allapot']=$rekord->allapot;
          $_POST['komfort']=$rekord->komfort;
          $_POST['kilatas']=$rekord->kilatas;
          $_POST['kat']=$rekord->fneve;
          $_POST['kategoria']=$rekord->kategoria;
          $_POST['csere']=$rekord->csere;
          $_POST['terhelt']=$rekord->terhelt;
          $_POST['ar']=$rekord->ar;
          $_POST['teralap']=$rekord->teralap;
          $_POST['tertelek']=$rekord->tertelek;
          $_POST['terasz']=$rekord->terasz;
          $_POST['belmag']=$rekord->belmag;
          $_POST['muti']=$rekord->muti;
          $_POST['szint']=$rekord->szint;
          $_POST['emelet']=$rekord->emelet;
          $_POST['szoba']=$rekord->szoba;
          $_POST['szobafel']=$rekord->szobafel;
          $_POST['energtan']=$rekord->energtan;
          $_POST['refszam']=$rekord->refszam;
          $_POST['hol_pontos']=$rekord->hol_pontos;
          $_POST['hol_pontosk']=$rekord->hol_pontosk;
          $_POST['futes']=$rekord->futes;
          $_POST['leiras']=$rekord->leiras;
          $_POST['lift']=$rekord->lift;
          $_POST['egyeb']=$rekord->egyeb;
          $_POST['fotosor']=$rekord->fotosor;
        }else $_GET['ingatlan_id']=0;
      }
?>
      <div id="urlap">
        <a class="gomb gomblink" href="/hirdkarb">Vissza a listához</a>
        <form class="clear" name="formhirdkarb" method="post" action="/hirdkarb/szerk<?php echo($_GET['ingatlan_id']>0)?'?ingatlan_id='.$_GET['ingatlan_id']:''?>" enctype="multipart/form-data">
          <div class="flex111">
            <div>
              <h3>Értesítési adatok</h3>
              <ul class="form-text form-pb">
<?php
      if($_SESSION[REGCEG]>0){
?>
                <li class="form-w9 form-m"><input class="formelem tooltip tt_focus" type="text" name="refszam" value="<?php echo $_POST['refszam']?>" placeholder="Ingatlanos kód"><?php $doksi->doksi_mezo('hirdkarb','Urefszam')?></li>
<?php
      }else{
        $regisztracio->regrekord();
        SQL_kapcs();
        if(!$_POST['erttelefon']) $_POST['erttelefon']=$regisztracio->reg_rekord->telefon;
        if(!$_POST['ertemail']) $_POST['ertemail']=$regisztracio->reg_rekord->email;
?>
                <li class="form-w9 form-m"><input class="formelem tooltip tt_focus" type="text" name="erttelefon" value="<?php echo $_POST['erttelefon']?>" placeholder="Telefon"><?php $doksi->doksi_mezo('hirdkarb','Uerttelefon')?></li>
                <li class="form-w9 form-m"><input class="formelem tooltip tt_focus" type="text" name="ertemail" value="<?php echo $_POST['ertemail']?>" placeholder="Email"><?php $doksi->doksi_mezo('hirdkarb','Uertemail')?></li>
<?php
      }
?>
              </ul>
              <h3>Ingatlanadatok</h3>
              <ul class="form-select form-pb form-v clear">
                <li class="form-fejlec form-w9">Megye</li>
                <li class="form-w9 form-m" data-icon="&#xf0dc">
                  <select class="formelem" name="megye">
<?php
          $eredmt = mysql_query("SELECT DISTINCT neve,regio_id FROM hol_regio, hol_telepules WHERE regio_id=megye_id AND mi='M' ORDER BY neve")
            or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
          while($rekordt = mysql_fetch_object($eredmt)){
            if(!$_POST['megye']) $_POST['megye']=$rekordt->regio_id;
?>
                    <option value="<?php echo $rekordt->regio_id?>"<?php echo ($_POST['megye']==$rekordt->regio_id)?' selected':''?>><?php echo $rekordt->neve?></option>
<?php
          }
?>
                  </select>
                </li>
              </ul>
              <ul class="form-select form-pb form-v clear">
                <li class="form-fejlec form-w7">Település</li>
                <li class="form-fejlec form-w3">Kerület</li>
                <li class="form-w7 form-m" data-icon="&#xf0dc">
                  <select class="formelem" name="telepules_id">
<?php
          $eredmt = mysql_query("SELECT DISTINCT telepules,telepules_id FROM hol_telepules WHERE megye_id='".$_POST['megye']."' ORDER BY telepules")
            or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
          while($rekordt = mysql_fetch_object($eredmt)){
?>
                    <option value="<?php echo $rekordt->telepules_id?>"<?php echo ($_POST['telepules_id']==$rekordt->telepules_id)?' selected':''?>><?php echo $rekordt->telepules?></option>
<?php
          }
?>
                  </select>
                </li>
                <li class="form-w3 form-m" data-icon="&#xf0dc">
                  <select class="formelem" name="kregio"<?php echo($_POST['telepules_id']!=3347)?' disabled':''?>>
                    <option<?php echo($_POST['kregio']=='')?' selected':''?>></option>
                    <option<?php echo($_POST['kregio']=='I.')?' selected':''?>>I.</option>
                    <option<?php echo($_POST['kregio']=='II.')?' selected':''?>>II.</option>
                    <option<?php echo($_POST['kregio']=='III.')?' selected':''?>>III.</option>
                    <option<?php echo($_POST['kregio']=='IV.')?' selected':''?>>IV.</option>
                    <option<?php echo($_POST['kregio']=='V.')?' selected':''?>>V.</option>
                    <option<?php echo($_POST['kregio']=='VI.')?' selected':''?>>VI.</option>
                    <option<?php echo($_POST['kregio']=='VII.')?' selected':''?>>VII.</option>
                    <option<?php echo($_POST['kregio']=='VIII.')?' selected':''?>>VIII.</option>
                    <option<?php echo($_POST['kregio']=='IX.')?' selected':''?>>IX.</option>
                    <option<?php echo($_POST['kregio']=='X.')?' selected':''?>>X.</option>
                    <option<?php echo($_POST['kregio']=='XI.')?' selected':''?>>XI.</option>
                    <option<?php echo($_POST['kregio']=='XII.')?' selected':''?>>XII.</option>
                    <option<?php echo($_POST['kregio']=='XIII.')?' selected':''?>>XIII.</option>
                    <option<?php echo($_POST['kregio']=='XIV.')?' selected':''?>>XIV.</option>
                    <option<?php echo($_POST['kregio']=='XV.')?' selected':''?>>XV.</option>
                    <option<?php echo($_POST['kregio']=='XVI.')?' selected':''?>>XVI.</option>
                    <option<?php echo($_POST['kregio']=='XVII.')?' selected':''?>>XVII.</option>
                    <option<?php echo($_POST['kregio']=='XVIII.')?' selected':''?>>XVIII.</option>
                    <option<?php echo($_POST['kregio']=='XIX.')?' selected':''?>>XIX.</option>
                    <option<?php echo($_POST['kregio']=='XX.')?' selected':''?>>XX.</option>
                    <option<?php echo($_POST['kregio']=='XXI.')?' selected':''?>>XXI.</option>
                    <option<?php echo($_POST['kregio']=='XXII.')?' selected':''?>>XXII.</option>
                    <option<?php echo($_POST['kregio']=='XXIII.')?' selected':''?>>XXIII.</option>
                  </select>
                </li>
              </ul>
              <script type="text/javascript">
                var formmegye = document.formhirdkarb.megye;
                var formtelepulesid = document.formhirdkarb.telepules_id;
                var formkregio = document.formhirdkarb.kregio;
                formmegye.addEventListener("change",function(){
                  var xhr = new XMLHttpRequest();
                  var fd = new FormData();
                  fd.append("megye", this.value);
                  xhr.open('POST','/ajax_formkiir_selecttelepules.php', true);
                  xhr.onreadystatechange = function(){
                    if(xhr.readyState == 4 && xhr.status == 200){
                      formtelepulesid.innerHTML = xhr.responseText;
                      formkregio.value='';
                      formkregio.disabled = true;
                    }
                  };
                  xhr.send(fd);
                });
                formtelepulesid.addEventListener("change",function(){
                  if(this.value==3347){ // Budapest
                    formkregio.disabled = false;
                  }else{
                    formkregio.value='';
                    formkregio.disabled = true;
                  }
                });
              </script>
              <ul class="form-text form-pb">
                <li class="form-fejlec">Városrész</li>
                <li class="form-w9 form-m"><input class="formelem tooltip tt_focus" type="text" name="vregio" value="<?php echo $_POST['vregio']?>"><?php $doksi->doksi_mezo('hirdkarb','Uvregio')?></li>
                <li class="form-fejlec">Közelebbi cím</li>
                <li class="form-w9 form-m"><input class="formelem tooltip tt_focus" type="text" name="hol_pontos" value="<?php echo $_POST['hol_pontos']?>"><?php $doksi->doksi_mezo('hirdkarb','Uholpontos')?></li>
                <li class="form-fejlec">Megjelenített közelebbi cím</li>
                <li class="form-w9 form-m"><input class="formelem tooltip tt_focus" type="text" name="hol_pontosk" value="<?php echo $_POST['hol_pontosk']?>"><?php $doksi->doksi_mezo('hirdkarb','Uholpontosp')?></li>
              </ul>
              <ul class="form-pb form-v clear">
                <li class="form-fejlec form-w3">Megbízás</li>
                <li class="form-fejlec form-w4">Megjelenés</li>
                <li class="form-fejlec form-w4">Ár</li>
              </ul>
              <ul class="form-text form-select form-pb form-v clear">
                <li class="form-w3 form-m" data-icon="&#xf0dc">
                  <select class="formelem" name="ajanlat">
                    <option value="E"<?php echo($_POST['ajanlat']=='E')?' selected':''?>>Eladó</option>
                    <option value="K"<?php echo($_POST['ajanlat']=='K')?' selected':''?>>Kiadó</option>
                  </select>
                </li>
                <li class="form-w4 form-m" data-icon="&#xf0dc">
                  <select class="formelem tooltip tt_focus" name="muti">
                    <option value="P"<?php echo($_POST['muti']=='P')?' selected':''?>>Látható</option>
                    <option value="R"<?php echo($_POST['muti']=='R')?' selected':''?>>Rejtett</option>
                  </select><?php $doksi->doksi_mezo('hirdkarb','Umuti')?>
                </li>
                <li class="form-w4 form-m" data-to="Ft">
                  <input class="formelem" type="text" name="ar" value="<?php echo number_format($_POST['ar'],0,',',' ')?>" onkeyup="szamcsop(this);">
                  <script type="text/javascript">
                    function szamcsop(obj){
                      str = obj.value;
                      output = str.toString();
                      output = output.replace(/[^0-9]/gi,"");
                      var sRegExp = new RegExp('(-?[0-9]+)([0-9]{3})');
                      while(sRegExp.test(output)){ output = output.replace(sRegExp, '$1 $2'); }
                      obj.value = output;
                    }
                  </script>
                </li>
              </ul>
              
              <ul class="form-select form-pb form-v clear">
                <li class="form-w5 form-m" data-icon="&#xf0dc">
                  <select class="formelem" name="kat">
  <?php
      $eredmk = mysql_query("SELECT DISTINCT fneve FROM kategoria ORDER BY fneve") or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
      while($rekordk = mysql_fetch_object($eredmk)){
?>
                    <option <?php echo($_POST['kat']==$rekordk->fneve)?' selected':''?>><?php echo $rekordk->fneve?></option>
<?php
      }
?>
                  </select>
                </li>
                <li class="form-w5 form-m" data-icon="&#xf0dc">
                  <select class="formelem" name="kategoria">
  <?php
      $eredmk = mysql_query("SELECT * FROM kategoria WHERE fneve='".$_POST['kat']."' ORDER BY kneve") or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
      while($rekordk = mysql_fetch_object($eredmk)){
        if(!$rkparam) $rkparam=$rekordk->param;
        if($_POST['kategoria']==$rekordk->kategoria_id) $tlist=str_split($rekordk->param);
?>
                    <option value="<?php echo $rekordk->kategoria_id?>"<?php echo($_POST['kategoria']==$rekordk->kategoria_id)?' selected':''?>><?php echo $rekordk->kneve?></option>
<?php
      }
      if(!$tlist) $tlist=str_split($rkparam);
?>
                  </select>
                </li>
              </ul>
              <script type="text/javascript">
                var formkat = document.formhirdkarb.kat;
                var formkategoria = document.formhirdkarb.kategoria;
                function csere_hirdkarb(){
                  var xhr = new XMLHttpRequest();
                  var fd = new FormData(document.formhirdkarb);
                  xhr.open('POST','/ajax_formkiir_hirdkarb.php', true);
                  xhr.onreadystatechange = function(){
                    if(xhr.readyState == 4 && xhr.status == 200){
                      document.getElementById("formhirdkarbdiv").innerHTML = xhr.responseText;
                    }
                  };
                  xhr.send(fd);
                }
                formkat.addEventListener("change",function(){
                  var xhr = new XMLHttpRequest();
                  var fd = new FormData();
                  fd.append("kat", this.value);
                  xhr.open('POST','/ajax_formkiir_selectkat.php', true);
                  xhr.onreadystatechange = function(){
                    if(xhr.readyState == 4 && xhr.status == 200){
                      formkategoria.innerHTML = xhr.responseText;
                      csere_hirdkarb();
                    }
                  };
                  xhr.send(fd);
                });
                formkategoria.addEventListener("change",csere_hirdkarb);
              </script>
              <div id="formhirdkarbdiv">
                <div class="clear">
              
<?php
          if($tlist[0]>0){
?>
                  <ul class="form-text form-pb floatl">
                    <li class="form-fejlec form-w3">Alapterület</li>
                    <li class="form-w3 form-m" data-to="m2"><input class="formelem" type="text" name="teralap" value="<?php echo $_POST['teralap']?>"></li>
                  </ul>
<?php
          }
          if($tlist[1]>0){
?>
                  <ul class="form-text form-pb floatl">
                    <li class="form-fejlec form-w3">Telekterület</li>
                    <li class="form-w3 form-m" data-to="m2"><input class="formelem" type="text" name="tertelek" value="<?php echo $_POST['tertelek']?>"></li>
                  </ul>
<?php
          }
          if($tlist[2]>0 or $tlist[3]>0){
            if($tlist[3]>0){
?>
                  <ul class="form-text form-pb floatl">
                    <li class="form-fejlec form-w3">Belmagas</li>
                    <li class="form-w3 form-m" data-to="cm"><input class="formelem tooltip tt_focus" type="text" name="belmag" value="<?php echo $_POST['belmag']?>"><?php $doksi->doksi_mezo('hirdkarb','Ubelmag')?></li>
                  </ul>
<?php
            }
            if($tlist[2]>0){
?>
                  <ul class="form-text form-pb floatl">
                    <li class="form-fejlec form-w3">Terasz</li>
                    <li class="form-w3 form-m" data-to="m2"><input class="formelem tooltip tt_focus" type="text" name="terasz" value="<?php echo $_POST['terasz']?>"><?php $doksi->doksi_mezo('hirdkarb','Uterasz')?></li>
                  </ul>
<?php
            }
          }
          if($tlist[6]>0){
?>
                  <ul class="form-text form-pb floatl">
                    <li class="form-fejlec form-w3">Szoba</li>
                    <li class="form-w3 form-m"><input class="formelem" type="text" name="szoba" value="<?php echo $_POST['szoba']?>"></li>
                  </ul>
                  <ul class="form-text form-pb floatl">
                    <li class="form-fejlec form-w3">Félszoba</li>
                    <li class="form-w3 form-m"><input class="formelem tooltip tt_focus" type="text" name="szobafel" value="<?php echo $_POST['szobafel']?>"><?php $doksi->doksi_mezo('hirdkarb','Utszobafel')?></li>
                  </ul>
<?php
          }
          if($tlist[4]>0){
            if($tlist[4]>0){
?>
                  <ul class="form-text form-pb floatl">
                    <li class="form-fejlec form-w3">Szintek</li>
                    <li class="form-w3 form-m"><input class="formelem" type="text" name="szint" value="<?php echo $_POST['szint']?>"></li>
                  </ul>
<?php
            }
            if($tlist[5]>0){
?>
                  <ul class="form-select form-pb floatl">
                    <li class="form-fejlec form-w5">Emelet</li>
                    <li class="form-w5 form-m" data-icon="&#xf0dc">
                      <select class="formelem" name="emelet">
                        <option value="-1"<?php echo ($_POST['emelet']==-1)?' selected':''?>>alagsor
                        <option value="0"<?php echo ($_POST['emelet']==0)?' selected':''?>>földszint
                        <option value="-2"<?php echo ($_POST['emelet']==-2)?' selected':''?>>magasföldszint
                        <option value="1"<?php echo ($_POST['emelet']==1)?' selected':''?>>1
                        <option value="2"<?php echo ($_POST['emelet']==2)?' selected':''?>>2
                        <option value="3"<?php echo ($_POST['emelet']==3)?' selected':''?>>3
                        <option value="4"<?php echo ($_POST['emelet']==4)?' selected':''?>>4
                        <option value="5"<?php echo ($_POST['emelet']==5)?' selected':''?>>5
                        <option value="6"<?php echo ($_POST['emelet']==6)?' selected':''?>>6
                        <option value="7"<?php echo ($_POST['emelet']==7)?' selected':''?>>7
                        <option value="8"<?php echo ($_POST['emelet']==8)?' selected':''?>>8
                        <option value="9"<?php echo ($_POST['emelet']==9)?' selected':''?>>9
                        <option value="10"<?php echo ($_POST['emelet']==10)?' selected':''?>>10
                        <option value="11"<?php echo ($_POST['emelet']==11)?' selected':''?>>11-
                        <option value="-3"<?php echo ($_POST['emelet']==-3)?' selected':''?>>tetőtér
                      </select>
                    </li>
                  </ul>
<?php
            }
          }
?>
                </div>
                <div class="clear">
<?php
          if($tlist[10]>0){
?>
                  <ul class="form-select form-pb floatl">
                    <li class="form-fejlec form-w5">Állapot</li>
                    <li class="form-w5 form-m" data-icon="&#xf0dc">
                      <select class="formelem" name="allapot">
<?php
            foreach($listallapot as $value){
?>
                      <option value="<?php echo $value?>"<?php echo ($_POST['allapot']==$value)?' selected':''?>><?php echo $value?>
<?php
            }
?>
                      </select>
                    </li>
                  </ul>
<?php
          }
          if($tlist[11]>0){
?>
                  <ul class="form-select form-pb floatl">
                    <li class="form-fejlec form-w5">Kilátás</li>
                    <li class="form-w5 form-m" data-icon="&#xf0dc">
                      <select class="formelem" name="kilatas">
<?php
            foreach($listkilatas as $value){
?>
                        <option value="<?php echo $value?>"<?php echo ($_POST['kilatas']==$value)?' selected':''?>><?php echo $value?>
<?php
            }
?>
                      </select>
                    </li>
                  </ul>
<?php
          }
          if($tlist[8]>0){
?>
                  <ul class="form-select form-pb floatl">
                    <li class="form-fejlec form-w5">Tanusítvány</li>
                    <li class="form-w5 form-m" data-icon="&#xf0dc">
                      <select class="formelem" name="energtan">
                        <option value="-"<?php echo ($_POST['energtan']=='-')?' selected':''?>>nincs</option>
                        <option <?php echo ($_POST['energtan']=='A+')?' selected':''?>>A+</option>
                        <option <?php echo ($_POST['energtan']=='A')?' selected':''?>>A</option>
                        <option <?php echo ($_POST['energtan']=='B')?' selected':''?>>B</option>
                        <option <?php echo ($_POST['energtan']=='C')?' selected':''?>>C</option>
                        <option <?php echo ($_POST['energtan']=='D')?' selected':''?>>D</option>
                        <option <?php echo ($_POST['energtan']=='E')?' selected':''?>>E</option>
                        <option <?php echo ($_POST['energtan']=='F')?' selected':''?>>F</option>
                        <option <?php echo ($_POST['energtan']=='G')?' selected':''?>>G</option>
                        <option <?php echo ($_POST['energtan']=='H')?' selected':''?>>H</option>
                        <option <?php echo ($_POST['energtan']=='I')?' selected':''?>>I</option>
                      </select>
                    </li>
                  </ul>
<?php
          }
          if($tlist[12]>0){
?>
                  <ul class="form-select form-pb floatl">
                    <li class="form-fejlec form-w5">Fűtés</li>
                    <li class="form-w5 form-m" data-icon="&#xf0dc">
                      <select class="formelem" name="futes">
<?php
            foreach($listfutes as $value){
?>
                        <option value="<?php echo $value?>"<?php echo ($_POST['futes']==$value)?' selected':''?>><?php echo $value?>
<?php
            }
?>
                      </select>
                    </li>
                  </ul>
<?php
          }
          if($tlist[9]>0){
?>
                  <ul class="form-select form-pb floatl">
                    <li class="form-fejlec form-w5">Komfort</li>
                    <li class="form-w5 form-m" data-icon="&#xf0dc">
                      <select class="formelem" name="komfort">
<?php
            foreach($listkomfort as $value){
?>
                        <option value="<?php echo $value?>"<?php echo ($_POST['komfort']==$value)?' selected':''?>><?php echo $value?>
<?php
            }
?>
                      </select>
                    </li>
                  </ul>
<?php
          }
?>
                </div>
                <ul class="form-check form-v clear">
                  <li class="form-w5 form-m">
                    <input type="checkbox" id="csere" name="csere" value="I"<?php echo($_POST['csere']=='I')?' checked':''?>>
                    <label for="csere">Csere lehetséges</label>
                  </li>
                  <li class="form-w5 form-m">
                    <input type="checkbox" id="terhelt" name="terhelt" value="I"<?php echo($_POST['terhelt']=='I')?' checked':''?>>
                    <label for="terhelt">Terhelt az ingatlan</label>
                  </li>
<?php
          if($tlist[13]>0){
?>
                  <li class="form-w5 form-m">
                    <input type="checkbox" id="lift" name="lift" value="I"<?php echo($_POST['lift']=='I')?' checked':''?>>
                    <label for="lift">Lift van</label>
                  </li>
<?php
          }
?>
                </ul>
              </div>
              <ul class="form-area form-pb">
                <li class="form-fejlec form-w9">Leírás</li>
                <li class="form-w9 form-m"><textarea class="formelem" name="leiras"><?php echo $_POST['leiras']?></textarea></li>
<?php
          if($_SESSION[REGCEG]>0){
?>
                <li class="form-fejlec form-w9">Megjegyzés</li>
                <li class="form-w9 form-m"><textarea class="formelem tooltip tt_focus" name="egyeb"><?php echo $_POST['egyeb']?></textarea><?php $doksi->doksi_mezo('hirdkarb','Uegyeb')?></li>
<?php
          }
?>
              </ul>
              <input class="gomb" type="submit" name="gomb_szerk" value="Mentés" title="">
            </div>
<?php
          if($_GET['ingatlan_id']>0){
?>
            <div>
              <h3>Képek</h3>
              <input type="hidden" name="fotosor" value="<?php echo $_POST['fotosor']?>">
              <ul class="form-pb">
<?php
            $kids=explode(',',$_POST['fotosor']);
            if(count($kids)<20){
?>
                <li class="form-ddfile form-w9 form-m">
                  <span class="icon box__icon" data-icon="&#xf093;">max <?php echo (20-count($kids))?> db</span>
                  <input class="box__file" id="foto" type="file" name="foto[]" data-multiple-caption="{count} kiválasztott fájl" multiple>
                  <label for="foto"><strong>Válassz fájlt</strong><span class="box__dragndrop"> vagy húzd ide</span></label>
                </li>
                <li class="box__uploading">Feltöltés&hellip;</li>
		            <li class="box__success">Kész! <a href="#" class="box__restart" role="button">További fájlok?</a></li>
		            <li class="box__error">Hiba! <span></span>. <a href="#" class="box__restart" role="button">Próbáld újra!</a></li>
<?php
            }
?>
                <li id="kepablak">
<?php
            
            foreach($kids as $kid){
              if (!file_exists('kepek/'.$_GET['ingatlan_id'].'_'.$kid.'n.jpg')){
                if(file_exists('kepek/'.$_GET['ingatlan_id'].'_'.$kid.'imp.jpg')) meretezo_jpg('kepek/'.$_GET['ingatlan_id'].'_'.$kid.'imp.jpg',154,154,'kepek/'.$_GET['ingatlan_id'].'_'.$kid.'n.jpg',100,1,'z');
              }
              if (file_exists('kepek/'.$_GET['ingatlan_id'].'_'.$kid.'n.jpg')){
                $kep='kepek/'.$_GET['ingatlan_id'].'_'.$kid.'n.jpg';
?>
                  <div draggable="false">
                    <img data-kid="<?php echo $kid?>" src="/<?php echo $kep.'?'.md5(date("YmdHis",filemtime($kep)))?>" width="154">
                    <a class="icon" data-icon="&#xf044;" data-wincim="Ablak" href="/iframe_kepkarb.php?iid=<?php echo $_GET['ingatlan_id'].'&kid='.$kid?>"></a>
                  </div>
<?php
              }
            }
?>
                </li>
              </ul>
              <script>
                'use strict';
                ;(function(document, window, index){
                  var isAdvancedUpload = function(){ // drag & drop, formdata, filereader elérhető?
                    var div = document.createElement('div');
                    return ( ( 'draggable' in div ) || ( 'ondragstart' in div && 'ondrop' in div ) ) && 'FormData' in window && 'FileReader' in window;
                  }();
                  var 
                    fotosor  = document.forms['formhirdkarb'].fotosor,
                    span     = document.querySelector( '.form-ddfile .box__icon' ),
                    kepablak = document.getElementById('kepablak'),
                    forms    = document.querySelectorAll('form[name=formhirdkarb]');
                  Array.prototype.forEach.call(forms, function(form){
                    var 
                      input		    = document.getElementById('foto'),
                      label		    = input.nextSibling,
                      updb        = 0,
                      maxdb       = 20,
                      errorMsg	  = form.querySelector( '.box__error span' ),
                      restart		  = form.querySelectorAll( '.box__restart' ),
                      formddfile  = form.querySelector( '.form-ddfile' ),
                      upFiles     = false,
                      showFiles	  = function(fname){
                        label.textContent = updb > 1 ? ( input.getAttribute( 'data-multiple-caption' ) || '' ).replace( '{count}', updb ) : fname;
                      },
                      funUpload   = function(){
                        if(formddfile.classList.contains('is-uploading')) return false;
                        formddfile.classList.add('is-uploading');
                        formddfile.classList.remove('is-error');
                      
                        if(upFiles){
                          var 
                            tomb   = [],
                            kid    = 1,
                            maxkid = 0;
                          if(fotosor.value.length>0){
                            tomb = fotosor.value.split(",");
                            kid = tomb.length > 0 ? Math.max(...tomb) + 1 : 1;
                            maxdb -= tomb.length;
                          }
                          updb = (upFiles.length > maxdb)? maxdb : upFiles.length;
                          for (var i=kid; i<updb + kid; i++){
                            tomb.push(i);
                            maxkid = i;
                          }
                          fotosor.value = tomb.join();
                          showFiles(upFiles[0].name);
                          
                          var ajaxData = new FormData();
                          ajaxData.append('fotosor', fotosor.value);
                          ajaxData.append('id', <?php echo $_GET['ingatlan_id']?>);
                          var ajax = new XMLHttpRequest();
                          ajax.open('POST','/ajax_fotosor.php', true);
                          ajax.send(ajaxData);
                          Array.prototype.forEach.call(upFiles, function(file){
                            if(kid<=maxkid){
                              var img = document.createElement("img");
                              img.src = '/ajax-loader.gif';
                              img.width = 30;
                              var div = document.createElement("div");
                              div.setAttribute("style", "min-width: 32px");
                              div.appendChild(img);
                              kepablak.appendChild(div);
                              
                              var ajaxData = new FormData();
                              ajaxData.append('myFile', file);
                              ajaxData.append('id', <?php echo $_GET['ingatlan_id']?>);
                              ajaxData.append('kid', kid);
                              var ajax = new XMLHttpRequest();
                              ajax.open('POST','/ajax_kepek.php', true);
                              ajax.onload = function(){
                                formddfile.classList.remove('is-uploading');
                                if(ajax.status >= 200 && ajax.status < 400){
                                  var data = JSON.parse(ajax.responseText);
                                  
                                  img.src=data.src;
                                  img.width = 154;
                                  img.setAttribute("data-kid", kid);
                                  var alink = document.createElement("a");
                                  alink.href = '/iframe_kepkarb.php?iid=<?php echo $_GET["ingatlan_id"]?>&kid=' + kid;
                                  alink.setAttribute("data-icon", String.fromCodePoint(0xf044));
                                  alink.classList.add('icon');
                                  alink.addEventListener('click', openWin, false);
                                  div.removeAttribute("style");
                                  div.setAttribute('draggable', 'true');
                                  div.addEventListener('dragstart', handleDragStart, false);
                                  div.addEventListener('dragenter', handleDragEnter, false);
                                  div.addEventListener('dragover', handleDragOver, false);
                                  div.addEventListener('dragleave', handleDragLeave, false);
                                  div.addEventListener('drop', handleDrop, false);
                                  div.addEventListener('dragend', handleDragEnd, false);
                                  div.appendChild(alink);
                                  
                                  updb--;
                                  maxdb--;
                                  if(updb>0){
                                    showFiles('');
                                    span.textContent = 'max ' + maxdb + ' db';
                                  }else{
                                    label.textContent = '';
                                  }
                                  
                                  formddfile.classList.add(data.success == true ? 'is-success' : 'is-error');
                                  if(!data.success) errorMsg.textContent = data.error;
                                }else alert('Error. Please, contact the webmaster!');
                              };
                              ajax.onerror = function(){
                                formddfile.classList.remove('is-uploading');
                                alert( 'Error. Please, try again!' );
                              };
                              ajax.send(ajaxData);
                            }
                            kid++;
                          });
                        }
                    };
                    input.addEventListener('change', function(e){
                      upFiles = e.target.files;
                      funUpload();
                    });
                    if(isAdvancedUpload){
                      formddfile.classList.add('has-advanced-upload');
                      ['drag', 'dragstart', 'dragend', 'dragover', 'dragenter', 'dragleave', 'drop'].forEach(function(event){
                        formddfile.addEventListener(event,function(e){
                          e.preventDefault();
                          e.stopPropagation();
                        });
                      });
                      ['dragover', 'dragenter'].forEach(function(event){
                        formddfile.addEventListener(event, function(){
                          formddfile.classList.add('is-dragover');
                        });
                      });
                      ['dragleave', 'dragend', 'drop'].forEach(function(event){
                        formddfile.addEventListener(event, function(){
                          formddfile.classList.remove('is-dragover');
                        });
                      });
                      formddfile.addEventListener('drop',function(e){
                        upFiles = e.dataTransfer.files;
                        funUpload();
                      });
                    }
                    Array.prototype.forEach.call(restart, function(entry){
                      entry.addEventListener('click', function( e ){
                        e.preventDefault();
                        formddfile.classList.remove('is-error', 'is-success');
                        input.click();
                      });
                    });
                    input.addEventListener('focus', function(){input.classList.add('has-focus');});
                    input.addEventListener('blur', function(){input.classList.remove('has-focus');});
                  });
                  
                  // kép sorrend
                  
                  var cols = document.querySelectorAll('#kepablak>div');
                  var dragSrcEl = null;
                  
                  function handleDragStart(e){
                    dragSrcEl = this;
                    e.dataTransfer.effectAllowed = 'move';
                    e.dataTransfer.setData('text/html', this.innerHTML);
                    this.classList.add('moving');
                  }
                  function handleDragOver(e){
                    if (e.preventDefault){
                      e.preventDefault();
                    }
                    e.dataTransfer.dropEffect = 'move';
                    return false;
                  }
                  function handleDragEnter(e){
                    this.classList.add('over');
                  }
                  function handleDragLeave(e){
                    this.classList.remove('over');
                  }
                  function handleDrop(e){
                    if (e.stopPropagation){
                      e.stopPropagation();
                    }
                    if (dragSrcEl != this){
                      dragSrcEl.innerHTML = this.innerHTML;
                      this.innerHTML = e.dataTransfer.getData('text/html');
                    }
                    handleDragEnd();
                    return false;
                  }
                  function handleDragEnd(e){
                    fotosor.value = '';
                    [].forEach.call(cols, function (col){
                      col.classList.remove('over');
                      col.classList.remove('moving');
                      
                      if(fotosor.value) fotosor.value += ',';
                      fotosor.value += parseInt(col.querySelector('img').getAttribute('data-kid'));
                    });
                    var ajaxData = new FormData();
                    ajaxData.append('fotosor', fotosor.value);
                    ajaxData.append('id', <?php echo $_GET['ingatlan_id']?>);
                    var ajax = new XMLHttpRequest();
                    ajax.open('POST','/ajax_fotosor.php', true);
                    ajax.send(ajaxData);
                  }
                  [].forEach.call(cols, function(col){
                    col.setAttribute('draggable', 'true');
                    col.addEventListener('dragstart', handleDragStart, false);
                    col.addEventListener('dragenter', handleDragEnter, false);
                    col.addEventListener('dragover', handleDragOver, false);
                    col.addEventListener('dragleave', handleDragLeave, false);
                    col.addEventListener('drop', handleDrop, false);
                    col.addEventListener('dragend', handleDragEnd, false);
                  });
                  
                  // openwin
                  
                  function openWin(e){
                    var  cim = typeof this.getAttribute('data-wincim') !== 'undefined' ? this.getAttribute('data-wincim') : '',
                    closeWin = function(){
                      if(document.getElementById('openwin')){
                        document.getElementById('openwin').remove();
                      }
                    },
                        oWin = document.createElement('div'),
                    oWinHead = document.createElement('div'),
                oWinFrameDiv = document.createElement('div'),
                      oFrame = document.createElement('iframe');
                    closeWin(); // bezárás ha van nyitva
                    oWin.setAttribute('id','openwin');
                    document.body.appendChild(oWin);
                    oWinHead.innerHTML = '<strong class="icon" data-icon="&#xf057"></strong><i>' + cim + '</i>';
                    oWinHead.firstChild.addEventListener('click', closeWin);
                    oWin.appendChild(oWinHead);
                    oWin.appendChild(oWinFrameDiv);
                    oFrame.src = this.href;
                    oWinFrameDiv.appendChild(oFrame);
                    e.preventDefault();
                    return false;
                  }
                  
                  var openwinlinks = kepablak.querySelectorAll('a.icon');
                  Array.prototype.forEach.call(openwinlinks, function(openwinlink){
                    openwinlink.addEventListener('click', openWin, false);
                  });
                  
                }( document, window, 0 ));
              </script>
            </div>
            <div>
              <div class="hirdinfbox">
                <ul class="form-pb form-v">
                  <li class="form-w9 form-m">Rendelkezésre álló egyenleg: <strong><?php echo $_SESSION[REGKREDIT]?></strong> Ft</li>
                  <li class="form-w9"><a class="gomb" href="?b">Egyenleg feltöltés</a></li>
                </ul>
              </div>
              <hr>
              <h3>Szolgáltatások</h3>
<?php
/*
          if($_SESSION[REGCEG]==0){
?>
              <h4>Hirdetés megjelentetés</h4>
              <p>A feladott hirdetés megjelenítése a saját régiós domain alatt és az ingatlanom.hu alatt</p>
              <ul class="form-select form-pb">
                <li class="form-w9" data-icon="&#xf0dc">
                  <select class="formelem" name="hirdetnap">
                    <option value="30"<?php echo ($_POST['hirdetnap']==30)?' selected':''?>><?php echo $def->maganh?> Ft / 30 nap</option>
                    <option value="90"<?php echo ($_POST['hirdetnap']==90)?' selected':''?>><?php echo $def->magann?> Ft / 90 nap</option>
                    <option value="180"<?php echo ($_POST['hirdetnap']==180)?' selected':''?>><?php echo $def->maganf?> Ft / 180 nap</option>
                    <option value="365"<?php echo ($_POST['hirdetnap']==365)?' selected':''?>><?php echo $def->magane?> Ft / 365 nap</option>
                  </select>
                </li>
              </ul>
              <input class="gomb" type="submit" name="gomb_hirdet" value="Indulhat" title="">
              <hr>
<?php
          }
?>
              <h4>Cimke felirattal kiemelés</h4>
              <p>A hirdetésben elhelyezett címke feltünő és magához vonzza a figyelmet. A címke látható a találati listánál és a hírdetés adatlapjánál is.</p>
              <ul class="form-select form-pb">
                <li class="form-fejlec">Cimke szövege</li>
                <li class="form-w9" data-icon="&#xf0dc">
                  <select class="formelem" name="feliratmi">
<?php
        foreach($listfelirat as $value){
?>
                    <option value="<?php echo $value?>"<?php echo ($_POST['feliratmi']==$value)?' selected':''?>><?php echo $value?>
<?php
        }
?>
                  </select>
                </li>
                <li class="form-fejlec">Megjelenés ideje</li>
                <li class="form-w9" data-icon="&#xf0dc">
                  <select class="formelem" name="feliratnap">
                    <option value="1"<?php echo ($_POST['feliratnap']==1)?' selected':''?>><?php echo $def->cimke1?> Ft / 1 nap</option>
                    <option value="7"<?php echo ($_POST['feliratnap']==7)?' selected':''?>><?php echo $def->cimke7?> Ft / 7 nap</option>
                  </select>
                </li>
              </ul>
              <input class="gomb" type="submit" name="gomb_cime" value="Indulhat" title="">
              <hr>
              <h4>Szines háttérrel kiemelés</h4>
              <p>A találati listán feltünő háttérrel jelenik meg a hirdetés.</p>
              <ul class="form-select form-pb">
                <li class="form-fejlec">Megjelenés ideje</li>
                <li class="form-w9" data-icon="&#xf0dc">
                  <select class="formelem" name="szinesnap">
                    <option value="1"<?php echo ($_POST['szinesnap']==1)?' selected':''?>><?php echo $def->szinh1?> Ft / 1 nap</option>
                    <option value="7"<?php echo ($_POST['szinesnap']==7)?' selected':''?>><?php echo $def->szinh7?> Ft / 7 nap</option>
                  </select>
                </li>
              </ul>
              <input class="gomb" type="submit" name="gomb_szines" value="Indulhat" title="">
              <hr>
              <h4>Találati listán előre helyezés</h4>
              <p>A találati listán a lista elején látható a hirdetés.</p>
              <ul class="form-select form-pb">
                <li class="form-fejlec">Megjelenés ideje</li>
                <li class="form-w9" data-icon="&#xf0dc">
                  <select class="formelem" name="listanap">
                    <option value="1"<?php echo ($_POST['listanap']==1)?' selected':''?>><?php echo $def->liste1?> Ft / 1 nap</option>
                    <option value="7"<?php echo ($_POST['listanap']==7)?' selected':''?>><?php echo $def->liste7?> Ft / 7 nap</option>
                  </select>
                </li>
              </ul>
              <input class="gomb" type="submit" name="gomb_lista" value="Indulhat" title="">
              <hr>
              <h4>Főoldali képes kiemelés</h4>
              <p>A főoldalon kiemelt hirdetések között az első képpel jelenik meg a hirdetés.</p>
              <ul class="form-select form-pb">
                <li class="form-fejlec">Megjelenés ideje</li>
                <li class="form-w9" data-icon="&#xf0dc">
                  <select class="formelem" name="ablakfonap">
                    <option value="1"<?php echo ($_POST['ablakfonap']==1)?' selected':''?>><?php echo $def->kiemf1?> Ft / 1 nap</option>
                    <option value="7"<?php echo ($_POST['ablakfonap']==7)?' selected':''?>><?php echo $def->kiemf7?> Ft / 7 nap</option>
                  </select>
                </li>
              </ul>
              <input class="gomb" type="submit" name="gomb_ablakfo" value="Indulhat" title="">
              <hr>
              <h4>Találati oldalon képes kiemelés</h4>
              <p>A találati oldalon kiemelt hirdetések között az első képpel jelenik meg a hirdetés.</p>
              <ul class="form-select form-pb">
                <li class="form-fejlec">Megjelenés ideje</li>
                <li class="form-w9" data-icon="&#xf0dc">
                  <select class="formelem" name="ablaklinap">
                    <option value="1"<?php echo ($_POST['ablaklinap']==1)?' selected':''?>><?php echo $def->kieml1?> Ft / 1 nap</option>
                    <option value="7"<?php echo ($_POST['ablaklinap']==7)?' selected':''?>><?php echo $def->kieml7?> Ft / 7 nap</option>
                  </select>
                </li>
              </ul>
              <input class="gomb" type="submit" name="gomb_ablakli" value="Indulhat" title="">
            </div>
<?php
*/
          }
?>
          </div>
        </form>
      </div>
<?php
        }
      break;
  default:
    $SQL = 'SELECT ingatlan.*,hol_telepules.telepules FROM ingatlan,hol_telepules
             WHERE ingatlan.telepules_id=hol_telepules.telepules_id AND '.(($_SESSION[REGCEG]>0)?'iroda_id="'.$_SESSION[REGCEG].'"':'reg_id="'.$_SESSION[REG].'"')
          .' ORDER BY ingatlan_id DESC';
    $eredm = mysql_query($SQL)or die($SQL." Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
    $akting=mysql_num_rows($eredm);
    if($_SESSION[REGCEG]>0){
      $regi = mysql_fetch_object(mysql_query('SELECT * FROM iroda WHERE iroda_id="'.$_SESSION[REGCEG].'" LIMIT 1'));
      if($regi->aktivkelt>date('Y-m-d H:i:s')){ // fizetett az iroda?
        if($_POST['gomb_import']){
          if($_FILES['xmlfile']['tmp_name']){
            $keptipus=explode('.',$_FILES['xmlfile']['name']);
            $keptipus[1]=strtolower($keptipus[1]);
            if('xml' == $keptipus[1]) $dir=$_FILES['xmlfile']['tmp_name'];
          }else $dir='http://'.$regi->importdir.'/ingatlanom.xml';
          if($dir){
            $xml = simplexml_load_file($dir);
            $ingatlanidvan = []; // ami van azt össze gyűjteni, ami nincs törölni
            foreach($xml as $ingatlan){
              $ingatlanidvan[] = $ingatlan->id;
              $eredming = mysql_query("SELECT * FROM ingatlan WHERE iroda_id=".$_SESSION[REGCEG]." AND import_id=".$ingatlan->id." LIMIT 1")
                or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
              if(!$eredming or mysql_num_rows($eredming)==0){ // ha még nincs fenn
                $hiba=0;
                $SQL="INSERT INTO ingatlan(reg_id,iroda_id,import_id,telepules_id,kategoria,kategoriafo,kategoriaal,ajanlat,leiras,ar,tertelek,
                                           teralap,belmag,terasz,szint,emelet,szoba,szobafel,energtan,csere,terhelt,lift,refszam,erttelefon,
                                           ertemail,komfort,allapot,kilatas,hol_pontos,hol_pontosk,futes,foto,fotosor,modokelt,rogzkelt)
                           VALUES(";
                $SQL.="'".$_SESSION[REG]."','".$_SESSION[REGCEG]."'";
                if($ingatlan->id>0) $SQL.=",'".$ingatlan->id."'"; else $hiba=1;
                if($ingatlan->telepulesid>0) $SQL.=",'".$ingatlan->telepulesid."'"; else $hiba=1;
                if($hiba==0 and $ingatlan->kategoria>0){
                  $eredmk = mysql_query("SELECT * FROM kategoria WHERE kategoria_id=".$ingatlan->kategoria." LIMIT 1")
                    or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
                  if($rekordk = mysql_fetch_object($eredmk)){
                    $SQL.=",'".$ingatlan->kategoria."','".$rekordk->fneve."','".$rekordk->kneve."'";
                  }else $hiba=1;
                }else $hiba=1;
                if($hiba==0 and ($ingatlan->ajanlat=='E' or $ingatlan->ajanlat=='K')) $SQL.=",'".$ingatlan->ajanlat."'"; else $hiba=1;
                if($hiba==0 and $ingatlan->leiras) $SQL.=",'".$ingatlan->leiras."'"; else $hiba=1;
                if($hiba==0 and $ingatlan->fotosor==''){
                  if(count($ingatlan->kep)>0){
                    $i=1;
                    foreach($ingatlan->kep as $kep){
                      $ingatlan->fotosor.=($i==1)?$i:','.$i;
                      $i++;
                    }
                  }
                }
                if($hiba==0) $SQL.=",'".(($ingatlan->ar)?$ingatlan->ar:'0')."','".(($ingatlan->tertelek)?$ingatlan->tertelek:'0')."'
                                    ,'".(($ingatlan->teralap)?$ingatlan->teralap:'0')."','".(($ingatlan->belmag)?$ingatlan->belmag:'0')."'
                                    ,'".(($ingatlan->terasz)?$ingatlan->terasz:'0')."','".(($ingatlan->szint)?$ingatlan->szint:'0')."'
                                    ,'".(($ingatlan->emelet)?$ingatlan->emelet:'0')."','".(($ingatlan->szoba)?$ingatlan->szoba:'0')."'
                                    ,'".(($ingatlan->szobafel)?$ingatlan->szobafel:'0')."','".(($ingatlan->energtan)?$ingatlan->energtan:'0')."'
                                    ,'".(($ingatlan->csere)?$ingatlan->csere:'N')."'
                                    ,'".(($ingatlan->terhelt)?$ingatlan->terhelt:'N')."','".(($ingatlan->lift)?$ingatlan->lift:'N')."'
                                    ,'".(($ingatlan->refszam)?$ingatlan->refszam:'')."','".(($ingatlan->erttelefon)?$ingatlan->erttelefon:'')."'
                                    ,'".(($ingatlan->ertemail)?$ingatlan->ertemail:'')."','".(($ingatlan->komfort)?$ingatlan->komfort:'')."'
                                    ,'".(($ingatlan->allapot)?$ingatlan->allapot:'')."','".(($ingatlan->kilatas)?$ingatlan->kilatas:'')."'
                                    ,'".(($ingatlan->hol_pontos)?$ingatlan->hol_pontos:'')."','".(($ingatlan->hol_pontosk)?$ingatlan->hol_pontosk:'')."'
                                    ,'".(($ingatlan->futes)?$ingatlan->futes:'')."','".(($ingatlan->foto)?$ingatlan->foto:'')."'
                                    ,'".(($ingatlan->fotosor)?$ingatlan->fotosor:'')."'";
                $SQL.=",'".date("Y-m-d H:i:s")."','".date("Y-m-d H:i:s")."')";
                if($hiba==0){
                  mysql_query($SQL)or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . $SQL . " / " . mysql_error());
                  $ingatlan_id=mysql_insert_id();
                  if(count($ingatlan->kep)>0){
                    $fotosor=explode(',',$ingatlan->fotosor);
                    $i=0;
                    foreach($ingatlan->kep as $kep){
                      $kepuj = 'kepek/'.$ingatlan_id.'_'.$fotosor[$i];
                      $i++;
                      if(!copy($kep,$kepuj.'imp.jpg')) echo '<br>Hiás kép: '.$kepuj.' / '.$kep;
                    }
                  }
                }else{
                  echo '<br>Hibás XML adat: '.$SQL; //hibás XML rekord
                }
              }else{ // ha már van
                if(count($ingatlan->kep)>0){
                  if($rekord = mysql_fetch_object($eredming)){
                    if($ingatlan->fotosor==''){
                      $i=1;
                      foreach($ingatlan->kep as $kep){
                        $ingatlan->fotosor.=($i==1)?$i:','.$i;
                        $i++;
                      }
                      mysql_query("UPDATE ingatlan SET fotosor='".$ingatlan->fotosor."' WHERE ingatlan_id=".$rekord->ingatlan_id);
                    }
                    if($ingatlan->fotosor!=$rekord->fotosor){
                      mysql_query("UPDATE ingatlan SET fotosor='".$ingatlan->fotosor."' WHERE ingatlan_id=".$rekord->ingatlan_id);
                    }
                    $fotosor=explode(',',$ingatlan->fotosor);
                    if($fotosor[0])
                      $i=0;
                      foreach($ingatlan->kep as $kep){
                        $kepuj = 'kepek/'.$rekord->ingatlan_id.'_'.$fotosor[$i];
                        $i++;
                        if(!file_exists($kepuj.'imp.jpg')){
                          if(!copy($kep,$kepuj.'imp.jpg')) echo '<br>Hiás kép: '.$kepuj.' / '.$kep;
                        }
                      }
                  }
                }
              }
            }
            if(count($ingatlanidvan)>0){ // kitörölni ami már nincs
              $eredmk = mysql_query("SELECT * FROM ingatlan WHERE iroda_id=".$_SESSION[REGCEG])or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . mysql_error());
              while($rekordk=mysql_fetch_object($eredmk)){
                if(!in_array($rekordk->import_id, $ingatlanidvan)){ // ami nincs annak törlése
                  echo 'Törlés: '.$rekordk->import_id.'<br>';
                  mysql_query('DELETE FROM ingatlan WHERE ingatlan_id='.$rekordk->ingatlan_id)or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . $SQL . " / " . mysql_error());
                  foreach (glob('kepek/'.$rekordk->ingatlan_id.'*.jpg') as $filename){
                    unlink($filename);
                  }
                }
              }
            }
          }
        }
        if($_GET['delingatlan_id']){
          foreach (glob('kepek/'.$_GET['delingatlan_id'].'*.jpg') as $filename){
            unlink($filename);
          }
          mysql_query('DELETE FROM ingatlan WHERE ingatlan_id='.$_GET['delingatlan_id'])or die("Nem sikerült a lekérdezés, MySQL hibaüzenet:" . $SQL . " / " . mysql_error());
        }
?>
      <div class="hirdinfbox">
        <form method="post" action="/hirdkarb" enctype="multipart/form-data">
<?php
        if(!$regi->importdir){
?>
          <ul class="form-pb">
            <li class="form-fejlec">XML import fájl feltöltése</li>
            <li class="form-w9 form-m">
              <input class="form-file" id="xmlfile" type="file" name="xmlfile" data-multiple="{count} xml file kiválasztva">
              <label for="xmlfile"><span></span><strong class="icon" data-icon="&#xf093;">XML file kiválasztása</strong></label>
            </li>
            <script type="text/javascript" src="/form_filebox.js"></script>
          </ul>
<?php
        }
?>
          <input class="gomb" type="submit" name="gomb_import" value="Import" title="">
        </form>
      </div>
      <div class="hirdinfbox">
        <ul class="form-pb form-v">
          <li class="form-w7 form-m">Iroda aktíválva eddig:</li>
          <li class="form-w3"><strong title="<?php echo substr($regi->aktivkelt,11)?>"><?php echo substr($regi->aktivkelt,2,8)?></strong></li>
          <li class="form-w7 form-m">Feltölthető ingatlanok száma:</li>
          <li class="form-w3 form-m" style="text-align:right"><?php echo $regi->maxing?> db</li>
          <li class="form-w7 form-m">Feltöltött ingatlanok száma:</li>
          <li class="form-w3 form-m" style="text-align:right"><?php echo $akting?> db</li>
          <li class="form-w7 form-m">Rendelkezésre álló hely:</li>
          <li class="form-w3 form-m" style="text-align:right"><strong><?php echo $regi->maxing-$akting?></strong> db</li>
<?php
        if($regi->maxing!=0){
?>
          <li class="form-w9 form-m">Kiegészítő csomag a feltölthető ingatlanok korlátjának növelésére: <?php echo $def->irodab.' Ft/30 nap'?></li>
<?php
          if($_SESSION[REGKREDIT]>=$def->irodab){
?>
          <li class="form-w9"><a class="gomb" href="?b">Kérem a szolgáltatást</a></li>
<?php
          }else{
?>
          <li class="form-w9 fontos">Kevés az egyenleg!</li>
<?php
          }
        }
?>
        </ul>
      </div>
<?php
      }else{
        $regs = mysql_fetch_object(mysql_query('SELECT * FROM szerzodes WHERE mirol=3 AND reg_id="'.$_SESSION[REG].'" LIMIT 1'));
?>
      <div class="hirdinfbox">
        <ul class="form-pb form-v">
          <li class="form-w9">Iroda csomagja lejárt! Az ingatlanok jelenleg nem jelennek meg a felhasznállóinknak. Kérem fizessen elő!</li>
<?php
        if($regi->maxing>0){
?>
          <li class="form-w7 form-m">Feltölthető ingatlanok száma:</li>
          <li class="form-w3" style="text-align:right"><?php echo $regi->maxing?> db</li>
<?php
          if($akting>0){
?>
          <li class="form-w7 form-m">Feltöltött ingatlanok száma:</li>
          <li class="form-w3" style="text-align:right"><?php echo $akting?> db</li>
<?php
          }
        }
?>
        </ul>
      </div>
      <div class="hirdinfbox">
        <ul class="form-pb form-v">
          <li class="form-w9 form-m">Az iroda csomag díja: <?php echo $regs->dij.' Ft/'.$regs->idoszak.' nap'?></li>
          <li class="form-w9"><a class="gomb" href="?d">Kérem a díjbekérőt</a></li>
        </ul>
      </div>
<?php
      }    
?>
      <div class="hirdinfbox">
        <ul class="form-pb form-v">
          <li class="form-w9 form-m">Rendelkezésre álló egyenleg: <strong><?php echo $_SESSION[REGKREDIT]?></strong> Ft</li>
          <li class="form-w9"><a class="gomb" href="?b">Egyenleg feltöltés</a></li>
        </ul>
      </div>
<?php
    } // iroda vége
?>
      <table class="clearfloat ingkarb">
        <thead>
          <tr>
            <th>#</th>
<?php
    if($_SESSION[REGCEG]>0){
?>
            <th>Azonosító</th>
<?php
    }
?>
            <th>Tipus</th>
            <th>Hol</th>
            <th>Ár</th>
<?php
    if($_SESSION[REGCEG]==0){
?>
            <th>Megjelenés</th>
<?php
    }
?>
            <th>Cimke</th>
            <th>Háttér</th>
            <th>Előre</th>
            <th>Ki fő</th>
            <th>Ki li</th>
            <th><?php echo($_SESSION[REGCEG]<1 or $regi->aktivkelt>date('Y-m-d H:i:s'))?'<a class="gomb" href="hirdkarb/szerk">Új hirdetés</a>':''?></th>
          </tr>
        </thead>
        <tbody>
<?php
    while ($rekord = mysql_fetch_object($eredm)){
?>
          <tr>
            <td><?php echo $rekord->ingatlan_id?></td>
<?php
      if($_SESSION[REGCEG]>0){
?>
            <td<?php echo($rekord->muti=='R')?' style="background:orangered"':''?>><?php echo $rekord->refszam?></td>
<?php
      }
?>
            <td><?php echo $rekord->kategoriafo.' / '.$rekord->kategoriaal?></td>
            <td><?php echo $rekord->telepules?> <?php echo $rekord->hol_pontos?></td>
            <td style="text-align:right"><?php echo number_format($rekord->ar,0,',',' ')?></td>
<?php
      if($_SESSION[REGCEG]==0){
?>
            <td><?php echo($rekord->aktivkelt!='0000-00-00')?substr($rekord->aktivkelt,2,14):''?></td>
<?php
      }
?>
            <td><?php echo($rekord->feliratkelt!='0000-00-00 00:00:00')?substr($rekord->feliratkelt,2,14):''?></td>
            <td><?php echo($rekord->szineskelt!='0000-00-00 00:00:00')?substr($rekord->szineskelt,2,14):''?></td>
            <td><?php echo($rekord->listakelt!='0000-00-00 00:00:00')?substr($rekord->listakelt,2,14):''?></td>
            <td><?php echo($rekord->ablakfokelt!='0000-00-00 00:00:00')?substr($rekord->ablakfokelt,2,14):''?></td>
            <td><?php echo($rekord->ablaklikelt!='0000-00-00 00:00:00')?substr($rekord->ablaklikelt,2,14):''?></td>
            <td>
              <a class="icon" data-icon="&#xf044" href="hirdkarb/szerk?ingatlan_id=<?php echo $rekord->ingatlan_id?>" title='Szerkesztés'></a>
              <a class="icon" data-icon="&#xf014" href="hirdkarb?delingatlan_id=<?php echo $rekord->ingatlan_id?>" title='Törlés' onclick="return confirm('Biztos törlöd?')"></a>
            </td>
          </tr>
<?php
    }
?>
        </tbody>
      </table>
<?php
}
?>