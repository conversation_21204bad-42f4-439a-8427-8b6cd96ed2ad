<header>
  <h6><PERSON>j <PERSON><PERSON></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_price">
    <ul class="formbox">
    <li class="form col0">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?>">
        <label><PERSON><PERSON> megne<PERSON></label>
      </li>
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="accommodation_unit_type_id" placeholder="">
          <?php foreach( response::$vw->view->accommodation_unit_types as $accommodation_unit_type ){ ?>
          <option value="<?= $accommodation_unit_type->id ?>"<?= ( $_POST['accommodation_unit_type_id'] == $accommodation_unit_type->id )?' selected' : '' ?>><?= $accommodation_unit_type->name ?></option>
          <?php } ?>
        </select>
        <label>Lakóegység típus</label>
      </li>
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="period_id" placeholder="">
          <?php foreach( response::$vw->view->periods as $period ){ ?>
          <option value="<?= $period->id ?>"<?= ( $_POST['period_id'] == $period->id )?' selected' : '' ?>><?= $period->name ?></option>
          <?php } ?>
        </select>
        <label>Időszak</label>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Új ár mentése">Létrehoz</button>
</footer>