<header>
  <h6>Ár</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_price">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <input type="hidden" name="prices" value='<?= json_encode( $_POST['prices'] ) ?>'>
    <ul class="formbox uladd">
      <li class="form col0">
        <input type="text" name="name" placeholder="" value="<?= $_POST['name'] ?>">
        <label>Ár megne<PERSON></label>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="accommodation_unit_type_id" placeholder="">
          <?php foreach( response::$vw->view->accommodation_unit_types as $accommodation_unit_type ){ ?>
          <option value="<?= $accommodation_unit_type->id ?>"<?= ( $_POST['accommodation_unit_type_id'] == $accommodation_unit_type->id )?' selected' : '' ?>><?= $accommodation_unit_type->name ?></option>
          <?php } ?>
        </select>
        <label>Lakóegység típus</label>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="period_id" placeholder="">
          <?php foreach( response::$vw->view->periods as $period ){ ?>
          <option value="<?= $period->id ?>"<?= ( $_POST['period_id'] == $period->id )?' selected' : '' ?>><?= $period->name ?></option>
          <?php } ?>
        </select>
        <label>Időszak</label>
      </li>
      <li class="form col5 toggle-buttons">
        <input type="hidden" name="weekly_priced_days" value="<?= $_POST['weekly_priced_days'] ?? '1111111' ?>">
        <div>
          <span class="soon" data-index="0">H</span>
          <span class="soon" data-index="1">K</span>
          <span class="soon" data-index="2">Sze</span>
          <span class="soon" data-index="3">Cs</span>
          <span class="soon" data-index="4">P</span>
          <span class="soon" data-index="5">Szo</span>
          <span class="soon" data-index="6">V</span>
        </div>
        <label>Mely napokon</label>
      </li>
      <li class="form col5 toggle-buttons">
        <input type="hidden" name="weekly_arrival_days" value="<?= $_POST['weekly_arrival_days'] ?? '1111111' ?>">
        <div>
          <span class="soon" data-index="0">H</span>
          <span class="soon" data-index="1">K</span>
          <span class="soon" data-index="2">Sze</span>
          <span class="soon" data-index="3">Cs</span>
          <span class="soon" data-index="4">P</span>
          <span class="soon" data-index="5">Szo</span>
          <span class="soon" data-index="6">V</span>
        </div>
        <label>Érkezési napok</label>
      </li>
      <li class="form col5 bgonto" data-to="nap">
        <input type="text" name="locked_nights" placeholder="" value="<?= $_POST['locked_nights'] ?>">
        <label>+nap</label>
      </li>
      <li class="form col5 bgonto" data-to="nap">
        <input type="text" name="min_booking_period" placeholder="" value="<?= $_POST['min_booking_period'] ?>">
        <label>Előfoglalás</label>
      </li>
      <li class="form col5 bgonto" data-to="éj">
        <input type="text" name="min_days" placeholder="" value="<?= $_POST['min_days'] ?>">
        <label>Min.éj</label>
      </li>
      <li class="form col5 bgonto" data-to="éj">
        <input type="text" name="max_days" placeholder="" value="<?= $_POST['max_days'] ?>">
        <label>Max.éj</label>
      </li>
      <?php if( count( $_POST['prices'] )){ ?>
      <?php   foreach( $_POST['prices'] as $key => $price ){ ?>
      <li class="form col5" style="--toicon:var(--icon-trash-empty)">
        <input type="text" placeholder="" value="<?= $price[0].' fő '.$price[1].' Ft/éj' ?>" disabled>
        <label>Ár</label>
        <div class="del-price" data-key="<?= $key ?>" title="Ár törlése"></div>
      </li>
      <?php } } ?>
    </ul>
    <ul class="formbox" style="margin-top: clamp(.25rem, 4%, 1rem)">  
      <li class="form col4 bgonto" data-to="fő">
        <input type="text" id="guest" placeholder="" value="">
        <label>Fő</label>
      </li>
      <li class="form col4 bgonto" data-to="Ft">
        <input type="text" id="price" placeholder="" value="">
        <label>Ár/éj</label>
      </li>
      <li class="form col2">
        <button class="add-price" style="--icon:var(--icon-plus-circled)" title="Ár hozzáadása"></button>
      </li> 
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <button class="callback" name="btn_modositas" title="Ár mentése">Módosít</button>
</footer>