<header>
  <h6>
    Foglalás számlái -
    <span style="color:var(--danger-color)">
      <?= http::$route[3] ?? '' ?><?= ( response::$vw->view->foglalas->lemondva ?? 0 )? ' LEMONDVA' : '' ?>
    </span>
  </h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_foglalas_invoice">
    <ul class="formbox">
      <li class="form col4">
        <input type="text" placeholder="" value="<?= response::$vw->view->foglalas->kapcsolattarto ?? '' ?>" disabled>
        <label for="kapcsolattarto">Kapcsolattartó</label>
      </li>
      <li class="form col3">
        <input type="text" placeholder="" value="<?= response::$vw->view->foglalas->fizetendo ?? '' ?>" disabled>
        <label>Összeg HUF</label>
      </li>
      <li class="form col3">
        <input type="text" name="eloleg" placeholder="" value="<?= response::$vw->view->foglalas->eloleg ?? '' ?>" disabled>
        <label>Előleg HUF</label>
      </li>

      <li class="form col0">
        <input type="text" name="inv_name" placeholder="" value="<?= $_POST['inv_name'] ?? ''?>">
        <label>Számlázási név</label>
      </li>
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="inv_countryCode" placeholder="">
          <?php foreach( response::$vw->view->countries as $country ){ ?>
          <option value="<?= $country->code2 ?>"<?= ( $_POST['inv_countryCode'] == $country->code2 )?' selected':'' ?>><?= $country->name ?></option>
          <?php } ?>
        </select>
        <label>Ország</label>
      </li>
      <li class="form col3">
        <input type="text" name="inv_zip" placeholder="" value="<?= $_POST['inv_zip'] ?? ''?>">
        <label>Irányítószám</label>
      </li>
      <li class="form col7">
        <input type="text" name="inv_city" placeholder="" value="<?= $_POST['inv_city'] ?? ''?>">
        <label>Település</label>
      </li>
      <li class="form col0">
        <input type="text" name="inv_address" placeholder="" value="<?= $_POST['inv_address'] ?? ''?>">
        <label>Utca, házszám</label>
      </li>
      <li class="form col5">
        <input type="text" name="inv_taxNumber" value="<?= $_POST['inv_taxNumber'] ?? '' ?>" placeholder="">
        <label>Adószám</label>
      </li>
      <li class="form col5">
        <input type="text" name="inv_communityVatNumber" value="<?= $_POST['inv_communityVatNumber'] ?? '' ?>" placeholder="">
        <label>Közösségi adószám</label>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="callback" name="btn_mentes" title="Új foglalás adatainak mentése">Mentés</button>
  <button class="close">Kilép</button>
</footer>