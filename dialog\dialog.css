@charset "UTF-8";
.dialog-background{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
}
dialog{
  top: 0;
	left: 0;
	right: 0;
	bottom: 0;
  position: fixed;
  z-index: calc(infinity);
  &.max-screen{
    position: fixed;
    top: 0;
    left: 0;
    max-width: 100%;
    max-height: 100%;
    min-width: 100%;
    height: 100%;
    overflow: auto;
  }
  &[data-dialog^="status-"]{
    position: absolute;
    & > div{
      & > section{
        display: flex;
        justify-content: center;
        align-items: center;
        padding: .5rem 0;
      }
    }
  }
}
html:has(dialog[open][data-dialog="modal"]){ overflow: hidden }