  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Üzenetek</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új üzenet</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable( 
          'messages',
          [ 'name' => ['th' => 'Tárgy'],
            'sending_time' => ['th' => 'Küldés ideje'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés::id',
                'trash-empty:event:Törlés:/messages/template/del/:id:confirm:where:db'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script src="/shared/js/jodit/jodit.min.js"></script>
  <link href="/shared/js/jodit/jodit.min.css" rel="stylesheet">
  <script type="module">
    import { $, datatable, dialog, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd, inputJE
      switch( ifunction ){
        case 'messages_edit':
          const editor = new Jodit( document.getElementById( 'joditeditor' ), { toolbarButtonSize: 'small' } )
          return {
            'click': [['.send', 'messages_edit_send', id]],
            'change': [
              ['[name="booking_id"]', 'messages_edit_booking', id],
              ['[name="template_id"]', 'messages_edit_template', id],
            ]
          }
        break;
        case 'messages_edit_send':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_message' ) )
          ajax( {
            url: '/modal/messagesend',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Az üzenet elküldve', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a küldés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'messages_edit_booking':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_message' ) )
          ajax( {
            url: '/modal/messageeditbooking',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                $( 'input[name="to"]' ).value = response.data.booking.email
                if( response.data.subject )
                  $( 'input[name="subject"]' ).value = response.data.subject
                if( response.data.body ){
                  $( '.jodit-wysiwyg > p' ).innerHTML = response.data.body
                  $( 'textarea[name="body"]' ).value = response.data.body
                }
                $( '.send' ).disabled = ( response.data.booking.email != '' )? true : false
              }else
                dialog( {type: 'status:error', content: 'Nincs megfelelő foglalás!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'messages_edit_template':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_message' ) )
          ajax( {
            url: '/modal/messageedittemplate',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok ){
                if( response.data.subject )
                  $( 'input[name="subject"]' ).value = response.data.subject
                if( response.data.body )
                  $( '.jodit-wysiwyg > p' ).innerHTML = response.data.body
                  $( 'textarea[name="body"]' ).value = response.data.body
              }else
                dialog( {type: 'status:error', content: 'Nincs megfelelő foglalás!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'messages_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_message' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/message',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Az üzenet módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break;
        case 'messages_edit_close':
          location.replace( '/message/messages' )
        break;
        case 'messages_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( {
            url: '/modal/messagedel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/message/messages' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a sablon!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break;
      }
    }

    window.addEventListener('DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új üzenet',
          label: 'Tárgy'
        },
        eventManagerFunctions,
        modalEvents: ['change']
      } )
    } )
  </script>