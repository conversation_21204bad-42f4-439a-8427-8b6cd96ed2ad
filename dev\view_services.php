  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Szolgáltatások</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új szolgáltatás</a>
          <?php //} ?>
        </span>
      </header>
      <article>
        <?= table::datatable( 
          'services',
          [ 'allapotstr' => ['th' => 'Állapot', 'status' => 'allapot'],
            'name' => ['th' => 'Megnevezés'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés::id',
                'trash-empty:event:Törlés:/service/del/:id:confirm:where:db'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { datatable, dialog, upload, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let
        ifunction = data[1],
        id = data[2] || null,
        fd,
        selects
      switch( ifunction ){
        case 'services_edit':
          upload()
          return {
            'click': [
              ['.select-add', 'services_edit_select_add', id],
              ['.select-del', 'services_edit_select_del', id]
            ]
          }
        break    
        case 'services_edit_select_add':
          event.preventDefault()
          const
            selectsBox = document.querySelector( 'div.selects-box' ),
            text = document.querySelector( 'input[name="select_text"]' ).value,
            price = document.querySelector( 'input[name="select_price"]' ).value
          let ul = document.createElement( 'ul' )
          selects = document.querySelector( 'input[name="selects"]' ).value ? JSON.parse( document.querySelector( 'input[name="selects"]' ).value ) : []
          ul.classList.add( 'formbox' )
          ul.innerHTML = 
            '<li class="form col5">'+
            '  <input type="text" placeholder="" value="'+ text +'" disabled>'+
            '  <label>Szöveg</label>'+
            '</li>'+
            '<li class="form col2">'+
            '  <input type="text" placeholder="" value="'+ price +'" disabled>'+
            '  <label>Ár</label>'+
            '</li>'+
            '<li class="form col3">'+
            '  <button class="select-del" data-selectid="'+ selects.length  +'" name="btn_del" title="Választható tétel törlése">Töröl</button>'+
            '</li>'
          selectsBox.append( ul )
          selects.push( {'text': text, 'price': price} )
          document.querySelector( 'input[name="selects"]' ).value = JSON.stringify( selects )
          document.querySelector( 'input[name="select_text"]' ).value = ''
          document.querySelector( 'input[name="select_price"]' ).value = ''
        break
        case 'services_edit_select_del':
          event.preventDefault()
          const
            selectid = event.target.dataset.selectid
          selects = document.querySelector( 'input[name="selects"]' ).value ? JSON.parse( document.querySelector( 'input[name="selects"]' ).value ) : []
          event.target.closest( 'ul.formbox' ).remove()
          selects.splice( selectid, 1 )
          document.querySelector( 'input[name="selects"]' ).value = JSON.stringify( selects )
        break
        case 'services_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_service' ) )
          ajax( {
            url: '/modal/service',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'A szolgáltatás módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'services_edit_close':
          location.replace( '/services' )
        break
        case 'services_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( {
            url: '/modal/servicedel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/services' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a szolgáltatás!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új szolgáltatás',
          label: 'Megnevezés'
        },
        eventManagerFunctions
      } )
    } )
  </script>