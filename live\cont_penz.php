<?php
$penz = db::list_penz();
$foglalasok = db::list_foglalasokPenz();
$list = [];
$ofizetendoS = $ofizetendoL = $ofizetettS = $ofizetettL = 0;
foreach( $foglalasok as $foglalas ){
  if( $foglalas['erkezes'] >= $_POST['kezd'] and $foglalas['erkezes'] <= $_POST['vege'] ){
    $f = ['foglalva' => substr( $foglalas['foglalva'], 0, 11 ), 'lemondva' => ($foglalas['lemondva'][0])?1:0, 'mikor' => $foglalas['erkezes'], 'foglalas_id' => $foglalas['foglalas_id']];
    if( date( 'Y-m-d', strtotime( substr( $foglalas['foglalva'], 0, 11 ).' +'.$_POST['elnap'].'day' ) ) >= date('Y-m-d') )
      if( $foglalas['eloleg'] > $foglalas['fizetve'] )
        $color = 'warning';
      elseif( $foglalas['fizetendo'] < $foglalas['fizetve'] )
        $color = 'info';
      elseif( $foglalas['fizetendo'] == $foglalas['fizetve'] )
        $color = 'success';
      elseif( $foglalas['eloleg'] )
        $color = 'success';
      else
        $color = 'warning';
    elseif( $foglalas['erkezes'] > date('Y-m-d') )
      if( $foglalas['eloleg'] > $foglalas['fizetve'] )
        $color = 'danger';
      elseif( $foglalas['fizetendo'] > $foglalas['fizetve'] )
        $color = 'warning';
      elseif( $foglalas['fizetendo'] < $foglalas['fizetve'] )
        $color = 'info';
      else
        $color = 'success';
    elseif( $foglalas['fizetendo'] > $foglalas['fizetve'] )
      $color = 'danger';
    elseif( $foglalas['fizetendo'] < $foglalas['fizetve'] )
      $color = 'info';
    else
      $color = 'success';
    $f['color'] = $color;
    if( date( 'Y-m-d', strtotime( $foglalas['erkezes'].' -'.$_POST['lenap'].'day' ) ) < date('Y-m-d') ){
      $f['fizetendoS'] = $foglalas['fizetendo'];
      $f['fizetettS'] = $foglalas['fizetve'] ?? 0;
      $ofizetendoS += $f['fizetendoS'];
      $ofizetettS += $f['fizetettS'];
    }else{
      $f['fizetendoL'] = $foglalas['fizetendo'];
      $f['fizetettL'] = $foglalas['fizetve'] ?? 0;
      $ofizetendoL += $f['fizetendoL'];
      $ofizetettL += $f['fizetettL'];
    }
    $list[] = $f;
  }
}

$slist = [];
foreach( $list as $key => $rec )
  $slist[$key] = $rec['mikor'];
arsort($slist);
foreach( $slist as $key => $rec )
  $slist[$key] = $list[$key];

response::add( 'view', 'penzlist', $slist );
response::add( 'view', 'penzossz', ['ofizetendoS' => $ofizetendoS, 'ofizetendoL' => $ofizetendoL, 'ofizetettS' => $ofizetettS, 'ofizetettL' => $ofizetettL] );