  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Lakóegységek<br><?= ( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 )? response::$vw->view->accommodationunittype->name : '' ?></h5>
        <span>
          <?php if( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 ){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új lakóegység</a>
          <?php } ?>
          </span>
      </header>
      <article>
        <?= table::datatable(
          'accommodation_units',
          [ 'status_str' => ['th' => 'Állapot', 'status' => 'status'],
            'name' => ['th' => 'Lakóegység'],
            '*1' => [
              'th' => ( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 )? '<a href="/accommodationunittypes">Vissza</a>' : '',
              'icons' => ( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 )?
               [
                'edit:modal:Szerkesztés',
                /*'picture:modal:Képek',*/
                'exchange:modal:Szinkronok',
                'trash-empty:event:Törlés:::confirm:where:is_trash_empty'
              ] :
              [
                'edit:modal:Szerkesztés',
                /*'picture:modal:Képek',*/
                'exchange:modal:Szinkronok'
              ]
            ]
          ],
          null,
          null,
          'accommodation_unit_type_id='.http::$route[1]
        ) ?>
      </article>
    </div>
  </section>
  <?php if( setup::is_right( 12 )){ ?>
  <script src="/shared/js/jodit/jodit.min.js" type="text/javascript"></script>
  <link href="/shared/js/jodit/jodit.min.css" rel="stylesheet">
  <?php } ?>
  <script type="module">
    import { $, datatable, renderTbody, dialog, ajax, validation } from '/shared/js/spritzer/index.js'
    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd,
          napok = ['H','K','Sa','C','P','So','V']
      switch( ifunction ){
        case 'accommodation_units_edit':
          <?php if( setup::is_right( 12 )){ ?>
          var editor1 = new Jodit(document.getElementById('joditeditor1', { "toolbarButtonSize": "tiny" } ) )
          var editor2 = new Jodit(document.getElementById('joditeditor2', { "toolbarButtonSize": "tiny" } ) )
          <?php } ?>
          validation('dialog#accommodation_units-edit')
        break
        case 'accommodation_units_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'accommodation_units_edit' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/accommodation_unit_save',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Adatok módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'accommodation_units_edit_close':
          location.replace( '/accommodationunits/<?= http::$route[1] ?>' )
        break
        case 'accommodation_units_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( {
            url: '/modal/accommodation_units_del',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/accommodationunits/'+ <?= http::$route[1] ?> )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a lakóegység!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        /*
        case 'accommodation_units_picture':
          var isAdvancedUpload = function(){
                var div = document.createElement( 'div' )
                return ( ( 'draggable' in div ) || ( 'ondragstart' in div && 'ondrop' in div ) ) && 'FormData' in window && 'FileReader' in window
              }(),
                      fotosor  = document.forms['form_lakoegysegkep_modosit'].fotosor,
                      span     = document.querySelector( '.form-ddfile .box__icon' ),
                      kepablak = document.getElementById( 'kepablak' ),
                      forms    = document.querySelectorAll( 'form[name=form_lakoegysegkep_modosit]' )
          Array.prototype.forEach.call( forms, function( form ){
            var input       = document.getElementById('foto'),
                label       = input.nextSibling,
                updb        = 0,
                maxdb       = 20,
                errorMsg    = form.querySelector( '.box__error span' ),
                restart     = form.querySelectorAll( '.box__restart' ),
                formddfile  = form.querySelector( '.form-ddfile' ),
                upFiles     = false,
                showFiles   = function(fname){
                  label.textContent = updb > 1 ? ( input.getAttribute( 'data-multiple-caption' ) || '' ).replace( '{count}', updb ) : fname
                },
                funUpload   = function(){
                  if( formddfile.classList.contains( 'is-uploading' ) ) return false
                  formddfile.classList.add( 'is-uploading' )
                  formddfile.classList.remove( 'is-error' )
                  if( upFiles ){
                    var tomb   = [],
                        kid    = 1,
                        maxkid = 0
                    if( fotosor.value.length > 0 ){
                      tomb = fotosor.value.split( "," )
                      kid = tomb.length > 0 ? Math.max( ...tomb ) + 1 : 1
                      maxdb -= tomb.length
                    }
                    updb = ( upFiles.length > maxdb )? maxdb : upFiles.length
                    for( var i=kid; i<updb + kid; i++ ){
                      tomb.push( i )
                      maxkid = i
                    }
                    fotosor.value = tomb.join()
                    showFiles( upFiles[0].name )
                    var ajaxData = new FormData(),
                            ajax = new XMLHttpRequest()
                    ajaxData.append( 'fotosor', fotosor.value )
                    ajaxData.append( 'id', <?= $_POST['id']?> )
                    ajax.open( 'POST', '/ajax_fotosor.php', true )
                    ajax.send( ajaxData )
                    Array.prototype.forEach.call( upFiles, function( file ){
                      if( kid<=maxkid ){
                        var img = document.createElement( 'img' )
                        img.src = '/ajax-loader.gif'
                        img.width = 30
                        var div = document.createElement( 'div' )
                        div.setAttribute( 'style', 'min-width: 32px' )
                        div.appendChild( img )
                        kepablak.appendChild( div )
                        var ajaxData = new FormData(),
                                ajax = new XMLHttpRequest()
                        ajaxData.append( 'myFile', file )
                        ajaxData.append( 'id', <?= $_POST['id']?> )
                        ajaxData.append( 'kid', kid )
                        ajax.open( 'POST', '/ajax_kepek.php', true )
                        ajax.onload = function(){
                          formddfile.classList.remove( 'is-uploading' )
                          if( ajax.status >= 200 && ajax.status < 400 ){
                            var data = JSON.parse( ajax.responseText )
                            img.src=data.src
                            img.width = 154
                            img.setAttribute( 'data-kid', kid )
                            var alink = document.createElement( 'a' )
                            alink.href = '/iframe_kepkarb.php?iid=<?= $_POST['id']?>&kid=' + kid
                            alink.setAttribute( 'style', '--icon:var(--icon-trash-empty)' )
                            alink.addEventListener( 'click', openWin, false )
                            div.removeAttribute( 'style' )
                            div.setAttribute( 'draggable', 'true' )
                            div.addEventListener( 'dragstart', handleDragStart, false )
                            div.addEventListener( 'dragenter', handleDragEnter, false )
                            div.addEventListener( 'dragover', handleDragOver, false )
                            div.addEventListener( 'dragleave', handleDragLeave, false )
                            div.addEventListener( 'drop', handleDrop, false )
                            div.addEventListener( 'dragend', handleDragEnd, false )
                            div.appendChild( alink )
                            updb--
                            maxdb--
                            if( updb>0 ){
                              showFiles( '' )
                              span.textContent = 'max ' + maxdb + ' db'
                            }else
                              label.textContent = ''
                            formddfile.classList.add( data.success == true ? 'is-success' : 'is-error' )
                            if(!data.success) errorMsg.textContent = data.error
                          }else alert( 'Error. Please, contact the webmaster!' )
                        }
                        ajax.onerror = function(){
                          formddfile.classList.remove( 'is-uploading' )
                          alert( 'Error. Please, try again!' )
                        }
                        ajax.send( ajaxData )
                      }
                      kid++
                    } )
                  }
                }
                input.addEventListener( 'change', function( e ){
                  upFiles = e.target.files
                  funUpload()
                })
            if( isAdvancedUpload ){
              formddfile.classList.add( 'has-advanced-upload' )
              ['drag', 'dragstart', 'dragend', 'dragover', 'dragenter', 'dragleave', 'drop'].forEach( function( event ){
                formddfile.addEventListener( event, function( e ){
                  e.preventDefault()
                  e.stopPropagation()
                } )
              } )
              ['dragover', 'dragenter'].forEach( function( event ){
                formddfile.addEventListener( event, function(){
                  formddfile.classList.add( 'is-dragover' )
                } )
              } )
              ['dragleave', 'dragend', 'drop'].forEach( function( event ){
                formddfile.addEventListener( event, function(){
                  formddfile.classList.remove( 'is-dragover' )
                } )
              } )
              formddfile.addEventListener( 'drop',function( e ){
                upFiles = e.dataTransfer.files
                funUpload()
              } )
            }
            Array.prototype.forEach.call( restart, function( entry ){
              entry.addEventListener( 'click', function( e ){
                e.preventDefault()
                formddfile.classList.remove( 'is-error', 'is-success' )
                input.click()
              } )
            } )
            input.addEventListener( 'focus', function(){ input.classList.add( 'has-focus' ) } )
            input.addEventListener( 'blur', function(){ input.classList.remove( 'has-focus' ) } )
          } )
      
          // kép sorrend
          var cols = document.querySelectorAll( '#kepablak>div' ),
              dragSrcEl = null
          
          function handleDragStart( e ){
            dragSrcEl = this
            e.dataTransfer.effectAllowed = 'move'
            e.dataTransfer.setData( 'text/html', this.innerHTML )
            this.classList.add( 'moving' )
          }
          function handleDragOver( e ){
            if( e.preventDefault )
              e.preventDefault()
            e.dataTransfer.dropEffect = 'move'
            return false
          }
          function handleDragEnter( e ){
            this.classList.add('over')
          }
          function handleDragLeave( e ){
            this.classList.remove( 'over' )
          }
          function handleDrop( e ){
            if( e.stopPropagation )
              e.stopPropagation()
            if( dragSrcEl != this ){
              dragSrcEl.innerHTML = this.innerHTML
              this.innerHTML = e.dataTransfer.getData( 'text/html' )
            }
            handleDragEnd()
            return false
          }
          function handleDragEnd( e ){
            fotosor.value = ''
            Array.prototype.forEach.call( cols, function ( col ){
              col.classList.remove( 'over' )
              col.classList.remove( 'moving' )
              if(fotosor.value) fotosor.value += ','
              fotosor.value += parseInt( col.querySelector( 'img' ).getAttribute( 'data-kid' ) )
            } )
            var ajaxData = new FormData(),
                    ajax = new XMLHttpRequest()
            ajaxData.append( 'fotosor', fotosor.value )
            ajaxData.append( 'id', <?= $_POST['id']?> )
            ajax.open( 'POST', '/ajax_fotosor.php', true )
            ajax.send( ajaxData )
          }
          Array.prototype.forEach.call(cols, function(col){
            col.setAttribute('draggable', 'true')
            col.addEventListener('dragstart', handleDragStart, false)
            col.addEventListener('dragenter', handleDragEnter, false)
            col.addEventListener('dragover', handleDragOver, false)
            col.addEventListener('dragleave', handleDragLeave, false)
            col.addEventListener('drop', handleDrop, false)
            col.addEventListener('dragend', handleDragEnd, false)
          })
          
          // openwin
          function openWin(e){
            var cim = typeof this.getAttribute('data-wincim') !== 'undefined' ? this.getAttribute('data-wincim') : '',
                closeWin = function(){
                  if(document.getElementById('openwin')){
                    document.getElementById('openwin').remove()
                  }
                },
                oWin = document.createElement('div'),
                oWinHead = document.createElement('div'),
                oWinFrameDiv = document.createElement('div'),
                oFrame = document.createElement( 'iframe' )
            closeWin(); // bezárás ha van nyitva
            oWin.setAttribute('id','openwin');
            document.body.appendChild(oWin);
            oWinHead.innerHTML = '<strong style="--icon:var(--icon-picture)"></strong><i>' + cim + '</i>';
            oWinHead.firstChild.addEventListener('click', closeWin);
            oWin.appendChild(oWinHead);
            oWin.appendChild(oWinFrameDiv);
            oFrame.src = this.href;
            oWinFrameDiv.appendChild(oFrame);
            e.preventDefault();
            return false;
          }
          
          var openwinlinks = kepablak.querySelectorAll('a.icon'); // TODO class megszünt cserélni
          Array.prototype.forEach.call(openwinlinks, function(openwinlink){
            openwinlink.addEventListener('click', openWin, false);
          });
        break
        case 'accommodation_units_picture_close':
          location.replace( '/accommodation_units/<?= http::$route[1] ?>' )
        break
        */
        case 'accommodation_units_exchange': // betöltés után ez fut le
          return {
            submit: {
              add_channel: ['[name="form_add_channel"]', 'lakoegyseglist_exchange_add_channel', id],
              save_channel: ['[name="form_edit_channel"]', 'lakoegyseglist_exchange_save_channel', id]
            },
            click: {
              del_channel: ['.btn.del_channel', 'lakoegyseglist_exchange_del_channel', id],
              send_update: ['.btn.send', 'lakoegyseglist_exchange_send_channel', id],
              receive_update: ['.btn.receive', 'lakoegyseglist_exchange_receive_channel', id]
            }
          }
        break
        case 'accommodation_units_exchange_add_channel':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_add_channel' ) )
          fd.append( 'lakoegyseg_id', id )
          fd.append( 'btn_add_channel', true )
          ajax( {
            url: '/modal/szinkronnaptarcreate',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                mdbRebuild( 'lakoegyseglist-exchange' )
              else
                mdb( {is_not_modal: true, width: '200px', margin_top_x: 2, time_delay: 3000, title: "Hiba", content: "Sikertelen a mentés"} )
            },
            fail: ( err ) => {
              mdb( {is_not_modal: true, width: '200px', time_delay: 3000, title: "Hiba!", content: "Sikertelen művelet"} )
            }
          } )
        break
        case 'accommodation_units_exchange_save_channel':
          event.preventDefault()
          fd = new FormData( event.target )
          ajax( {
            url: '/modal/szinkronnaptarsave',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                mdb( {is_not_modal: true, width: '200px', margin_top_x: 2, time_delay: 3000, title: "Üzenet", content: "A fogadás link módosítva"} )
              else
                mdb( {is_not_modal: true, width: '200px', margin_top_x: 2, time_delay: 3000, title: "Hiba", content: "Sikertelen a mentés"} )
            },
            fail: ( err ) => {
              mdb( {is_not_modal: true, width: '200px', time_delay: 3000, title: "Hiba!", content: "Sikertelen művelet"} )
            }
          } )
        break
        case 'accommodation_units_exchange_del_channel':
          fd = new FormData()
          fd.append( 'lakoegyseg_id', id )
          fd.append( 'szinkron_id', event.target.dataset.szinkronnaptarid )
          ajax( { url: '/modal/szinkronnaptardel',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      mdbRebuild( 'lakoegyseglist-exchange' )
                    }else
                      mdb( {is_not_modal: true, width: '200px', margin_top_x: 2, time_delay: 3000, title: "Hiba", content: "Sikertelen a törlés"} )
                  },
                  fail: ( err ) => {
                    mdb( {is_not_modal: true, width: '200px', time_delay: 3000, title: "Hiba!", content: "Sikertelen művelet"} )
                  }
          } )
        break
        case 'accommodation_units_exchange_send_channel':
          // TODO funkció kell
        break
        case 'accommodation_units_exchange_receive_channel':
          // TODO funkció kell
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        <?php if( setup::$company->configuration->supplierInfo->accommodation_units_number > 1 ){ ?>
        create: {
          title: 'Új lakóegység',
          label: 'Megnevezés',
          fields: [{'accommodation_unit_type_id' : <?= http::$route[1] ?>}]
        },
        <?php } ?>
        eventManagerFunctions
      } )
    } )
  </script>