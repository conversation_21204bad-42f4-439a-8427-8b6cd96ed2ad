<?php
require_once './autoload.php';
if( $partners = db::list_invoice_nav() )
  foreach( $partners as $partner ){
    if( $config = db::get( 'config', $partner['id'] ?? 0 ) ){
      $config['main_settings'] = json_decode( $config['main_settings'], true );
      $config['price_settings'] = json_decode( $config['price_settings'], true );
      $config['integration_settings'] = json_decode( $config['integration_settings'], true );
      $config['admin_settings'] = json_decode( $config['admin_settings'], true );
      $config['detailedAddress'] = json_decode( $config['detailedAddress'], true );
      $config['shop_settings'] = json_decode( $config['shop_settings'], true );
    }
    $_SESSION["SHOPID"] = $partner['id'];
    if( $invoices = db::list_invoices_isnav( $partner['id'] ) )
      foreach( $invoices as $invoice ){
        if( $invoice['status_nav'] < 2 ){
          $invoice['customer'] = json_decode( $invoice['customer'] ?? "[]", true );
          $invoice['supplier'] = json_decode( $invoice['supplier'] ?? "[]", true );
          $invoice['invoice_number'] = ['prefix' => $invoice['nprefix'],
                                        'created' => $invoice['ncreated'],
                                        'id' => $invoice['nid']];
          unset( $invoice['nprefix'] );
          unset( $invoice['ncreated'] );
          unset( $invoice['nid'] );

          invoice_nav::get_invoice_compilation( $invoice, $partner['id'] );
          include 'confignavinvoice.php';
          try{
            $navconfig = new NavOnlineInvoice\Config( $apiUrl, $userData, $softwareData );
            $reporter = new NavOnlineInvoice\Reporter( $navconfig );
            $invoiceXml = simplexml_load_file( 'upload/'.$partner['id'].'/invoice_'.$invoice['id'].'.xml' );
            $transactionId = $reporter->manageInvoice($invoiceXml);
            db::save( 'invoices', [['status_nav', 2, 'i']], ['id', $invoice['id'], 'i'] );
            db::save( 'invoice_transactions', [['invoice_id', $invoice['id'], 'i'],
                                              ['tax_office', 'NAV'],
                                              ['transaction', $transactionId]] );
          }catch( Exception $ex ){
            echo 'Hiba: '.$partner['id'].' | '.$invoice['id'].' | '. get_class( $ex ). ": " . $ex->getMessage().'<br>';
          }
        }elseif( $invoice['status_nav'] == 2 ){
          if( $transaction = db::get( 'invoice_transactions', 0, ' WHERE invoice_id='.$invoice['id'] ) ){
            include 'confignavinvoice.php';
            try{
              $navconfig = new NavOnlineInvoice\Config( $apiUrl, $userData, $softwareData );
              $reporter = new NavOnlineInvoice\Reporter( $navconfig );
              $xml = $reporter->queryTransactionStatus( $transaction['transaction'] );
              // RECEIVED PROCESSING SAVED DONE ABORTED
              switch( $xml->processingResults->processingResult->invoiceStatus ){
                case 'DONE':
                  echo $xml->processingResults->processingResult->invoiceStatus;
                  db::save( 'invoices', [['status_nav', [0,0,3,0,5,6,7][$invoice['type']], 'i']], ['id', $invoice['id'], 'i'] );
                break;
                case 'ABORTED':
                  print_r($xml->processingResults->processingResult);
                  db::save( 'invoices', [['status_nav', 3, 'i']], ['id', $invoice['id'], 'i'] );
                break;
              }
            }catch( Exception $ex ){
              echo 'Hiba: '.$partner['id'].' | '.$invoice['id'].' | '. get_class( $ex ). ": " . $ex->getMessage().'<br>';
            }
          }
        }
      }
  }
