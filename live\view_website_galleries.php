  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5><PERSON><PERSON><PERSON><PERSON><PERSON></h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új galéria</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable( 
          'galleries',
          [ 'allapotstr' => ['th' => 'Állapot', 'status' => 'signs[0]'],
            'name' => ['th' => 'Megnevezés'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés::id',
                'trash-empty:event:Törlés:/website/gallery/del/:id:confirm:where:db'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { datatable, dialog, library, upload, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd
      switch( ifunction ){
        case 'galleries_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_gallery' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/gallery',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'A galéria módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'galleries_edit_close':
          location.replace( '/website/galleries' )
        break
        case 'galleries_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( { 
            url: '/modal/gallerydel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/website/galleries' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a galéria!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'galleries_edit':
          library()
          upload()
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új galéria',
          label: 'Megnevezés'
        },
        eventManagerFunctions
      } )
    } )
  </script>