<?php
// <PERSON>ript to import email addresses from CSV and check/save to database
const CLASSVERSIONS = [
    'errors'      => '1.4',
    'model_basic' => '1.0',
    'mysql'       => '2.2'
  ];
  const DATABASENAME = 'szallasportal';
  require_once 'shared/class/autoload.php';
  errors::start( 37 );
// Check if the CSV file exists
$csvFile = 'szallasadok_email1.csv';
if (!file_exists($csvFile)) {
    die("Error: File $csvFile not found.\n");
}

// Open the CSV file
$file = fopen($csvFile, 'r');
if (!$file) {
    die("Error: Unable to open file $csvFile.\n");
}

// Process each line in the CSV
while (($line = fgets($file)) !== false) {
    // Clean the email (remove whitespace, newlines, etc.)
    $email = trim($line);
    
    // Skip empty lines
    if (empty($email)) {
        continue;
    }
    
    // Check if email exists in the database
    $user = db::get('shared.users', 'email="'.$email.'"');
    var_dump($user);
    echo '<br><br>';
    if ($user === null) {
        echo 'UJ<br>';
        // Email doesn't exist, add it to the database
        $id = db::save('shared.users', [['email', $email]]);
        $pid = db::save('shared.user_projects', [['user_id', $id, 'i'], ['project_id', 37, 'i']]);
        db::save('tren.subscribers', [['user_id', $id, 'i']]);
    } else {
        echo 'MEGLEVO<br>';
        // Email already exists, output it
        echo $user['email'].' '.$user['name'].' '.$user['first_name'].' '.$user['modified'].'<br>';
    }
}

// Close the file
fclose($file);

echo "Import process completed.\n";