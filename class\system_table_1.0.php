<?php
/**
 * Datatable táblá<PERSON><PERSON><PERSON><PERSON>
 * 
 * @method `list_body();` tbody r<PERSON><PERSON>t<PERSON>a`
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2023, T<PERSON>cz<PERSON> Róbert
 * 
 * @version 1.1.0
 * @since 1.1.0 2024.01.28
 * @since 1.0.0 2023.08.02
 */

class table{

  private static function _requestStatus( $code ){
    $status = [
      200 => 'OK',
      401 => 'Unauthorized',
      403 => 'Forbidden',
      404 => 'Not Found',
      405 => 'Method Not Allowed',
      406 => 'Not Acceptable',
      500 => 'Internal Server Error'
    ];
    return $status[$code ?? 500] ?? $status[500];
  }
  public static function datatableApi( $action, $parameters ){
    $parameters['where'] = null;
    if( !( $_SESSION[$parameters['datatable']] ?? 0 )){
      $status = 404;
      $json = ['message' => 'Nincs ilyen adattábla!'];
    }else{
      setcookie( $parameters['datatable'], json_encode( $parameters ), 0, '/');
      $_SESSION[$parameters['datatable']]['where'] = $parameters['where'] ?? $_SESSION[$parameters['datatable']]['where'] ?? null;
      $_SESSION[$parameters['datatable']]['order'] = $parameters['order'] ?? $_SESSION[$parameters['datatable']]['order'] ?? null;
      $_SESSION[$parameters['datatable']]['limit'] = $parameters['limit'] ?? $_SESSION[$parameters['datatable']]['limit'] ?? 10;
      $_SESSION[$parameters['datatable']]['activePage'] = $parameters['activePage'] ?? $_SESSION[$parameters['datatable']]['activePage'] ?? 1;
      switch( $action ){
        case 'render_tbody':
          $tbody = $pagination = '';
          $tbody = self::list_tbody( [
            'table' => $parameters['datatable'],
            'head' => $_SESSION[$parameters['datatable']]['head'],
            'check' => $_SESSION[$parameters['datatable']]['check'] ?? null,
            'where' => $parameters['where'] ?? $_SESSION[$parameters['datatable']]['where'] ?? null,
            'order' => $parameters['order'] ?? $_SESSION[$parameters['datatable']]['order'] ?? null,
            'limit' => $parameters['limit'] ?? $_SESSION[$parameters['datatable']]['limit'] ?? 10,
            'activePage' => $parameters['activePage'] ?? $_SESSION[$parameters['datatable']]['activePage'] ?? 1
          ] );
          $pagination = table::make_pagination( $parameters['datatable'] );
          $json = ['tbody' => $tbody, 'pagination' => $pagination];
          $status = 200;
        break;
        case 'insert_tr':
          require_once './datatable.php';
          $tr = '';
          if( $row = call_user_func( 'table_get', $parameters ) )
            $tr = self::make_tr( $row, $_SESSION[$parameters['datatable']]['head'], $_SESSION[$parameters['datatable']]['check'] ?? null );
          $json = ['tr' => $tr];
          $status = 200;
        break;
        default:
          $status = 406;
          $json = ['message' => 'Nem megfelelő kérés!'];
      }
    }
    header( 'HTTP/1.1 ' . $status . ' ' . self::_requestStatus( $status ) );
    //header( 'Access-Control-Allow-Origin: *' );
    //header( 'Cache-Control: no-cache, must-revalidate' );
    header( 'Content-Type: application/json;charset=utf-8' );
    echo json_encode( $json );
    //echo json_encode( $json, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES );
    exit;
  }
  
  public static function datatable( $table, $head, $controls = null, $pagination = null, $where = null, $order = null ){
    if( !( $_SESSION[$table] ?? 0 ) ){
      $_SESSION[$table] = [
        'piece' => 0,
        'head' => $head,
        'controls' => $controls,
        'pagination' => $pagination,
        'where' => $where,
        'order' => $order,
        'limit' => 10,
        'activePage' => 1
      ];
    }
    if( $_COOKIE[$table] ?? 0 ){
      $cookie = json_decode( $_COOKIE[$table] );
      $_SESSION[$table]['activePage'] = $cookie->activePage ?? $_SESSION[$table]['activePage'];
      $_SESSION[$table]['limit'] = $cookie->limit ?? $_SESSION[$table]['limit'];
      $_SESSION[$table]['order'] = $cookie->order ?? $_SESSION[$table]['order'];
      $_SESSION[$table]['where'] = $cookie->where ?? $_SESSION[$table]['where'];
    }else{
      /*
      setcookie( $table, json_encode( [
        'where' => $_SESSION[$table]['where'] ?? '',
        'order' => $_SESSION[$table]['order'] ?? '',
        'limit' => $_SESSION[$table]['limit'] ?? 10,
        'activePage' => $_SESSION[$table]['activePage'] ?? 1
      ] ) );
      */
    }
    $html = '<div data-datatable="'.$table.'">';
    if( $controls ?? 0 ){
      $html.= '  <div class="controls">';
      // group-operation

      // filter
     
      // export
      // import
      foreach( $controls as $control => $value ){
        switch( $control ){
          case 'search':
            $html.= '<form class="search formbox"><div class="form"><input type="text" name="'.$table.'_search" placeholder=""><label>Keres</label></div></form>';
          break;
          case 'create':
            $html.= '<a class="btn create" style="--icon:var(--icon-plus-circled)"> '.$value.'</a>';
          break;
        }
      }
      $html.= '  </div>';
    }
    $html.= '  <div class="tablescroll">';
    $html.= '  <table>';
    $check = null;
    $html.= self::make_thead( $table, $head, $controls, $check );
    $html.= '    <tbody>';
    $html.= self::list_tbody( [
      'table' => $table,
      'head' => $head,
      'check' => $check,
      'where' => $_SESSION[$table]['where'] ?? null,
      'order' => $_SESSION[$table]['order'] ?? null,
      'limit' => $_SESSION[$table]['limit'] ?? 10,
      'activePage' => $_SESSION[$table]['activePage'] ?? 1
    ] );
    $html.=    '</tbody>';
    $html.= '  </table>';
    $html.= '  </div>';
    if( $pagination ?? 0 ){
      $html.= '    <div class="pagination">';
      $html.= self::make_pagination( $table );
      $html.= '    </div>';
    }
    $html.= '</div>';
    return $html;
  }
/**
 * pagination összeállítása
 *
 * @param string $table   Adattábla neve
 * @param string $where   Szűrési feltételek, ha nincs null
 * @return string         html string
 */
  public static function make_pagination( $table, $activePage = null ){
    $pages = ceil( $_SESSION[$table]['piece'] / $_SESSION[$table]['limit'] );
    $_SESSION[$table]['activePage'] = $activePage ?? $_SESSION[$table]['activePage'];
    $html = '      <ul>';
    $html.= '        <li>'.$_SESSION[$table]['piece'].' találat</li>';
    $html.= '        <li>'.(( $_SESSION[$table]['limit'] ?? 0 )? (( $_SESSION[$table]['activePage'] - 1 ) * $_SESSION[$table]['limit'] + 1 ) : 1 ).' - '.
                           (( $_SESSION[$table]['limit'] ?? 0 )? ((( $_SESSION[$table]['activePage'] - 1 ) * $_SESSION[$table]['limit'] + $_SESSION[$table]['limit'] > $_SESSION[$table]['piece'] )? $_SESSION[$table]['piece'] : ( $_SESSION[$table]['activePage'] - 1 ) * $_SESSION[$table]['limit'] + $_SESSION[$table]['limit'] ) : $_SESSION[$table]['piece'] ).'</li>';
    $html.= '      </ul>';
    if( $_SESSION[$table]['limit'] ?? 0 ){
      $html.= '      <ul>';
      $html.= '        <li class="limit">';
      $html.= '          <select name="limit">';
      $html.= '            <option'.( ( $_SESSION[$table]['limit'] == 10 )? ' selected' : '' ).'>10</option>';
      $html.= '            <option'.( ( $_SESSION[$table]['limit'] == 20 )? ' selected' : '' ).'>20</option>';
      $html.= '            <option'.( ( $_SESSION[$table]['limit'] == 50 )? ' selected' : '' ).'>50</option>';
      $html.= '          </select>';
      $html.= '        </li>';
      if( $_SESSION[$table]['activePage'] > 1 )
        $html.= '        <li class="prev"><a><</a></li>';
      if( $_SESSION[$table]['activePage'] > 3 and $pages > 3 )
        $html .= '<li style="margin-right:1rem"><a style="display:inline-block">1</a>...</li>';
      if( $_SESSION[$table]['activePage'] - 2 >= 1 )
        $html .= '<li><a>'.( $_SESSION[$table]['activePage'] - 2 ).'</a></li>';
      if( $_SESSION[$table]['activePage'] - 1 >= 1 )
        $html .= '<li><a>'.( $_SESSION[$table]['activePage'] - 1 ).'</a></li>';
      $html.= '        <li class="active">'.$_SESSION[$table]['activePage'].'</li>';
      if( $_SESSION[$table]['activePage'] + 1 <= $pages )
        $html .= '<li><a>'.( $_SESSION[$table]['activePage'] + 1 ).'</a></li>';
      if( $_SESSION[$table]['activePage'] + 2 <= $pages )
        $html .= '<li><a>'.( $_SESSION[$table]['activePage'] + 2 ).'</a></li>';
      if( $pages > 3 and $_SESSION[$table]['activePage'] < $pages - 2 )
        $html .= '<li style="margin-left:1rem">...<a style="display:inline-block">'.$pages.'</a></li>';
      if( $_SESSION[$table]['activePage'] != $pages )
        $html.= '        <li class="next"><a>></a></li>';
      $html.= '      </ul>';
    }
    return $html;
  }

/**
 * thead rész összeállítása
 *
 * @param string $table   Adattábla neve
 * @param string $where   Szűrési feltételek, ha nincs null
 * @return string         html string
 */
  public static function make_thead( $table, $head, $controls = null, &$check = null ){
    $html = '    <thead data-order="">';
    $html.= '      <tr>';
    if(( $controls ?? 0 ) and ( $controls->group->operation ?? 0 )){
      $html.= '      <th class="allcheckbox">';
      $html.= '        <input type="checkbox" id="allcheckbox-'.$table.'">';
      $html.= '        <label for="allcheckbox-'.$table.'"></label>';
      $html.= '      </th>';
      $check = true;
    }
    foreach( $head as $col => $th ){
      $class = '';
      if( $th['class'] ?? 0 )
        $class = ( ( $class != '' )? ' ' : ' class="' ).$th['class'];
      if( $th['translate'] ?? 0 )
        $th['th'] = $th['th'];
      $dataorder = false;
      $order = '';
      if( $th['order'] ?? 0 ){
        $dataorder = $th['order'];
        if(( $_SESSION[$table]['order'] ?? 0 ) and $_SESSION[$table]['order'][0] == $col )
          if( $_SESSION[$table]['order'][1] == 'DESC' ){
            $dataorder.= ' DESC';
            $order = '<i style="--icon:var(--icon-sort-up)"></i>';
          }else{
            $dataorder.= ' ASC';
            $order = '<i style="--icon:var(--icon-sort-down)"></i>';
          }
        else
          $order = '<i style="--icon:var(--icon-sort-1)"></i>';
      }
      $style = '';
      if( $th['min-width'] ?? 0 )
        $style = ' style="min-width:'.$th['min-width'].'"';
      if( $th['max-width'] ?? 0 )
        $style = ' style="max-width:'.$th['max-width'].'"';
      $class.= ( $class != '' )? '"' : '';
      $html.= '<th'.$style.$class.(( $dataorder )? ' data-order="'.$dataorder.'"' : '' ).'>'.$th['th'].$order.'</th>';
    }
    $html.= '      </tr>';
    $html.= '    </thead>';
    return $html;
  }

/**
 * tbody rész összeállítása
 *
 * @param string $table   Adattábla neve
 * @param string $where   Szűrési feltételek, ha nincs null
 * @return string         html string
 */
  public static function list_tbody( $parameters ){
    require_once './datatable.php';
    $rows = call_user_func( 'table_'.$parameters['table'], $parameters );
    return self::make_tbody( $rows, $parameters['head'] ?? null, $parameters['check'] ?? null );
  }

/**
 * tbody rész elkészítése
 *
 * @param  array  $rows Megjelenítendő sorok adatai
 * @return string       html string
 */
  public static function make_tbody( $rows, $head = null, $check = null ){
    $html = '';
    if( $rows )
      foreach( $rows as $row )
        $html.= '<tr data-id="'.( $row['id'] ?? '' ).'">'.self::make_tr( $row, $head ?? null, $check ?? null ).'</tr>';
    else
      $html.= '<tr><td colspan="'.(( $head ?? 0 )? count( $head ) : 1 ).'"><center>Nem található adat!</center></td></tr>';
    return $html;
  }

/**
* táblázat egy sorának elkészítése
 *
 * @param  array   $row   Sor adata ['mező neve' => 'mező értéke',...]
 * @param  array   $style Egyes adatok paraméterei ['mező neve' => ['mi' => 'paraméter',...]]
 *                        mi: 'class' td -hez hozzáadja a paraméterben lévő class-t.
 *                            'status'
 *                            'span'
 *                            'translate'
 *                            'click' td-hez hozzáadja a data-click="{click}" atributumot. {click} szabja meg mi legyen
 *                                    {click} 'link:{url+id}' ugrás az url-re amihez a sor id hozzáadásra kerül pl. url/43
 *                                            'event:esemény neve' eseményfügvény erre a névre van kötve 
 * @param  boolean $check Van csoportos kijelölő true/false
 * @param  array   $icons Ha van akkor funkció ikon(ok) ['edit',trash]
 *                        Click esemény: .{tabla neve} i[data-function="trash"]
 * @return string        html string
 */
  public static function make_tr( $row, $head = null, $check = null ){
    $html = '';
    if( $check ?? 0 )
      $html .= '<td class="cb">
                  <div class="checkbox">
                    <input class="checkbox-input" type="checkbox" id="checkbox'.$row['id'].'">
                    <label class="checkbox-label" for="checkbox'.$row['id'].'"></label>
                  </div>
                </td>';
    foreach( $head ?? $row as $key => $value ){
      $content = $class = $click = '';
      
      if( ( $head[$key]['where'] ?? 0 ) and !$head[$key]['where'] ) continue;

      if( $head[$key]['class'] ?? 0 )
        $class = (( $class != '' )? ' ' : ' class="' ).$head[$key]['class'];

      if( $head[$key]['span'] ?? 0 ){
        $class = (( $class != '' )? ' ' : ' class="' ).$head[$key]['span'];
        if( $key[0] != '*' ) $content = '<span>'.( $row[$key] ?? '' ).'</span>';
      }

      if( $head[$key]['status'] ?? 0 ){
        $status = explode( '|', $head[$key]['status'] );
        if( $key[0] != '*' ) $content = '<span data-'.$key.'="'.( $row[$status[0]] ?? '' ).'">'.( $row[$key] ?? '' ).'</span>';
      }

      if( $head[$key]['click'] ?? 0 )
        $click = ' data-click="'.$head[$key]['click'].'"';

      if( $head[$key]['icons'] ?? 0 ){
        $class = (( $class != '' )? ' ' : ' class="' ).'icons';
        foreach( $head[$key]['icons'] as $icon ){
          $icon = explode( ':', $icon );
          if(( $icon[6] ?? 0 ) and $icon[6] == 'where' and ( $row[$icon[7]] ?? 0 )) continue;
          if( $icon[1] == 'link' )
            $content.= '<a style="--icon:var(--icon-'.$icon[0].')"
                            href="'.http::$path.$icon[3].(( $icon[3] ?? 0 )? ( $row[$icon[4]] ?? '' ) : '' ).'"
                           title="'.$icon[2].'"'.((( $icon[5] ?? 0 ) and $icon[5] == 'confirm' )? ' onclick="return confirm( \'Biztos, hogy ezt akarod?\' )"' : '' ).'></a>';
          elseif( $icon[1] == 'event' )
            $content.= '<i data-ifunction="'.$icon[0].'|event" style="--icon:var(--icon-'.$icon[0].')"></i>';
          elseif( $icon[1] == 'modal' )
            $content.= '<i data-ifunction="'.$icon[0].'|modal" style="--icon:var(--icon-'.$icon[0].')"></i>';
          else
            $content.= '<i style="--icon:var(--icon-'.$icon[0].')"></i>';
        }
      }
      
      $content.= ( $content == '' and $key[0] != '*' )? ( $row[$key] ?? '' ) : '';
      $class.= ( $class != '' )? '"' : '';
      $html.= '<td'.$class.$click.'>'.$content.'</td>';
    }
    
    return $html;
  }
}
