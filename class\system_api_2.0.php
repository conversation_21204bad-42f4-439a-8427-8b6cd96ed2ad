<?php
/**
 * <PERSON><PERSON> kez<PERSON>
 *
 * $_SESSION['PROID'] hasz<PERSON><PERSON><PERSON>, ha már <PERSON>
 *
 * @method `init();`           <PERSON><PERSON><PERSON><PERSON><PERSON> indítása
 * @method `response();`       <PERSON><PERSON><PERSON>z összeállítás
 * @method `apiKeyControl();`  <PERSON><PERSON><PERSON><PERSON>
 * @method `authentication();` <PERSON><PERSON><PERSON><PERSON>
 * @method `verifyToken();`    Token ellenőrzés adatok védelmében (GET)
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2018, Tánczos Róbert
 *
 * @version 2.0.0
 * @since 2.0.0 2025.03.08 tokenizálás GET esetén adatok védelmében, hibaüzenes refaktorálás
 * @since 1.4.0 2023.10.21 api_key kezelés változása
 * @since 1.3.0 2022.12.30 verziózás bevezetése a fájlnévben
 * @since 1.2.0 2020.02.29 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>á tétel
 * @since 1.1.0 2018.07.11 Optimalizálás, php 7.x
 * @since 1.0.0 2018.01.08 
 */

class Api extends model_basic{
  public static $request = [];
  public static $error = false;

  private static function _requestStatus( $code ){
    $status = [
      200 => 'OK',
      201 => 'Created',
      202 => 'Accepted',
      203 => 'Non-Authoritative Information',
      204 => 'No Content',
      205 => 'Reset Content',
      206 => 'Partial Content',
      400 => 'Bad Request',
      401 => 'Unauthorized',
      402 => 'Payment Required',
      403 => 'Forbidden',
      404 => 'Not Found',
      405 => 'Method Not Allowed',
      406 => 'Not Acceptable',
      407 => 'Proxy Authentication Required',
      408 => 'Request Time-out',
      409 => 'Conflict',
      410 => 'Gone',
      411 => 'Length Required',
      412 => 'Precondition Failed',
      413 => 'Request Entity Too Large',
      414 => 'Request-URI Too Large',
      415 => 'Unsupported Media Type',
      500 => 'Internal Server Error',
      501 => 'Not Implemented',
      502 => 'Bad Gateway',
      503 => 'Service Unavailable',
      504 => 'Gateway Time-out',
      505 => 'HTTP Version not supported'
    ];
    return $status[$code ?? 500] ?? $status[500];
  }

  private static function _cleanInputs( $data ){
    return is_array( $data )? array_map( [self::class, '_cleanInputs'], $data ) : trim( strip_tags( $data ));
  }
  
  public static function init(){
    switch( $_SERVER['REQUEST_METHOD'] ?? (( http::$route[1] != 'web' )? 'POST' : 'GET' )){
      case 'DELETE':
      case 'POST':
        if( $_POST ?? 0 )
          self::$request = self::_cleanInputs( $_POST );
        else{
          $requestPayload = file_get_contents( 'php://input' );
          self::$request = ( $requestPayload ?? 0 )? json_decode( $requestPayload, true ) : [];
        }
      break;
      case 'GET': self::$request = self::_cleanInputs( $_GET ?? [] ); break;
      default: self::response( 'Invalid Method', 405 ); break;
    }

    if( !( http::$route[1] ?? 0 ) or http::$route[1] != 'web' ){
      if(( self::$request ?? 0 ) and array_values( self::$request )[0] == '' ){
        self::$request['apikey'] = array_key_first( self::$request );
        unset( self::$request[self::$request['apikey']] );
      }
    }else{
      $headers = getallheaders();
      if( !array_key_exists( 'Authorization' , $headers )){
        if( http::$route[3] != 'documentation' )
          self::response( 'Missing Authorization Header', 401 );
      }elseif( substr( $headers['Authorization'], 0, 7 ) !== 'Bearer ' )
        self::response( 'Invalid Authorization Header', 401 );
      else
        self::$request['apikey'] = substr( $headers['Authorization'], 7 );
    }
    user_error( 'INFOAPI ROUTE: '.json_encode( http::$route ).' REQ: '.json_encode( self::$request ));
  }
  
  public static function response( $data, $status = 200 ){
    header( 'HTTP/1.1 ' . $status . ' ' . self::_requestStatus( $status ));
    echo json_encode( $data );
  }

  public static function apiKeyControl(){
    if( $company = self::get_companyApi( self::$request['apikey'] ?? '' )){
      $company['configuration'] = json_decode( $company['configuration'] ?? '' );
      $company['api_settings'] = json_decode( $company['api_settings'] ?? '' );
      if( $company['configuration']->api_domain ?? 0 ){        
        $referer = ( $_SERVER['HTTP_REFERER'] ?? 0 )?
          substr( $_SERVER['HTTP_REFERER'], strpos( $_SERVER['HTTP_REFERER'], '://' ) + 3 ) : 0;
        $referer = $referer ? substr( $referer, 0, strpos( $referer, '/' )) : 0;
        $ok_domain = false;
        if( $referer )
          foreach( $company['api_settings'] as $api )
            if(( $api ?? 0 ) and $api->domain == $referer ){
              $ok_domain = true;
              break;
            }
        else $ok_domain = true;
        if( !$ok_domain ){
          self::$error = ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Jogosulatlan domain-ről hozzáférési kísérlet']];
          return false;
        }
      }
      if( $company['configuration']->api_ip ?? 0 ){
        $ok_ip = false;
        if( $api ?? 0 )
          $ok_ip = ( $api->ip == $_SERVER['REMOTE_ADDR'] )? true : false;
        else
          foreach( $company['api_settings'] as $api )
            if(( $api ?? 0 ) and $api->ip == $_SERVER['REMOTE_ADDR'] ){
              $ok_ip = true;
              break;
            }
        if( !$ok_ip ){
          self::$error = ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Jogosulatlan IP-ről hozzáférési kísérlet']];
          return false;
        }
      }
      if( $api ?? 0 ){
        if( $api->expire < date('Y-m-d' )){
          self::$error = ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Lejárt apikulcs']];
          return false;
        }
      }
    }else{
      self::$error = ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Nemlétező apikulcs']];
      return false;
    }
    return $company;
  }

  public static function authentication( $action = 'apikey' ){
    switch( $action ){
      case 'apikey':
        if( $company = self::get_companyApi( self::$request['apikey'] ?? '' )){
          $company['configuration'] = json_decode( $company['configuration'] ?? '' );
          $company['api_settings'] = json_decode( $company['api_settings'] ?? '' );
          if( $company['configuration']->api_domain ?? 0 ){    
            $referer = ( $_SERVER['HTTP_REFERER'] ?? 0 )?
              substr( $_SERVER['HTTP_REFERER'], strpos( $_SERVER['HTTP_REFERER'], '://' ) + 3 ) : 0;
            $referer = $referer ? substr( $referer, 0, strpos( $referer, '/' )) : 0;
            $ok_domain = false;
            if( $referer != '' )
              if(( $company['api_settings']->domain ?? 0 ) and $company['api_settings']->domain == $referer ){
                $ok_domain = true;
                break;
              }
            else $ok_domain = true;
            if( !$ok_domain ){
              self::response( ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Unauthorised domain access: '. $referer]], 401 );
              exit;
            }
          }
          if( $company['api_settings']->ip ?? 0 ){
            $ok_ip = ( $company['api_settings']->ip == $_SERVER['REMOTE_ADDR'] )? true : false;
            if( !$ok_ip ){
              self::response( ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Unauthorised domain access: '. $_SERVER['REMOTE_ADDR']]], 401 );
              exit;
            }
          }
          if( $company['api_settings']->expire ?? 0 ){
            if( $company['api_settings']->expire < date('Y-m-d' )){
              self::response( ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Eligibility has expired: '. $company['api_settings']->expire]], 401 );
              exit;
            }
          }
        }else{
          self::response( ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Inadequate authentication']], 401 );
          exit;
        }
        return $company;
      break;
    }
  }

  public static function verifyToken( $token ){
    $tokenization = substr( $token, 0, 3 );
    $token = substr( $token, 3 );
    switch( $tokenization ){
      case 'b64':
        $data = json_decode( base64_decode( $token ), true );
      break;
      case 'mac':
        $data = explode( '::', base64_decode( $token ));
        $signature = hash_hmac( 'sha256', $data[0], self::$request['apikey'] );
        if( $signature != $data[1] ){
          self::response( ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Invalid signature']], 401 );
          exit;
        }
        $data = json_decode( $data[0], true );
        if( $data['signature_expires'] < time()){
          self::response( ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Signature expired']], 401 );
          exit;
        }
        unset( $data['signature_expires'] );
      break;
      case 'jwt':
        $data = JWT::decode( $token, self::$request['apikey'], ['HS256'] );
        if( $data['signature_expires'] < time()){
          self::response( ['error' => ['code' => 401, 'message' => 'Unauthorised access', 'details' => 'Signature expired']], 401 );
          exit;
        }
        unset( $data['signature_expires'] );
      break;
    }
    if( count( $data ))
      self::$request = array_merge( self::$request, $data );
  }
}