  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5)?>
      <?= response::alert()?>
      <header><h5>Foglalási naptár / Szobatükör</h5></header>
      <article>
        <?php if( response::$vw->view->foglalaslist ?? 0 ){ ?>
        <div data-datatable="naptar">
          <div class="controls">
            <?php /*
            <form class="search formbox">
              <div class="form">
                <input type="text" name="foglalasoklist_search" placeholder="">
                <label>Keres</label>
              </div>
            </form>
            */ ?>
            <span>
              <a href="?step=VH">-1 hó</a>
              <a href="?step=V7">-1 hét</a>
              <a href="?step=VN">-1 nap</a>
              <a href="?step=MA">Mai nap</a>
              <a href="?step=EN">+1 nap</a>
              <a href="?step=E7">+1 hét</a>
              <a href="?step=EH">+1 hó</a>
            </span>
            <span>
              <a class="btn blocking" style="--icon:var(--icon-blocked)">Időszak zárolás</a>
              <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új foglalás</a>
            </span>
          </div>  
          <div class="tablescroll">
            <table>
              <thead>
                <tr>
                  <th class="celfix">Lakóegység</th>
                  <?php $napok = ['Vas','Hét','Ked','Sze','Csü','Pén','Szo']; ?>
                  <?php for( $i = 0; $i < $_SESSION['MAXNAP']; $i++ ){ ?>
                  <th style="min-width:68px<?=( date('Y-m-d', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) ) == date('Y-m-d') )?'; background:var(--btn-bg-color)':''?><?= ( date( 'N', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) ) > 5 and date('Y-m-d', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) ) != date('Y-m-d') )? '; background:var(--work-lighter)' : '' ?>"><a href="?step=DA<?= date('Y-m-d', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) ) ?>"><?= date('m.d', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) ) ?></a><br><?= $napok[date( 'w', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) )] ?></th>
                  <?php } ?>
                </tr>
              </thead>
              <tbody>
                <?php foreach( response::$vw->view->foglalaslist as $szoba => $foglalt ){ $iid = 0; $szoba = explode('max', $szoba); ?>
                <tr>
                  <th class="szoba">
                    <?=$szoba[0]?><br>max<?=$szoba[1]?>
                    <?php /*
                    <a href="<?= http::$path?>/naptar/foglalas/<?=$szoba[2]?>" title="Foglalás"><i style="--icon:var(--icon-key)"></i></a>
                    */ ?>
                  </th>
                  <?php for( $i = 0; $i < $_SESSION['MAXNAP']; $i++ ){ ?>
                  
                  <?php   $mikor = date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) ); ?>
                  <?php   $divmargin = ''; $divwidth = 83; ?>
                  <?php   if( isset( $foglalt->foglalt[$iid] ) ){ ?> 
                  <?php     if( $foglalt->foglalt[$iid]->erkezes < $_SESSION['NAP'] ){ $divmargin = 'ET'; $divwidth -= 42; } ?>
                  <?php     if( $foglalt->foglalt[$iid]->tavozas > date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +'.( $_SESSION['MAXNAP']-1 ).' day' ) ) ) $divwidth -= 42; ?>
                  <?php   } ?>

                  <td class="<?= ( date( 'N', strtotime( $_SESSION['NAP'].' +'.$i.' day' ) ) > 5 )? 'kiemelt ' : '' ?>">
                    <?php if( $divmargin == 'ET' ){ ?>
                    <?php   view_divFoglalt(); ?>
                    <?php   $divmargin = ''; $divwidth = 83; ?>
                    <?php   $iid++; ?>
                    <?php   if( $foglalt->foglalt[$iid]->tavozas > date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +'.( $_SESSION['MAXNAP']-1 ).' day' ) ) ){ $divwidth -= 58; } ?>
                    <?php } ?>

                    <?php if( $foglalt->foglalt[$iid]->erkezes ?? 0 and $foglalt->foglalt[$iid]->erkezes == $mikor ){ ?>
                    <?php   view_divFoglalt(); ?>
                    <?php   $iid++; ?>
                    <?php } ?>
                    &nbsp;
                  </td>
                  <?php } ?>
                </tr>
                <?php } ?>
              </tbody>
            </table>
          </div>
        </div>
        <?php }else{ ?>
          Előbb lakóegységet kell felvinni!
        <?php } ?>
      </article>
    </div>
  </section>
  <script type="module">
    import
      {$, dialog, dialogRebuild, closeDialog, ajax, getFormatDate, getNextDate, getDiffDay} from '/shared/js/spritzer/index.js'
    
    window.addEventListener('DOMContentLoaded', () => {
      
      const blocking = document.querySelector( '.btn.blocking' )
      blocking ? blocking.addEventListener( 'click', () => {
        dialog( {
          id: 'naptar-blocked',
          isKeepOpen: true,
          loadFile: '/modal/naptarblockedadd',
          eventManagers: {
            'callback': ['', 'naptar-blocked_callback']
          },
          eventManagerFunctions: ( data ) => {
            let 
              ifunction = data[1],
              id = data[2] || null,
              fd,
              is_ko
            switch( ifunction ){
              case 'naptar-blocked_callback':
                event.preventDefault()
                is_ko = true;
                fd = new FormData( $( 'form[name="form_blocking"]' ) )
                fd.append( 'btn_mentes', true )
                if( fd.get('nap1') && fd.get('nap1') != '' &&
                    fd.get('nap2') && fd.get('nap2') != '' &&
                    fd.get('lakoegysegek[]') )
                  ajax( {
                    url: '/modal/naptar-blocked_callback',
                    body: fd,
                    done: ( back ) => {
                      let response = JSON.parse( back.response )
                      if( response.ok ){
                        closeDialog( 'naptar-blocked' )
                        dialog( {type: 'status:success', content: 'A zárolás mentve', timeDelay: 6000} )
                        setTimeout( location.replace( '/naptar' ), 8000 )
                      }else
                        dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                    },
                    fail: ( err ) => {
                      dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                    }
                  } )
                else{
                  dialog( {type: 'status:error', content: 'Csillaggal jelölt mezők kitöltése kötelező!<br><b>Kezdő nap</b>, <b>utolsó nap</b> és legalább egy <b>lakóegység</b> kiválasztása', timeDelay: 6000} )
                  is_ko = false;
                }
              break
            }
          }
        } )
      } ) : null

      const create = document.querySelector( '.btn.create' )
      create ? create.addEventListener( 'click', () => {
        dialog( {
          id: 'naptar-create',
          isKeepOpen: true,
          loadFile: '/modal/naptarcreate',
          events: ['change'],
          eventManagers: {
            'callback': ['', 'naptar_create_callback'],
            'change': [['input[name="nap1"], input[name="nap2"], input[name="ej"]', 'naptar_change_date']]
          },
          eventManagerFunctions: ( data ) => {
            let 
              ifunction = data[1],
              id = data[2] || null,
              fd
            switch( ifunction ){
              case 'naptar_create_callback':
                event.preventDefault()
                let 
                  is_ko = true,
                  fd = new FormData( document.forms.namedItem( 'form_uj_foglalas' ) )
                fd.append( 'btn_mentes', true )
                if( fd.get('nap1') && fd.get('nap1') != '' &&
                    fd.get('nap2') && fd.get('nap2') != '' &&
                    fd.get('lakoegysegek[]') )
                  ajax( {
                    url: '/modal/foglalascreate',
                    body: fd,
                    done: ( back ) => {
                      let response = JSON.parse( back.response )
                      if( response.ok ){
                        closeDialog( 'naptar-create' )
                        dialog( {type: 'status:success', content: 'A foglalás mentve', timeDelay: 6000} )
                        setTimeout( location.replace( '/naptar' ), 8000 )
                      }else
                        dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
                    },
                    fail: ( err ) => {
                      dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                    }
                  } )
                else{
                  dialog( {type: 'status:error', content: 'Csillaggal jelölt mezők kitöltése kötelező!<br><b>Érkezés</b>, <b>távozás</b> és legalább egy <b>lakóegység</b> kiválasztása', timeDelay: 6000} )
                  is_ko = false;
                }
              break
              case 'naptar_change_date':
                let
                  erkezes = document.querySelector('input[name="nap1"]'),
                  tavozas = document.querySelector('input[name="nap2"]'),
                  ej = document.querySelector('input[name="ej"]')
                
                switch( event.target.name ){
                  case 'ej':
                    tavozas.value = ej2tavozas( erkezes.value, ej.value )
                  break
                  case 'nap2':
                    ej.value = getDiffDay( new Date(tavozas.value.valueOf() ), new Date(erkezes.value.valueOf() ) )
                  break
                  case 'nap1':
                    let
                      erk = new Date(erkezes.value.valueOf() ),
                      tav = new Date(tavozas.value.valueOf() )
                    if( erk < tav )
                      ej.value = getDiffDay( tav, erk )
                    else
                      tavozas.value = ej2tavozas( erkezes.value, ej.value )
                  break
                }

                function ej2tavozas( erkezes, ej ){
                  return getFormatDate( 'Y-m-d', getNextDate( new Date( erkezes ), parseInt( ej ) ) )
                }
              break
            }
          }
        } )
      } ) : null

      const blockedits = document.querySelectorAll( '[data-foglalasid].statusZ' )
      blockedits ? blockedits.forEach( blockedit => blockedit.addEventListener( 'click', () => {
        dialog( {
          id: 'naptar-blocked',
          isKeepOpen: true,
          loadFile: '/modal/naptarblocked/'+ blockedit.dataset.foglalasid,
          eventManagers: {
            'callback': ['', 'naptar-blocked_del', blockedit.dataset.foglalasid]
          },
          eventManagerFunctions: ( data ) => {
            let 
              ifunction = data[1],
              id = data[2] || null,
              fd
            switch( ifunction ){
              case 'naptar-blocked_del': // Zárolást töröl
                event.preventDefault()
                fd = new FormData()
                fd.append( 'foglalas_id', id )
                ajax( {
                  url: '/modal/foglalastorol',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      closeDialog( 'naptar-blocked' )
                      dialog( {type: 'status:success', content: 'Zárolás törölve', timeDelay: 6000} )
                      setTimeout( location.replace( '/naptar' ), 8000 )
                    }else
                    dialog( {type: 'status:error', content: 'Sikertelen zárolás törlés!', timeDelay: 6000} )
                  },
                  fail: ( err ) => {
                    dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                  }
                } )
              break
            }
          }
        } )
      } ) ) : null

      const edits = document.querySelectorAll( '[data-foglalasid]:not(.statusZ)' )
      edits ? edits.forEach( edit => edit.addEventListener( 'click', () => {
        dialog( {
          id: 'naptar-edit',
          isKeepOpen: true,
          loadFile: '/modal/naptar/edit/'+ edit.dataset.foglalasid,
          eventManagers: {
            'close': ['', 'naptar-edit_close'],
            'afterBuilding': ['', 'naptar-edit_after_building', edit.dataset.foglalasid]
          },
          eventManagerFunctions: ( data ) => {
            let 
              ifunction = data[1],
              id = data[2] || null,
              fd
            switch( ifunction ){
              case 'naptar-edit_after_building':
                return {
                  'click': [
                    ['button[name="btn_modositas"]', 'naptar_edit_foglalas_modositas', id],
                    ['.btn.lemond', 'naptar_edit_foglalas_lemond', id],
                    ['.btn.torol', 'naptar_edit_foglalas_torol', id],
                    ['.btn.torolle', 'naptar_edit_lakoegyseg_torol', id],
                    ['.btn.plussz-le', 'naptar_edit_lakoegyseg_hozzaad', id],
                    ['button[name="btn_modositas-le"]', 'naptar_edit_lakoegyseg_modositas', id],
                    ['button[name="btn_fizet"]', 'naptar_edit_fizet', id],
                    ['[data-datatable="fizetesek"] .icons i', 'naptar_edit_fizet_torol']
                  ]
                }
              break;
              case 'naptar_edit_foglalas_modositas':
                event.preventDefault()
                fd = new FormData( $( 'form[name="form_foglalas_modosit"]' ) )
                fd.append( 'foglalas_id', id )
                fd.append( 'btn_modositas', true )
                ajax( {
                  url: '/modal/foglalas',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      closeDialog( 'naptar-edit' )
                      dialog( {type: 'status:success', content: 'A foglalás módosítva', timeDelay: 6000} )
                      setTimeout( location.replace( '/naptar' ), 8000 )
                    }else
                      dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
                  },
                  fail: ( err ) => {
                    dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                  }
                } )
              break;
              case 'naptar_edit_foglalas_lemond':
                event.preventDefault()
                  fd = new FormData()
                  fd.append( 'foglalas_id', id )
                  ajax( {
                    url: '/modal/lemondas',
                    body: fd,
                    done: ( back ) => {
                      let response = JSON.parse( back.response )
                      if( response.ok ){
                        closeDialog( 'naptar-edit' )
                        dialog( {type: 'status:success', content: 'A foglalás lemondva', timeDelay: 6000} )
                        setTimeout( location.replace( '/naptar' ), 8000 )
                      }else
                        dialog( {type: 'status:error', content: 'Sikertelen a lemondás!', timeDelay: 6000} )
                    },
                    fail: ( err ) => {
                      dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                    }
                  } )
              break;
              case 'naptar_edit_foglalas_torol':
                event.preventDefault()
                fd = new FormData()
                fd.append( 'foglalas_id', id )
                ajax( {
                  url: '/modal/foglalastorol',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      closeDialog( 'naptar-edit' )
                      dialog( {type: 'status:success', content: 'A foglalás törölve', timeDelay: 6000} )
                      setTimeout( location.replace( '/naptar' ), 8000 )
                    }else
                      dialog( {type: 'status:error', content: 'Sikertelen a törlés!', timeDelay: 6000} )
                  },
                  fail: ( err ) => {
                    dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                  }
                } )
              break;
              case 'naptar_edit_lakoegyseg_torol':
                event.preventDefault()
                fd = new FormData( event.target.closest( 'form[name="form_data_modosit-le"]' ) )
                fd.append( 'foglalas_id', id )
                ajax( {
                  url: '/modal/foglalastorolle',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      dialogRebuild( 'naptar-edit' )
                      dialog( {type: 'status:success', content: 'A foglalt lekóegység törölve', timeDelay: 3000} )
                    }else
                    dialog( {type: 'status:error', content: 'Sikertelen a foglalt lakóegység törlés!', timeDelay: 6000} )
                  },
                  fail: ( err ) => {
                    dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                  }
                } )
              break;
              case 'naptar_edit_lakoegyseg_hozzaad':
                event.preventDefault()
                let options = '';
                <?php foreach( response::$vw->view->szabadlakoegysegek as $lakoegyseg ){ ?>
                options += '<option value="<?= $lakoegyseg->id ?>"><?= $lakoegyseg->name ?></option>';
                <?php } ?>
                dialog({
                  id: 'lakoegyseg-select',
                  type: 'dialog',
                  btnCallback: 'Hozzáad',
                  isBtnCallback: true,
                  isBtnClose: true,
                  isKeepOpen: true,
                  title: 'Lakóegység választás',
                  content:
                    '<form name="form_data_ujlakoegyseg">' +
                    '  <ul class="formbox">' +              
                    '    <li class="form col0" style="--toicon:var(--icon-angle-double-down)">' +
                    '      <select name="ujlakoegyseg_id">' + options +
                    '      </select>' +
                    '      <label>Lakóegység</label>' +
                    '    </li>' +
                    '  </ul>' +
                    '</form>',
                  eventManagers: {
                    'afterBuilding': ['', 'lakoegyseg_select'],
                    'callback': ['', 'lakoegyseg_callback', id]
                  },
                  eventManagerFunctions: function( data ){
                    let ifunction = data[1],
                        id = data[2] || null,
                        fd
                    switch( ifunction ){
                      case 'lakoegyseg_callback':
                        fd = new FormData( $( 'form[name="form_data_ujlakoegyseg"]' ) )
                        fd.append( 'foglalas_id', id )
                        ajax( {
                          url: '/modal/plusszfoglalle',
                          body: fd,
                          done: ( back ) => {
                            let response = JSON.parse( back.response )
                            if( response.ok ){
                              closeDialog( 'lakoegyseg-select' )
                              dialogRebuild( 'naptar-edit' )
                              dialog( {type: 'status:success', content: 'A foglalás módosítva', timeDelay: 3000} )
                            }else
                            dialog( {type: 'status:error', content: 'A foglalás módosítása sikertelen!', timeDelay: 6000} )
                          },
                          fail: ( err ) => {
                            dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                          }
                        } )
                      break;
                      case 'lakoegyseg_select':
                        const select = document.querySelector( 'form[name="form_data_ujlakoegyseg"] select' )
                        select ? select.focus() : null
                      break;
                    }
                  }
                } )
              break;
              case 'naptar_edit_lakoegyseg_modositas':
                event.preventDefault()
                fd = new FormData( event.target.closest( 'form[name="form_data_modosit-le"]' ) )
                fd.append( 'btn_modositas-le', true )
                ajax( {
                  url: '/modal/foglalaslemod',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      dialog( {type: 'status:success', content: 'A foglalás módosítva', timeDelay: 3000} )
                    }else
                    dialog( {type: 'status:error', content: 'A foglalás módosítása sikertelen!', timeDelay: 6000} )
                  },
                  fail: ( err ) => {
                    dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                  }
                } )
              break;
              case 'naptar_edit_fizet':
                event.preventDefault()
                fd = new FormData( $( 'form[name="form_data_fizet"]' ) )
                fd.append( 'foglalas_id', id )
                fd.append( 'btn_fizet', true )
                ajax( {
                  url: '/modal/foglalasfizet',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      dialogRebuild( 'naptar-edit' )
                      dialog( {type: 'status:success', content: 'A fizetés mentve', timeDelay: 3000} )
                    }else
                      dialog( {type: 'status:error', content: 'A fizetés mentése sikertelen!', timeDelay: 6000} )
                  },
                  fail: ( err ) => {
                    dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                  }
                } )
              break;
              case 'naptar_edit_fizet_torol':
                event.preventDefault()
                fd = new FormData()
                fd.append( 'penz_id', event.target.closest( 'tr[data-id]' ).dataset.id )
                ajax( {
                  url: '/modal/foglalasfizettorol',
                  body: fd,
                  done: ( back ) => {
                    let response = JSON.parse( back.response )
                    if( response.ok ){
                      dialogRebuild( 'naptar-edit' )
                      dialog( {type: 'status:success', content: 'A fizetési tétel törölve', timeDelay: 3000} )                
                    }else
                    dialog( {type: 'status:error', content: 'A fizetés törlése nem sikerült!', timeDelay: 6000} )
                  },
                  fail: ( err ) => {
                    dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
                  }
                } )
              break;
              case 'naptar-edit_close':
                location.replace( '/naptar' )
              break;
            }
          }
        } )
      } ) ) : null
    } )

  </script>

<?php
function view_divFoglalt(){
  global $foglalt, $iid, $divmargin, $divwidth;
  $status = 'statusF';
  if( $foglalt->foglalt[$iid]->erkezes <= date( 'Y-m-d' ) ) $status = 'statusB';
  if( $foglalt->foglalt[$iid]->tavozas < date( 'Y-m-d' ) ) $status = 'statusK';
  if( $foglalt->foglalt[$iid]->vendegfo == -1 ) $status = 'statusZ';
?>
  <div data-info="<?= '|'.$foglalt->foglalt[$iid]->erkezes.'|'.$foglalt->foglalt[$iid]->tavozas.'|'.$_SESSION['NAP'].'|'.$divwidth.'|'.$foglalt->foglalt[$iid]->div->db.'|'.$divmargin ?>" data-foglalasid="<?= $foglalt->foglalt[$iid]->foglalas_id ?>" class="foginfo foglalt<?= $divmargin ?> <?= $status ?><?= ( $foglalt->foglalt[$iid]->vendegfo != -1 and $foglalt->foglalt[$iid]->fizetendo != $foglalt->foglalt[$iid]->fizetve )? ' notpay ' : '' ?>" style="width: calc(<?= ( 100*$foglalt->foglalt[$iid]->div->db+$divwidth ) ?>% + <?= $foglalt->foglalt[$iid]->div->db ?>px )">
    <?php if( $foglalt->foglalt[$iid]->vendegfo == -1 ){ ?>
    <div>Lezárva</div>  
    <?php }else{ ?>
    <div><?= $foglalt->foglalt[$iid]->kapcsolattarto.'</div>
    <div>'.$foglalt->foglalt[$iid]->ej.' éj '.$foglalt->foglalt[$iid]->vendegfo.' fő' ?></div>
    <div><?= $foglalt->foglalt[$iid]->csatorna ?? '&nbsp;' ?></div>
    <div><?= $foglalt->foglalt[$iid]->szla ?? '&nbsp;' ?></div>
    <?php } ?>
  </div>
<?php
}