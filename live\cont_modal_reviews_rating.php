<?php
if( $review = db::get( DATABASENAME.'.reviews', http::$route[3] )){
  $_POST['id'] = $review['id'];
  $_POST['provider_id'] = $review['provider_id'];
  $_POST['trip_purpose_id'] = $review['trip_purpose_id'];
  $_POST['location'] = $review['location'];
  $_POST['communication'] = $review['communication'];
  $_POST['cleanliness'] = $review['cleanliness'];
  $_POST['value'] = $review['value'];
  $_POST['checkin'] = $review['checkin'];
  $_POST['accuracy'] = $review['accuracy'];
  $_POST['services'] = $review['services'];
  $_POST['comfort'] = $review['comfort'];
  $_POST['rooms'] = $review['rooms'];
  $_POST['wifi'] = $review['wifi'];
  $_POST['author_name'] = $review['author_name'];
  $_POST['author_country'] = $review['author_country'];
  $_POST['author_city'] = $review['author_city'];  
  $_POST['title'] = $review['title'];
  $_POST['review'] = $review['review'];
  $_POST['negative_review'] = $review['negative_review'];
  $_POST['answer'] = $review['answer'];
  $_POST['residency_days'] = $review['residency_days'];
  $_POST['when_was'] = $review['when_was'];
  $_POST['status'] = $review['signs'][0];
}
$providers = db::list( DATABASENAME.'.sys_providers' );
$countries = model_basic::list_countries( 'tld, name' );
$trip_purposes = ['Nincs megadva', 'Szóló', 'Barátokkal', 'Párban', 'Családdal', 'Munka'];