          <section class="container">
            <div class="cart">
                <?= response::alert('message',0,0,5) ?>
                <?= response::alert() ?>
                <header><h5>Pénzmozgások</h5></header>
                <article>
                  <form name="form_data_penz" method="post" action="<?= http::$path?>/penz">
                    <ul class="padlib fmve">
                      <li class="gridw5 padr"><span>Időszak tól</span><input type="text" name="kezd" value="2023-01-01"></li>
                      <li class="gridw5 padr"><span>Időszak ig</span><input type="text" name="vege" value="2023-12-31"></li>
                      <li class="gridw5 padr"><span>Előleg nap</span><input type="text" name="elnap" value="3"></li>
                      <li class="gridw5 padr"><span>Lemond nap</span><input type="text" name="lenap" value="14"></li>
                      <li class=""><input class="btn" type="submit" name="btn_penz" value="Gyűjt" title=""></li>
                    </ul>
                  </form>
                  <table>
                    <thead>
                      <tr>
                        <th rowspan="2">Foglalva</th>
                        <th rowspan="2">Érkezés</th>
                        <th rowspan="2">Foglalás</th>
                        <th colspan="2">Fizetendő</th>
                        <th colspan="2">Fizetett</th>
                        <th rowspan="2"></th>
                      </tr>
                      <tr>
                        <th>Lemondható</th>  
                        <th>Saját</th>
                        <th>Lemondható</th>
                        <th>Saját</th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php if( response::$vw->view->penzlist ){ ?>
                      <?php   foreach( response::$vw->view->penzlist as $penz ){
                                $bg = ( $penz->lemondva )? ' disabled' : ''; ?>
                      <tr>
                        <td><?= $penz->foglalva ?></td>
                        <td><?= $penz->mikor ?></td>
                        <td style="color: var(--table-thead-color); background-color: var(--<?= $penz->color ?>-color)" >#<?= $penz->foglalas_id ?></td>
                        <td class="tar<?= $bg ?>"><?= ( $penz->fizetendoL )? number_format( $penz->fizetendoL, 0, '', ' ' ) : '' ?></td>
                        <td class="tar<?= $bg ?>"><?= ( $penz->fizetendoS )? number_format( $penz->fizetendoS, 0, '', ' ' ) : '' ?></td>
                        <td class="tar<?= $bg ?>"><?= ( $penz->fizetettL )? number_format( $penz->fizetettL, 0, '', ' ' ) : '' ?></td>
                        <td class="tar<?= $bg ?>"><?= ( $penz->fizetettS )? number_format( $penz->fizetettS, 0, '', ' ' ) : '' ?></td>
                        <td>
                          <a style="--icon:var(--icon-edit)" href="<?= http::$path?>/naptar/modosit/<?= $penz->foglalas_id ?>" title="Foglaláshoz"></a>
                          <a style="--icon:var(--icon-money)" href="<?= http::$path?>/naptar/fizet/<?= $penz->foglalas_id ?>" title="Fizetéshez"></a>
                        </td>
                      </tr>
                      <?php   } ?>
                      <?php } ?>
                    </tbody>
                    <tfoot>
                      <tr>
                        <td rowspan="3" colspan="3">Összesen:</td>
                        <td class="tar"><?= number_format( response::$vw->view->penzossz->ofizetendoL, 0, '', ' ' ) ?></td>
                        <td class="tar"><?= number_format( response::$vw->view->penzossz->ofizetendoS, 0, '', ' ' ) ?></td>
                        <td class="tar"><?= number_format( response::$vw->view->penzossz->ofizetettL, 0, '', ' ' ) ?></td>
                        <td class="tar"><?= number_format( response::$vw->view->penzossz->ofizetettS, 0, '', ' ' ) ?></td>
                      </tr>
                      <tr>
                        <td colspan="2" class="tar"><b><?= number_format( response::$vw->view->penzossz->ofizetendoL + response::$vw->view->penzossz->ofizetendoS, 0, '', ' ' ) ?></b></td>
                        <td colspan="2" class="tar"><b><?= number_format( response::$vw->view->penzossz->ofizetettL + response::$vw->view->penzossz->ofizetettS, 0, '', ' ' ) ?></b></td>
                      </tr>
                      <tr>
                        <td colspan="2" class="tar"><b>Egyenleg:</b></td>
                        <td colspan="2" class="tar"><b><?= number_format( response::$vw->view->penzossz->ofizetendoL + response::$vw->view->penzossz->ofizetendoS - response::$vw->view->penzossz->ofizetettL - response::$vw->view->penzossz->ofizetettS, 0, '', ' ' ) ?></b></td>
                      </tr>
                    </tfoot>
                  </table>
                </article>
            </div>
          </section>
