<?php
/**
 * <PERSON><PERSON> k<PERSON><PERSON>
 *
 * $_SESSION['PROID'] hasz<PERSON><PERSON><PERSON>, ha már <PERSON>
 *
 * @method `init();`          <PERSON><PERSON><PERSON><PERSON><PERSON> indítása
 * @method `response();`      <PERSON><PERSON><PERSON>z összeállítás
 * @method `apikeykontrol();` <PERSON><PERSON><PERSON><PERSON>
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2019, Tánczos Róbert
 *
 * @version 1.3.0
 * @since 1.3.0 2022.12.30 verzi<PERSON><PERSON><PERSON> bevezetése a fájlnévben
 * @since 1.2.0 2020.02.29 <PERSON><PERSON><PERSON><PERSON>, általánossá tétel
 * @since 1.1.0 2018.07.11 Optimalizálás, php 7.x
 * @since 1.0.0 2018.01.08
 */

class api extends model_basic{
  public static $request = [];
  public static $error = false;

  private static function _requestStatus( $code ){
    $status = [
      200 => 'OK',
      404 => 'Not Found',
      405 => 'Method Not Allowed',
      500 => 'Internal Server Error'
    ];
    return $status[$code ?? 500] ?? $status[500];
  }

  private static function _cleanInputs( $data ){
    return is_array( $data )? array_map( [self::class, '_cleanInputs'], $data ) : trim( strip_tags( $data ) );
  }
  
  public static function init(){
    switch( $_SERVER['REQUEST_METHOD'] ?? 'POST' ){
      case 'POST': self::$request = self::_cleanInputs( $_POST ?? [] ); break;
      case 'GET': self::$request = self::_cleanInputs( $_GET ?? [] ); break;
      default: self::response( 'Invalid Method', 405 ); break;
    }
    if( self::$request ?? 0 and array_values( self::$request )[0] == '' ){
      self::$request['apikey'] = array_key_first( self::$request );
      unset( self::$request[self::$request['apikey']] );
    }
  }
  
  public static function response( $data, $status = 200 ){
    header( 'HTTP/1.1 ' . $status . ' ' . self::_requestStatus( $status ) );
    echo json_encode( $data );
  }

  public static function apikeykontrol(){
    user_error( 'API_KEYKONTROL: '.$_SERVER['HTTP_REFERER'].' | '.$_SERVER['REMOTE_ADDR'].' | '.json_encode( self::$request ) );
    if( $company = self::get_companyApi( self::$request['apikey'] ?? '' ) ){
      $company['configuration'] = json_decode( $company['configuration'] ?? false );
      if( $company['expire'] < date('Y-m-d' ) ){
          $company = false;
          self::$error = ["status" => "false", "hibakod" => "AK02" ]; // lejárt apikulcs
      }elseif($company['api_domain'] != ''){
        $referer = substr( $_SERVER['HTTP_REFERER'], strpos( $_SERVER['HTTP_REFERER'], '://' ) + 3 );
        $referer = substr( $referer, 0, strpos( $referer, '/' ) );
        if($company['api_domain'] != $referer ){
          $company = false;
          self::$error = ["status" => "false", "hibakod" => "AK04" ]; // jogosulatlan domain-ről hozzáférés
        }
      }elseif( $company['api_ip'] != '' and $company['api_ip'] != ( $_SERVER['REMOTE_ADDR'] ?? '-' ) ){
        $company = false;
        self::$error = ["status" => "false", "hibakod" => "AK03" ]; // jogosulatlan IP-ről hozzáférés
      }
    }else
      self::$error = ["status" => "false", "hibakod" => "AK01", "apikey" => self::$request['apikey'] ]; // nemlétező apikulcs
    return $company ?? false;
  }
}