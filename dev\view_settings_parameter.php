  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Paraméterek</h5>
      </header>
      <article>
        <form name="form_settings_parameter" method="post" action="<?= http::$path ?>/settings/parameter">
          <ul class="formbox">
            <li class="form col0">
              <input class="validation" type="text" name="name" value="<?= $_POST['name'] ?>" placeholder="" required autofocus autocomplete="off">
              <label>Szálláshely neve</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col5">
              <input class="validation" type="text" name="ntak_number" value="<?= $_POST['ntak_number'] ?? '' ?>" placeholder="" required autocomplete="off">
              <label>NTAK regisztr<PERSON><PERSON><PERSON> s<PERSON></label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col5" data-tooltip="line">
              <input class="validation" type="text" name="registration_number" value="<?= $_POST['registration_number'] ?? '' ?>" placeholder="" required autocomplete="off">
              <label>Önk. nyilvántartási szám</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Ez a működési engedély száma</div>
              </div>
            </li>
            <li class="form col5" data-tooltip="line">
              <input class="validation" type="number" name="accommodation_units_number" value="<?= $_POST['accommodation_units_number'] ?? '' ?>" placeholder="" min="1" max="8" required>
              <label>Lakóegységek száma</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Ha 8 lakóegységnél több van írjon nekünk</div>
              </div>
            </li>
            <li class="form col5 bgonto" data-to="fő" data-tooltip="line">
              <input class="validation" type="number" name="number_of_places" value="<?= $_POST['number_of_places'] ?? '' ?>" placeholder="" min="1" max="16" required>
              <label>Férőhelyek száma</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Ha 16 férőhelynél több van írjon nekünk</div>
              </div>
            </li>
          </ul>
          <hr>
          <h5>Foglalási feltételek</h5>
          <ul class="formbox">
            <li class="form col5 bgonto" data-to="%" data-tooltip="line">
              <input class="validation" type="number" name="advance_payment" value="<?= $_POST['advance_payment'] ?? '' ?>" placeholder="" min="0" max="100">
              <label>Előleg mértéke</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Ha nincs előleg fizetés, akkor 0</div>
              </div>
            </li>
            <li class="form col5 bgonto" data-to="nap" data-tooltip="line">
              <input class="validation" type="number" name="advance_payment_deadline" value="<?= $_POST['advance_payment_deadline'] ?>" placeholder="" min="0">
              <label>Előleg fizetési határidő</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Foglalás időpontjától számított napok, ha azonnali, akkor 0</div>
              </div>
            </li>
            <li class="form col5 bgonto" data-to="nap" data-tooltip="line">
              <input class="validation" type="number" name="cancellation_deadline" value="<?= $_POST['cancellation_deadline'] ?>" placeholder="" min="0">
              <label>Lemondás határideje</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Érkezést megelőző napok száma</div>
              </div>
            </li>
            <li class="form col5 bgonto" data-to="nap" data-tooltip="line">
              <input class="validation" type="number" name="data_request_deadline" value="<?= $_POST['data_request_deadline'] ?>" placeholder="" min="0">
              <label>Adatbekérés határidő</label>
              <div class="input-message tooltip">
                <div hidden class="error"></div>
                <div class="info">Érkezést megelőző napok száma. Vendégadatok bekérése, ha nincs, akkor üresen marad</div>
              </div>
            </li>
          </ul>
          <?php if( setup::is_right( 0 )){ ?>
          <hr>
          <h5>NAV számla küldés kapcsolat beállításai</h5>
          <ul class="formbox">
            <li class="form col5">
              <input class="validation" type="text" name="invoice_nav_login" value="<?= $_POST['invoice_nav_login'] ?>" placeholder="" required autocomplete="off">
              <label>NAV felhasználó</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col5">
              <input class="validation" type="text" name="invoice_nav_passwd" value="<?= $_POST['invoice_nav_passwd'] ?>" placeholder="" required autocomplete="off">
              <label>NAV jelszó</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col5">
              <input class="validation" type="text" name="invoice_nav_xml_key" value="<?= $_POST['invoice_nav_xml_key'] ?>" placeholder="" required autocomplete="off">
              <label>XML aláírókulcs</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col5">
              <input class="validation" type="text" name="invoice_nav_xml_key_change" value="<?= $_POST['invoice_nav_xml_key_change'] ?>" placeholder="" required autocomplete="off">
              <label>XML cserekulcs</label>
              <div class="input-message">
                <div hidden class="error"></div>
              </div>
            </li>
            <li class="form col0">
              <input type="text" name="invoice_nav_account_block" value="<?= $_POST['invoice_nav_account_block'] ?>" placeholder="" autocomplete="off">
              <label>Számlatömb</label>
            </li>
          </ul>
          <?php } ?>
          <ul class="formbox">
            <li><button name="btn_settings_parameter" value="1">Mentés</button></li>
          </ul>
        </form>
      </article>
    </div>
  </section>
  <script type="module">
    import{ validation } from '/shared/js/spritzer/index.js'
    window.addEventListener( 'DOMContentLoaded', validation())
  </script>