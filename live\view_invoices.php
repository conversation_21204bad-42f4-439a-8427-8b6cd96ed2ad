  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Számlák</h5>
      </header>
      <article>
        <?= table::datatable( 
          'invoices',
          [ 'invoice_number' => ['th' => 'Számlaszám'],
            'allapotstr' => ['th' => 'Állapot', 'status' => 'allapot'],
            'issue_date' => ['th' => 'Kiállítás kelte'],
            'booking_id' => ['th' => '#'],
            'customer_name' => ['th' => 'Vevő'],
            '*1' => [
              'th' => '',
              'icons' => [
                'file-pdf:event:PDF::id'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { datatable, dialog, upload, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let
        ifunction = data[1],
        id = data[2] || null,
        fd,
        selects
      switch( ifunction ){
        case 'invoices_edit':
          upload()
          return {
            'click': [
              ['.select-add', 'invoices_edit_select_add', id],
              ['.select-del', 'invoices_edit_select_del', id]
            ]
          }
        break
        case 'invoices_edit_close':
          location.replace( '/invoices' )
        break
        case 'invoices_file-pdf':
          event.preventDefault()
          fd = new FormData()
          fd.append( 'invoice_id', id )
          ajax( {
            url: '/modal/invoicepdf',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
              dialog( {
                'type': 'dialog',
                'isESCKey': true,
                'iconClose': '--icon-cancel',
                'title': 'Számla pdf',
                'content': 'pdf: <a href="/upload/<?= $_SESSION['COMPANY'] ?>/invoice_'+id+'.pdf" target="_blank">invoice_'+id+'.pdf</a>'
              } )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a pdf generálás! ' + response.error, timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
      }
    }

    window.addEventListener( 'DOMContentLoaded', () => {
      datatable( {
        eventManagerFunctions
      } )
    } )
  </script>