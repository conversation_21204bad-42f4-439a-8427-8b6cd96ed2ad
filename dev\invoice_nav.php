<?php
class InvoiceNav{

  public static function invoice2xml( $invoice, $shopid = 0 ){
    $xml = new XMLWriter();
    $xml->openURI( 'upload/'.( $shopid ? $shopid : $_SESSION['SHOPID'] ).'/invoice_'.$invoice['id'].'.xml' );
    $xml->setIndentString( '  ' );
    $xml->setIndent( true );
    $xml->startDocument( '1.0', 'UTF-8' );
    
      $xml->startElement( 'InvoiceData' );
        $xml->writeAttribute( 'xmlns', 'http://schemas.nav.gov.hu/OSA/3.0/data' );
        $xml->writeAttribute( 'xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance' );
        $xml->writeAttribute( 'xsi:schemaLocation', 'http://schemas.nav.gov.hu/OSA/3.0/data invoiceData.xsd' );
        $xml->writeAttribute( 'xmlns:common', 'http://schemas.nav.gov.hu/NTCA/1.0/common' );
        $xml->writeAttribute( 'xmlns:base', 'http://schemas.nav.gov.hu/OSA/3.0/base' );
    
        $xml->writeElement( 'invoiceNumber', $invoice['invoiceNumber'] );
        $xml->writeElement( 'invoiceIssueDate', $invoice['invoiceIssueDate'] );
        $xml->writeElement( 'completenessIndicator', 'false' );
        $xml->startElement( 'invoiceMain' );
          $xml->startElement( 'invoice' );
            $xml->startElement( 'invoiceHead' );
              $xml->startElement( 'supplierInfo' );
                $xml->startElement( 'supplierTaxNumber' );
                  $xml->writeElement( 'base:taxpayerId', substr( $invoice['supplierInfo']['taxNumber'], 0, 8 ) );
                  $xml->writeElement( 'base:vatCode', substr( $invoice['supplierInfo']['taxNumber'], 9, 1 ) );
                  $xml->writeElement( 'base:countyCode', substr( $invoice['supplierInfo']['taxNumber'], 11 ) );
                $xml->endElement();
                $xml->writeElement( 'supplierName', $invoice['supplierInfo']['supplierName'] );
                $xml->startElement( 'supplierAddress' );
                  $xml->startElement( 'base:detailedAddress' );
                    $xml->writeElement( 'base:countryCode', $invoice['supplierInfo']['countryCode'] );
                    $xml->writeElement( 'base:postalCode', $invoice['supplierInfo']['postalCode'] );
                    $xml->writeElement( 'base:city', $invoice['supplierInfo']['city'] );
                    $xml->writeElement( 'base:streetName', $invoice['supplierInfo']['streetName'] );
                    $xml->writeElement( 'base:publicPlaceCategory', $invoice['supplierInfo']['publicPlaceCategory'] );
                    $xml->writeElement( 'base:number', $invoice['supplierInfo']['number'] );
                  $xml->endElement();
                $xml->endElement();
                if( $invoice['supplierInfo']['supplierBankAccountNumber'] != '' )
                  $xml->writeElement( 'supplierBankAccountNumber', $invoice['supplierInfo']['supplierBankAccountNumber'] );
              $xml->endElement();
              $xml->startElement( 'customerInfo' );
                $xml->writeElement( 'customerVatStatus', 'DOMESTIC' ); // DOMESTIC || PRIVATE_PERSON
                if( $invoice['customerInfo']['taxNumber'] != '' ){
                  $xml->startElement( 'customerVatData' );
                    $xml->startElement( 'customerTaxNumber' );
                      $xml->writeElement( 'base:taxpayerId', substr( $invoice['customerInfo']['taxNumber'], 0, 8 ) );
                      $xml->writeElement( 'base:vatCode', substr( $invoice['customerInfo']['taxNumber'], 9, 1 ) );
                      $xml->writeElement( 'base:countyCode', substr( $invoice['customerInfo']['taxNumber'], 11 ) );
                      // communityVatNumber EU adószám
                      // thirdStateTaxId 3. ország beli adószám
                    $xml->endElement();
                  $xml->endElement();
                }
                $xml->writeElement( 'customerName', $invoice['customerInfo']['customerName'] );
                $xml->startElement( 'customerAddress' );
                  $xml->startElement( 'base:simpleAddress' );
                    $xml->writeElement( 'base:countryCode', $invoice['customerInfo']['countryCode'] );
                    $xml->writeElement( 'base:postalCode', $invoice['customerInfo']['postalCode'] );
                    if( $invoice['customerInfo']['city'] != '' )
                      $xml->writeElement( 'base:city', $invoice['customerInfo']['city'] );
                    if( $invoice['customerInfo']['additionalAddressDetail'] != '' )
                      $xml->writeElement( 'base:additionalAddressDetail', $invoice['customerInfo']['additionalAddressDetail'] );
                  $xml->endElement();
                $xml->endElement();
              $xml->endElement();
              $xml->startElement( 'invoiceDetail' );
                $xml->writeElement( 'invoiceCategory', 'NORMAL' );
                $xml->writeElement( 'invoiceDeliveryDate', $invoice['invoiceDetail']['invoiceDeliveryDate'] );
                $xml->writeElement( 'currencyCode', $invoice['invoiceDetail']['currencyCode'] );
                $xml->writeElement( 'exchangeRate', $invoice['invoiceDetail']['exchangeRate'] );
                $xml->writeElement( 'utilitySettlementIndicator', 'false' );
                $xml->writeElement( 'paymentDate', $invoice['invoiceDetail']['paymentDate'] );
                $xml->writeElement( 'invoiceAppearance', 'PAPER' );
              $xml->endElement();
            $xml->endElement();

            $xml->startElement( 'invoiceLines' );
              $xml->writeElement( 'mergedItemIndicator', 'false' );
              $key = 1;
              foreach( $invoice['ilines'] as $iline ){
                $xml->startElement( 'line' );
                  $xml->writeElement( 'lineNumber', $key );
                  $key++;
                  if( $iline['lineExpressionIndicator'] ?? 0 ){
                    $lineExpressionIndicator = $iline['lineExpressionIndicator']? 'true' : 'false';
                    $xml->writeElement( 'lineExpressionIndicator', $lineExpressionIndicator );
                    $xml->writeElement( 'lineDescription', $iline['lineDescription'] );
                    if( $iline['lineExpressionIndicator'] ){
                      $xml->writeElement( 'quantity', $iline['quantity'] );
                      $xml->writeElement( 'unitOfMeasure', $iline['unitOfMeasure'] );
                      $xml->writeElement( 'unitPrice', $iline['unitPrice'] );
                      
                      $xml->startElement( 'lineAmountsNormal' );
                        $xml->startElement( 'lineNetAmountData' );
                          $xml->writeElement( 'lineNetAmount', $iline['lineNetAmount'] );
                          $xml->writeElement( 'lineNetAmountHUF', $iline['lineNetAmountHUF'] );
                        $xml->endElement();
                        $xml->startElement( 'lineVatRate' );
                          $xml->writeElement( 'vatPercentage', $iline['taxRate'] );
                        $xml->endElement();
                        $xml->startElement( 'lineVatData' );
                          $xml->writeElement( 'lineVatAmount', $iline['lineVatAmount'] );
                          $xml->writeElement( 'lineVatAmountHUF', $iline['lineVatAmountHUF'] );
                        $xml->endElement();
                        $xml->startElement( 'lineGrossAmountData' );
                          $xml->writeElement( 'lineGrossAmountNormal', $iline['lineNetAmount'] + $iline['lineVatAmount'] );
                          $xml->writeElement( 'lineGrossAmountNormalHUF', $iline['lineNetAmountHUF'] + $iline['lineVatAmountHUF'] );
                        $xml->endElement();
                      $xml->endElement();
                    }
                  }else{
                    $xml->startElement( 'advanceData' );
                      $xml->writeElement( 'advanceIndicator', 'true' );
                      $xml->startElement( 'advancePaymentData' );
                        $xml->writeElement( 'advanceOriginalInvoice', $iline['advanceOriginalInvoice'] );
                        $xml->writeElement( 'advancePaymentDate', $iline['advancePaymentDate'] );
                        $xml->writeElement( 'advanceExchangeRate', $iline['advanceExchangeRate'] );
                      $xml->endElement();
                    $xml->endElement();
                  }
                $xml->endElement();
              }
            $xml->endElement();

            $xml->startElement( 'invoiceSummary' );
              $xml->startElement( 'summaryNormal' );
                $sumnet = $sumnetHUF = $sumvat = $sumvatHUF = 0;
                foreach( $invoice['summaryByVatRate'] as $svatrate ){
                  $xml->startElement( 'summaryByVatRate' );
                    $xml->startElement( 'vatRate' );
                      $xml->writeElement( 'vatPercentage', $svatrate['vatPercentage'] );
                    $xml->endElement();
                    $xml->startElement( 'vatRateNetData' );
                      $xml->writeElement( 'vatRateNetAmount', $svatrate['vatRateNetAmount'] );
                      $xml->writeElement( 'vatRateNetAmountHUF', $svatrate['vatRateNetAmountHUF'] );
                    $xml->endElement();
                    $xml->startElement( 'vatRateVatData' );
                      $xml->writeElement( 'vatRateVatAmount', $svatrate['vatRateVatAmount'] );
                      $xml->writeElement( 'vatRateVatAmountHUF', $svatrate['vatRateVatAmountHUF'] );
                    $xml->endElement();
                  $xml->endElement();
                  $sumnet+= $svatrate['vatRateNetAmount'];
                  $sumnetHUF+= $svatrate['vatRateNetAmountHUF'];
                  $sumvat+= $svatrate['vatRateVatAmount'];
                  $sumvatHUF+= $svatrate['vatRateVatAmountHUF'];
                }
                $xml->writeElement( 'invoiceNetAmount', $sumnet );
					      $xml->writeElement( 'invoiceNetAmountHUF', $sumnetHUF );
					      $xml->writeElement( 'invoiceVatAmount', $sumvat );
					      $xml->writeElement( 'invoiceVatAmountHUF', $sumvatHUF );
              $xml->endElement();
              $xml->startElement( 'summaryGrossData' );
                $xml->writeElement( 'invoiceGrossAmount', $sumnet + $sumvat );
                $xml->writeElement( 'invoiceGrossAmountHUF', $sumnetHUF + $sumvatHUF );
              $xml->endElement();
            $xml->endElement();
          $xml->endElement();
        $xml->endElement();
      $xml->endElement();
    $xml->endElement();

    $xml->endDocument();
    $xml->flush();
  }

  public static function get_invoice_items_compilation( $invoice, $shopid = 0 ){
    $lines = $sumvats = [];
    if( $invoice_items = db::list_invoice_items( $invoice['id'], $shopid ) )
      foreach( $invoice_items as $key => $iitem ){
        if( $iitem['product_id'] ){ 
          $lines[] = [
            'lineExpressionIndicator' => true,
            'lineDescription' => $iitem['name'].' '.$iitem['sku'],
            'quantity' => $iitem['quantity'],
            'unitOfMeasure' => 'PIECE',
            'unitPrice' => $iitem['price_net'],
            'taxRate' => $iitem['tax_rate'] - 1,
            'lineNetAmount' => $iitem['price_net'] * $iitem['quantity'],
            'lineNetAmountHUF' => round( $iitem['price_net'] * $iitem['quantity'] * $invoice['exchange_rate'], 2 ),
            'lineVatAmount' => round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 ), 2 ),
            'lineVatAmountHUF' => round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 )  * $invoice['exchange_rate'], 2 )
          ];
        }elseif( $iitem['quantity'] == -1 ){
          // Előleg
          $advance_invoice = db::get( 'invoices', 0, ' WHERE order_id='.$invoice['order_id'].' AND type=2 AND status=1' );
          $advance_invoice_number = db::get( 'invoice_number', 0, ' WHERE invoice_id='.$advance_invoice['id'] );
          $lines[] = [
            'advanceData' => [
              'advanceIndicator' => true,
              'advancePaymentData' => [
                'advanceOriginalInvoice' => $advance_invoice_number['prefix'].$advance_invoice_number['id'],
                'advancePaymentDate' => substr( $advance_invoice_number['create'], 0, 10 ),
                'advanceExchangeRate' => $advance_invoice['exchange_rate']
              ]
            ]
          ];
        }else{
          // Szállítási díj
          $lines[] = [
            'lineExpressionIndicator' => false,
            'lineDescription' => $iitem['name']
          ];
        }

        if( count( $sumvats ) ){
          $ok = true;
          foreach( $sumvats as $key => $value )
            if( $value['vatPercentage'] == $iitem['tax_rate'] - 1 ){
              $ok = false;
              $sumvats[$key]['vatRateNetAmount'] += $iitem['price_net'] * $iitem['quantity'];
              $sumvats[$key]['vatRateNetAmountHUF'] += round( $iitem['price_net'] * $iitem['quantity'] * $invoice['exchange_rate'], 2 );
              $sumvats[$key]['vatRateVatAmount'] += round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 ), 2 );
              $sumvats[$key]['vatRateVatAmountHUF'] += round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 ) * $invoice['exchange_rate'], 2 );
            }
          if( $ok )
            $sumvats[] = [
              'vatPercentage' => $iitem['tax_rate'] - 1,
              'vatRateNetAmount' => $iitem['price_net'] * $iitem['quantity'],
              'vatRateNetAmountHUF' => round( $iitem['price_net'] * $iitem['quantity'] * $invoice['exchange_rate'], 2 ),
              'vatRateVatAmount' => round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 ), 2 ),
              'vatRateVatAmountHUF' => round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 ) * $invoice['exchange_rate'], 2 )
            ];
        }else
          $sumvats[] = [
            'vatPercentage' => $iitem['tax_rate'] - 1,
            'vatRateNetAmount' => $iitem['price_net'] * $iitem['quantity'],
            'vatRateNetAmountHUF' => round( $iitem['price_net'] * $iitem['quantity'] * $invoice['exchange_rate'], 2 ),
            'vatRateVatAmount' => round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 ), 2 ),
            'vatRateVatAmountHUF' => round( $iitem['price_net'] * $iitem['quantity'] * ( $iitem['tax_rate'] - 1 ) * $invoice['exchange_rate'], 2 )
          ];      
      }
    return [$lines, $sumvats];
  }

  public static function get_invoice_compilation( $invoice, $shopid = 0 ){
    $lines = self::get_invoice_items_compilation( $invoice, $shopid );
    $invoice_nav = ['id' => $invoice['id'],
                    'invoiceNumber' => $invoice['invoice_number']['prefix'].$invoice['invoice_number']['id'],
                    'invoiceIssueDate' => substr( $invoice['invoice_number']['created'], 0, 10 ),
                    'supplierInfo' => [
                      'taxNumber' => $invoice['supplier']['tax_number'],
                      'supplierName' => $invoice['supplier']['name'],
                      'countryCode' => strtoupper( $invoice['supplier']['countryCode'] ),
                      'postalCode' => $invoice['supplier']['postalCode'] ?? '0000',
                      'city' => $invoice['supplier']['city'],
                      'streetName' => $invoice['supplier']['streetName'],
                      'publicPlaceCategory' => $invoice['supplier']['publicPlaceCategory'],
                      'number' => $invoice['supplier']['number'],
                      'supplierBankAccountNumber' => $invoice['supplier']['supplierBankAccountNumber'] ?? ''
                    ],
                    'customerInfo' => [
                      'VatStatus' => 'DOMESTIC',
                      'taxNumber' => $invoice['customer']['tax_number'],
                      'customerName' => $invoice['customer']['name'].' '.$invoice['customer']['firstname'],
                      'countryCode' => strtoupper( $invoice['customer']['country'] ),
                      'postalCode' => $invoice['customer']['zipcode'] ?? '0000',
                      'city' => $invoice['customer']['city'],
                      'additionalAddressDetail' => $invoice['customer']['address']
                    ],
                    'invoiceDetail' => [
                      'invoiceDeliveryDate' => $invoice['execution_date'],
                      'currencyCode' => $invoice['currency_code'],
                      'exchangeRate' => $invoice['exchange_rate'],
                      'paymentDate' => $invoice['payment_date'] ?? substr( $invoice['invoice_number']['created'], 0, 10 )
                    ],
                    'ilines' => $lines[0],
                    'summaryByVatRate' => $lines[1]
                  ];
    self::invoice2xml( $invoice_nav, $shopid );
  }

  public static function invoices_control(){

  }

  public static function import_invoices(){

  }


  <?php
// lekérdezés tól ig 
include ("config.php");
include("../../phputil/util.php");

$wKezd=$argv[1];
$wVege=utolsoNap($argv[1]);


try {
    $config = new NavOnlineInvoice\Config($apiUrl, $userData, $softwareData);
    $reporter = new NavOnlineInvoice\Reporter($config);
    $invoiceQueryParams = [
      "mandatoryQueryParams" => [
        "invoiceIssueDate" => [
          "dateFrom" => "2025-01-01",
          "dateTo" => "2025-01-31",
        ],
      ]
    ];
    $page = 1;

    $invoiceDigestResult = $reporter->queryInvoiceDigest( $invoiceQueryParams, $page, "OUTBOUND" );    

    print "Query results XML elem:\n";
    var_dump( $invoiceDigestResult );


    // ennyi oldalt kell lapoznom
    $WinvoiceDigestResult = $invoiceDigestResult->availablePage;
    
    
    for ($x = 1; $x <= $WinvoiceDigestResult; $x ++) {
        
        $invoiceDigestResult = $reporter->queryInvoiceDigest($invoiceQueryParams, $x, $irany);
        
        // foreach($invoiceDigestResult -> children() as $val)
        // foreach($invoiceDigestResult as $val){
        foreach ($invoiceDigestResult->invoiceDigest as $val) {
            //echo "'"; 
    echo $val->invoiceNumber . $delimiter  ;
            echo $val->customerName .  $delimiter  ;
            echo $val->customerTaxNumber .  $delimiter  ;
            echo $val->index .  $delimiter  ;
            echo $val->insDate .  $delimiter  ;
            echo $val->invoiceDeliveryDate .  $delimiter  ;
            echo $val->invoiceIssueDate . $delimiter  ;
            echo $val->invoiceNetAmountHUF .  $delimiter  ;
            echo $val->invoiceVatAmountHUF .  $delimiter  ;
            echo $val->paymentMethod . $delimiter  ;
            echo $val->paymentDate .  $delimiter  ;
            echo $val->supplierName .  $delimiter  ;
            echo $val->supplierTaxNumber .  $delimiter  ;
            echo $val->transactionId .  $delimiter  ;
$statusXml = $reporter->queryTransactionStatus($val->transactionId);
echo $statusXml->processingResults->processingResult->invoiceStatus  .  $delimiter  ;
echo $statusXml->processingResults->processingResult-> businessValidationMessages -> validationResultCode .  $delimiter  ;
echo $statusXml->processingResults->processingResult-> businessValidationMessages -> validationErrorCode . $delimiter  ;
echo $statusXml->processingResults->processingResult-> businessValidationMessages -> message .  PHP_EOL ;

        }
    }
} catch (Exception $ex) {
    print get_class($ex) . ": " . $ex->getMessage();
}

?>

************************
<?php

//echo utolsoNap($argv[1]);
// phonap utolso napját adja vissza 2020-08-01
function utolsoNap($dateToTest){

$lastday = date('t',strtotime($dateToTest));
//echo $lastday;
return (substr($dateToTest,0,8).$lastday);
}
?>




<?php
// lekérdezés tól ig 
include ("config.php");
include("../../phputil/util.php");




$wKezd=$argv[1];
$wVege=utolsoNap($argv[1]);

try {
    $config = new NavOnlineInvoice\Config($apiUrl, $userData, $softwareData);
    $reporter = new NavOnlineInvoice\Reporter($config);
    
    $invoiceQueryParams = [
        "mandatoryQueryParams" => [
            "invoiceIssueDate" => [
                "dateFrom" => $wKezd,
                "dateTo" => $wVege
            ]
        ]
    ];
    $irany='OUTBOUND';
// $irany='INBOUND';
    $invoiceDigestResult = $reporter->queryInvoiceDigest($invoiceQueryParams, 1, $irany);
    
    // ennyi oldalt kell lapoznom
    $WinvoiceDigestResult = $invoiceDigestResult->availablePage;
    
    
    for ($x = 1; $x <= $WinvoiceDigestResult; $x ++) {
        
        $invoiceDigestResult = $reporter->queryInvoiceDigest($invoiceQueryParams, $x, $irany);
        
        // foreach($invoiceDigestResult -> children() as $val)
        // foreach($invoiceDigestResult as $val){
        foreach ($invoiceDigestResult->invoiceDigest as $val) {
            echo $val->invoiceNumber . " ; ";
            echo $val->customerName . " ; ";
            echo $val->customerTaxNumber . " ; ";
            echo $val->index . " ; ";
            echo $val->insDate . " ; ";
            echo $val->invoiceDeliveryDate . " ; ";
            echo $val->invoiceIssueDate . " ; ";
            echo $val->invoiceNetAmountHUF . " ; ";
            echo $val->invoiceVatAmountHUF . " ; ";
            echo $val->paymentMethod . " ; ";
            echo $val->paymentDate . " ; ";
            echo $val->supplierName . " ; ";
            echo $val->supplierTaxNumber . " ; ";
            echo $val->transactionId . PHP_EOL;
        }
    }
} catch (Exception $ex) {
    print get_class($ex) . ": " . $ex->getMessage();
}

?>




}