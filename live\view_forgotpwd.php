        <style>
          :-webkit-autofill,
          :-webkit-autofill:hover, 
          :-webkit-autofill:focus {
            -webkit-box-shadow: 0 0 0px 1000px #fff inset;
          }
          article{
            & .carts{
              &>.cart{
                width: clamp( 300px, calc(50% - .5vw), calc(50% - .5vw) );
                & h1{ padding-bottom: 1em }
                & ul.formbox{
                  display: flex;
                  flex-flow: row wrap;
                  justify-content: center;
                  gap: 1rem;
                  & li{
                    &.form{
                      position: relative;
                      line-height: 100%;
                      & input{
                        height: 3rem;
                        width: 100%;
                        padding-top: 1rem;
                        border: 1px solid transparent;
                        border-radius: 8px;
                        text-indent: 0.8rem;
                        &:focus{
                          border-color: #472C4C;
                          & ~ label{
                            opacity: 0.8;
                            transform: scale(0.9) translateY(-0.65rem) translateX(0.1rem);
                          }
                        }
                        &:not(:placeholder-shown) ~ label{
                          opacity: 0.8;
                          transform: scale(0.9) translateY(-0.65rem) translateX(0.1rem);
                        }
                      }
                      &>label{
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 100%;
                        padding: 1rem 0.8rem 0;
                        pointer-events: none;
                        border: 1px solid transparent;
                        //text-transform: uppercase;
                        transform-origin: 0 0;
                        transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
                        color: #76aea2;
                      }
                    }
                    &.col0{ width: 100% }
                  }
                }
              }
            }
          }
        </style>
        <div class="carts" style="margin-top: 20vh">
          <div class="cart">
            <h1>Add meg az emailcímet amivel regisztráltál!</h1>
            <form name="form_forgotpwd" method="post" action="<?= http::$path ?>/forgotpwd">
              <ul class="formbox">
                <li class="form col0">
                  <input type="email" name="email" id="email" placeholder="" required autofocus>
                  <label for="email">Email</label>
                </li>
                <li><button name="btn_forgotpwd">Emlékeztető</button></li>
              </ul>
              <center>
                <a href="<?= http::$path?>/login" title="">Vissza a bejelentkezéshez</a><br>
              </center>
            </form>
            <?php if( response::$vw->error ?? 0 ){ ?>
            <?= response::alert('error','forgotpwd') ?>
            <?php } ?>
          </section>
