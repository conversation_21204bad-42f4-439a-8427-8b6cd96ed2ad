@charset "UTF-8";
/* Theme - <PERSON><PERSON><PERSON><PERSON> */
:root {

/* #ebfffb #d6fff6 *#a9f9d9 *#76aea2  #55b395 */

  --primary: oklch(93% 0.1 167); /* #a4feda */
  --secondary: #d6fff6;
  --tertiary: #76ae86;
  --work-lighter: #f9f9f9;
  --work-light: #f3f1f2;
  --work-medium: #efefed;
  --work-dark: #e7e7e5;
  --dark: #000;
  --light: #fff;
        
  --danger-color: #ff8569;
  --warning-color: #fcc74f;
  --success-color: #d9ed92;
  --info-color: #97e2f7;

  --color: var(--dark);
  --bg-color: var(--light);
  --header-bg-color: var(--secondary);
  --link-color: var(--tertiary);
  --link-hover-color: var(--dark);
  --btn-color: var(--dark);
  --btn-bg-color: var(--secondary);
  --btn-hover-color: var(--light);
  --btn-hover-bg-color: var(--tertiary);
  --dashboard-color: var(--dark);
  --dashboard-bg-color: var(--secondary);
  --dashboard-border-color: var(--work-lighter);
  --dashboard-hover-color: var(--dark);
  --dashboard-hover-bg-color: var(--work-medium);
  --dashboard-sec-color: var(--dark);
  --dashboard-sec-bg-color: var(--primary);
  --dashboard-sec-hover-color: var(--dark);
  --dashboard-sec-hover-bg-color: var(--work-medium);
  --dashboard-active-color: var(--dark);
  --dashboard-active-bg-color: var(--light);
  --dashboard-active-border-color: var(--warning-color);
  --cart-color: var(--dark);
  --cart-bg-color: var(--work-dark);
  --cart-border-color: var(--primary);
  --cart-article-bg-color: var(--work-light);
  --table-dt-bg-color: var(--light);
  --table-color: var(--dark);
  --table-bg-color: var(--light);
  --table-border-color: var(--work-medium);
  --table-thead-color: var(--setcolor3);
  --table-thead-bg-color: var(--light);
  --table-tfoot-color: var(--setcolor3);
  --table-tfoot-bg-color: var(--setcolor0);
  --form-color: var(--dark);
  --form-bg-color: var(--light);
  --form-border-color: transparent;
  --form-focus-color: initial;
  --form-focus-bg-color: var(--work-light);
  --form-focus-border-color: var(--link-color);
  --form-select-bg-color: var(--work-dark);
  --profirat-color: var(--cart-color);
  --profirat-bg-color: var(--cart-bg-color);
  --profirat-border-color: var(--cart-border-color);
  --profirat-link-color: var(--link-color);
  --profirat-link-hover-color: var(--link-hover-color);
  
  --sample-disabled: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAIklEQVQIW2NkQAKrVq36zwjjgzhhYWGMYAEYB8RmROaABADeOQ8CXl/xfgAAAABJRU5ErkJggg==);

  --icon-menu: '\f0c9';
  --icon-login: '\e818';
  --icon-logout: '\e819';
  --icon-key: '\e80a';
  --icon-ok: '\e80b';
  --icon-gauge: '\f0e4';
  --icon-calendar: '\e808';
  --icon-money: '\f0d6';
  --icon-exchange: '\f0ec';
  --icon-download-cloud: '\f0ed';
  --icon-bed: '\f236';
  --icon-cog: '\e817';
  --icon-left-open: '\e824';
  --icon-right-open: '\e825';
  --icon-edit: '\e804';
  --icon-picture: '\e805';
  --icon-trash-empty: '\e816';
  --icon-sort-1: '\e82b';
  --icon-sort-down: '\f0dd';
  --icon-sort-up: '\f0de';
  --icon-cancel-circled: '\e80c';
  --icon-plus-circled: '\e80d';
  --icon-cancel: "\e823";
  --icon-resize-full: '\e81a';
  --icon-resize-small: '\e81b';
  --icon-upload: '\e81e';
  --icon-arrows-cw: '\e822';
  --icon-blocked: '\e82a';
  --icon-plus: '\e82c';
  --icon-angle-double-down: '\f103';
  --icon-spinner: "\f110";
  --icon-percent: "\f295";
  --icon-food: "\f0f5";
  --icon-mail-alt: "\f0e0";
  --icon-star: "\e826";
  --icon-globe: "\e82d";
  --icon-android: "\e82e";
  --icon-tags: "\e82f";
  --icon-keyboard: '\f11c';
  --icon-stackoverflow: "\f16c";
  --icon-file-pdf: "\f1c1";
  --icon-sliders: "\f1de";
  --icon-clone: "\f24d";
  --icon-bot: "\f281";
  --icon-help: "\e811";
}

@font-face{
  font-family: "FontIcon";
  font-display: fallback;
  src: url("/items/fonticon.eot?") format("eot"), url("/items/fonticon.woff2") format("woff2"), url("/items/fonticon.woff") format("woff"), url("/items/fonticon.ttf") format("truetype"), url("/items/fonticon.svg#FontIcon") format("svg");
}