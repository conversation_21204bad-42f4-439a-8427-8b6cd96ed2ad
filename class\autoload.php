<?php
spl_autoload_register( function( $class ){
  if( file_exists( 'shared/class/system_'.$class.( ( CLASSVERSIONS[$class] ?? 0 )? '_'.CLASSVERSIONS[$class] : '').'.php' ) ){
    require_once 'shared/class/system_'.$class.( ( CLASSVERSIONS[$class] ?? 0 )? '_'.CLASSVERSIONS[$class] : '').'.php';
    if( method_exists( $class, 'init' ) )
      $class::init();
  }elseif( file_exists( '../class/system_'.$class.( ( CLASSVERSIONS[$class] ?? 0 )? '_'.CLASSVERSIONS[$class] : '').'.php' ) ){
    require_once '../class/system_'.$class.( ( CLASSVERSIONS[$class] ?? 0 )? '_'.CLASSVERSIONS[$class] : '').'.php';
    if( method_exists( $class, 'init' ) )
      $class::init();
  }elseif( file_exists( 'model_'.strtolower( $class ).'.php' ) )
    require_once 'model_'.strtolower( $class ).'.php';
  else{
    // navOnlineInvoice
    $className = ltrim( $class, '\\' );
    $file  = 'shared/app/';
    $namespace = '';
    if( $lastNsPos = strrpos( $className, '\\' ) ){
      $namespace = substr( $className, 0, $lastNsPos );
      $className = substr( $className, $lastNsPos + 1 );
      $file .= str_replace( '\\', DIRECTORY_SEPARATOR, $namespace ) . DIRECTORY_SEPARATOR;
    }
    $file .= str_replace( '_', DIRECTORY_SEPARATOR, $className ) . '.php';
    if( file_exists( $file ) )
      require( $file );
    else{
      // PSR-4 mpdf
      $prefix = 'Mpdf\\PsrLogAwareTrait\\';
      $base_dir ='shared/app/mpdf/src/';
      $len = strlen( $prefix );
      if( strncmp( $prefix, $class, $len ) === 0 ){
        $relative_class = substr( $class, $len );
        $file = $base_dir . str_replace( '\\', '/', $relative_class ) . '.php';
        if( file_exists( $file ) )
          require $file;
      }else{
        // PSR-4 mpdf
        $prefix = 'Mpdf\\';
        $base_dir = 'shared/app/mpdf/src/';
        $len = strlen( $prefix );
        if( strncmp( $prefix, $class, $len ) === 0 ){
          $relative_class = substr( $class, $len );
          $file = $base_dir . str_replace( '\\', '/', $relative_class ) . '.php';
          if( file_exists( $file ) )
            require $file;
        }else{
          // PSR-4 mpdf
          $prefix = 'setasign\\Fpdi\\';
          $base_dir = 'shared/app/setasign/src/';
          $len = strlen( $prefix );
          if( strncmp( $prefix, $class, $len ) === 0 ){
            $relative_class = substr( $class, $len );
            $file = $base_dir . str_replace( '\\', '/', $relative_class ) . '.php';
            if( file_exists( $file ) )
              require $file;
          }else{
            // PSR-4 mpdf
            $prefix = 'DeepCopy\\';
            $base_dir = 'shared/app/deepcopy/src/DeepCopy/';
            $len = strlen( $prefix );
            if( strncmp( $prefix, $class, $len ) === 0 ){
              $relative_class = substr( $class, $len );
              $file = $base_dir . str_replace( '\\', '/', $relative_class ) . '.php';
              if( file_exists( $file ) )
                require $file;
            }else{
              // PSR-4 mpdf
              $prefix = 'Psr\\Http\\Message\\';
              $base_dir = 'shared/app/psr/src/';
              $len = strlen( $prefix );
              if( strncmp( $prefix, $class, $len ) === 0 ){
                $relative_class = substr( $class, $len );
                $file = $base_dir . str_replace( '\\', '/', $relative_class ) . '.php';
                if( file_exists( $file ) )
                  require $file;
              }
              else{
                // PSR-4 mpdf
                $prefix = 'Psr\\Log\\';
                $base_dir = 'shared/app/psr/src/';
                $len = strlen( $prefix );
                if( strncmp( $prefix, $class, $len ) === 0 ){
                  $relative_class = substr( $class, $len );
                  $file = $base_dir . str_replace( '\\', '/', $relative_class ) . '.php';
                  if( file_exists( $file ) )
                    require $file;
                }
              }
            }
          }
        }
      }
    }
  }
} );