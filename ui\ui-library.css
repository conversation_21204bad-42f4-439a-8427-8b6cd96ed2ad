@charset "UTF-8";

[data-library]{
  display: flex;
  flex-flow: row wrap;
  align-content: start;
  gap: clamp(.875rem, 1.4vw, 1.125rem);
  &>div{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    max-width: calc(20% - 4 * clamp(.875rem, 1.4vw, 1.125rem) / 5);
    min-width: 79px;
    width: 100%;
    &.only-one-image{ max-width: 100% }
    &>a{
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      width: 1.5rem;
      height: 1.5rem;
      top: 2px;
      left: 2px;
      font-size: 1rem;
      background-color: var(--form-bg-color);
      border: 1px solid var(--form-color);
    }
    &.over{ border: 3px dotted red }
  }
  & .icon:before{ padding: 3px }
}