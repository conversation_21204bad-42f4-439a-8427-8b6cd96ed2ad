<?php
if( !( $_POST['mettol'] ?? 0 and $_POST['mettol'] != '' ) ) $_POST['mettol'] = date('Y-01-01');
if( !( $_POST['meddig'] ?? 0 and $_POST['meddig'] != '' ) ) $_POST['meddig'] = date('Y-12-31');
if( !( $_POST['egyseg'] ?? 0 and $_POST['egyseg'] != '' ) ) $_POST['egyseg'] = 'H';
$statlist = false;

if( $foglalasok = db::list_foglalstat( $_SESSION['COMPANY'], $_POST['mettol'] ) ){
  $fid = $napok = 0;
  $nap = $stat = [];
  foreach( $foglalasok as $foglalas ){
    if( $fid != $foglalas['foglalas_id'] ){
      if( $fid ){
        $napifiz = round( $fizetendo / $napok );
        foreach( $nap as $key => $value ){
          $stat[$key] = $stat[$key] ?? $stat[$key] = ['ej' => 0, 'fiz' => 0];
          $stat[$key]['ej'] += $nap[$key]['ej'];
          $stat[$key]['fiz'] += $napifiz;
        }
        $napok = 0;$nap = [];
      }
      $fid = $foglalas['foglalas_id'];
      $fizetendo = $foglalas['fizetendo'];
      $csatorna_id = $foglalas['csatorna_id'];
    }
    $napok += $foglalas['napok'];
    $i = 0;
    while( $foglalas['napok'] > $i ){
      $nap[date( 'Y-m-d', strtotime( $foglalas['erkezes'].' '.$i.' day' ) )] = $nap[date( 'Y-m-d', strtotime( $foglalas['erkezes'].' '.$i.' day' ) )] ?? $nap[date( 'Y-m-d', strtotime( $foglalas['erkezes'].' '.$i.' day' ) )] = ['ej' => 0];
      $nap[date( 'Y-m-d', strtotime( $foglalas['erkezes'].' '.$i.' day' ) )]['ej']++;
      $i++;
    }
  }
  ksort( $stat );
  $nap = $_POST['mettol'];
  while( $nap <= $_POST['meddig'] ){
    $stat[$nap]['maxej'] = 6;
    $stat[$nap]['maxfiz'] = 24000+26000+28000+28000+26000+37000;
    $nap = date( 'Y-m-d', strtotime( $nap.' 1 day' ) );
  }
  ksort( $stat );
  
  foreach( $stat as $nap => $adat ){
    if( $nap >= $_POST['mettol'] and $nap <= $_POST['meddig'] ){
      switch( $_POST['egyseg'] ){
        case 'N': $egyseg = $nap; break;
        case '7': $egyseg = date( 'W', strtotime( $nap ) ); break;
        case 'H': $egyseg = date( 'm', strtotime( $nap ) ); break;
      }
      if( !$statlist or !( $statlist[$egyseg] ?? 0 ) )
        $statlist[$egyseg] = ['ej' => 0, 'fiz' => 0, 'maxej' => 0, 'maxfiz' => 0];
      $statlist[$egyseg]['ej'] += $adat['ej'] ?? 0;
      $statlist[$egyseg]['fiz'] += $adat['fiz'] ?? 0;
      $statlist[$egyseg]['maxej'] += $adat['maxej'];
      $statlist[$egyseg]['maxfiz'] += $adat['maxfiz'];
    }
  }
}
response::add( 'view', 'statlist', $statlist );

$dashboard = [];
if( $report = db::list_report( $_SESSION['COMPANY'], $_POST['mettol'], $_POST['meddig'] ) ){
  $dashboard['channels'] = [];
  $dashboard['nights_booked'] = 0;
  $dashboard['total_revenue'] = 0;
  $dashboard['total_paid'] = 0;
  foreach( $report as $row ){
    if( $dashboard['channels'][$row['name']] ?? 0 )
      $dashboard['channels'][$row['name']] += $row['ej'];
    else
      $dashboard['channels'][$row['name']] = $row['ej'];
    $dashboard['nights_booked'] += $row['ej'];
    $dashboard['total_revenue'] += $row['revenue'];
    $dashboard['total_paid'] += $row['paid'];
  }
  arsort( $dashboard['channels'] );
}

$total = db::get_total_night( $_SESSION['COMPANY'], $_POST['mettol'], $_POST['meddig'] );
$dashboard['opened'] = $total['opened'];
$dashboard['closed'] = $total['closed'];
$dashboard['total_nights'] = $total['opened'] - $total['closed'];

response::add( 'view', 'dashboard', $dashboard );

$maerkezik = db::list_foglalErkez( date('Y-m-d') );
response::add( 'view', 'maerkezik', $maerkezik );
$holnaperkezik = db::list_foglalErkez( date( 'Y-m-d', strtotime( '+1day' ) ) );
response::add( 'view', 'holnaperkezik', $holnaperkezik );
$matavozik = db::list_foglalTavoz( date('Y-m-d') );
response::add( 'view', 'matavozik', $matavozik );
$holnaptavozik = db::list_foglalTavoz( date( 'Y-m-d', strtotime( '+1day' ) ) );
response::add( 'view', 'holnaptavozik', $holnaptavozik );
$mafoglalt = db::list_foglalFoglal( date('Y-m-d') );
response::add( 'view', 'mafoglalt', $mafoglalt );
$tegnapfoglalt = db::list_foglalFoglal( date( 'Y-m-d', strtotime( '-1day' ) ) );
response::add( 'view', 'tegnapfoglalt', $tegnapfoglalt );

function table_maerkezik(){
  return db::list_foglalErkez( date( 'Y-m-d' ) );
}
function table_holnaperkezik(){
  return db::list_foglalErkez( date( 'Y-m-d', strtotime( '+1day' ) ) );
}
function table_matavozik(){
  return db::list_foglalTavoz( date( 'Y-m-d' ) );
}
function table_holnaptavozik(){
  return db::list_foglalTavoz( date( 'Y-m-d', strtotime( '+1day' ) ) );
}
/*
function table_mafoglal(){
  return db::list_foglalFoglal( date( 'Y-m-d' ) );
}
function table_tegnapfoglal(){
  return db::list_foglalFoglal( date( 'Y-m-d', strtotime( '-1day' ) ) );
}
*/