        <h1>TREN a felhőalapú szálláskezelő szoftver</h1>
        <p>
          Szoftverünk felhasználóbarát felületet biztosít a kis és közepes méretű szállásadók számára. <PERSON><PERSON><PERSON><PERSON>, informatikai tudás nélkül is használható. A TREN felhőalapú szálláskezelő rends<PERSON> bárhonnan, bármikor és bármilyen okos eszközön elérhető. Számtalan hasznos funkcióval és megoldással támogatjuk a munkát. A szállás valós idejű vezetése még sosem volt ilyen egyszerű!
        </p>
        <h2>ALL-IN-ONE Minden egyben</h2>
        <p>
          Azaz egy olyan eszközt szeretnénk biztosítani, amely a teljes folyamat minden lépésére kínál megoldást!<br>
          Mindenkori technikai színvonalnak megfelelő rendszer áll rendelkezésre, a fejlesztést folyamatosan végezzük és a szállásadók visszajelzéseit is figyelembe vesszük.
        </p>
        <h2>Funkciók és megoldások</h2>

        <div class="carts">
          <div class="cart">
            <h3>Foglalások</h3>
            <p>Több forrásból is érkezhetnek a foglalások. A kézi bevitelen kívűl automatikus a foglalások rögzítése</p>
            <ul>
              <li>Manuális adatbevitel</li>
              <li>Saját honlap foglalási felület</li>
              <li>Más oldalon elhelyezett foglalási felület</li>
              <li>Foglalási oldalakról</li>
            </ul>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Szobatükör, naptár</h3>
            <p>
              A TREN naptár egy központi felületről biztosítja, hogy a napi teendők a lehető legegyszerűbben elvégezhetők legyenek. Egyéni és csoportos foglalások kezelése, egyszerű foglalás mozgatás szobák között, gyors bejelentkezés és kijelentkezés, lakóegység elérhetőség lezárása, kommunikáció, vendégadatok, napi adminisztratív feladatok nyilvántartása, egészen a számlázásig.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Lakóegységek és árazás</h3>
            <p>
              Több szálláshely, több lakóegysék egy felületen.Teljes rugalmasság az árazásban. Árazás időszakra, napra, minimum éj és érkezési napok szabályozása, kiemelt időszakok kezelése. Vendégszámtól függő beállítások.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Szinkronizálás, channel manager</h3>
            <p>
              A Channel Manager megoldásunk segítségével minden új foglalás, lemondás és változás automatikusan megérkezik. Fogadjuk a foglalásokat online szállásközvetítő oldalról vagy a foglalómotorról.
              Automatikusan összehangoljuk az elérhetőséget, átadjuk a foglaltsági adatokat a foglaló oldalaknak. Booking.com, Szallas.hu, Airbnb.com és bármilyen kommunikációra képes foglalási oldalnak.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Pénzügyek</h3>
            <p>
              Előlegek, szállásdíj fizetések és visszafizetések kezelése, tartozások és határidők figyelése, visszaigazolások és felszólytások küldése.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Számlázás, NAV</h3>
            <p>
              Egyszerűsítjük és automatizáljuk a számla kiállítás folyamatát. Az előlegszámla, díjbekérő kezelés és a végszámlák készítése kifelyezetten a szállás-szolgáltatásra optimalizáltak. IFA átvételi igazolása és NAV megfelelés és adatküldés biztosított.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Kommunikáció, üzenetek</h3>
            <p>
              Kezeld a vendégkommunikációt egy központi felületen. Automatikus és manuális üzenetek összeállítása, sablonok készítése, automatizmus beállítása és eseményhez kötése.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Marketing</h3>
            <p>
              Csomagajánlatok, szezonális kampányok összeállítása és a saját honlapon vagy tematikus oldalakon való megjelenítése. Témák: Karácsony, szilveszter, valentinnap, farsang, husvét, pünkösd, wellness, lastminute, firstminute stb.   
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>Honlap, foglalási ürlap</h3>
            <p>
              Saját honlap testreszabása. Főoldali megjelenés, lakóegység listák, galéria és további oldalak beálltása, kezelése. Foglalási ürlap, foglaltsági térkép beállítása saját igények szerint. A fogglalási ürlap bármilyen más weboldalba is beilleszthető. Közvetlen, direkt foglalás jutalék mentes, a leggazdaságosabb ügyfélszerzés. API megoldásunkkal valóban bármilyen kinézetet adhat szálláshelyének. Álmodja meg a látványt és élvezze a rendszerünk kényelmét. Honlaphoz választott .hu domainnevet, SSL szolgáltatást és tárhelyet biztosítunk.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>GDPR, ÁSZF, Házirend</h3>
            <p>
              Törvényi szabályozásokat követve biztosított dokumentumkezelés, szerződés és tájékoztató mintatár. Honlapjába beilleszthető és interaktív adatkezelési tályékoztató, süti és hozzájárulás kezelés. GDPR nyilvántartások automatizált vezetése. Adatkezelési szabályzat,ászf és háziren készítés és a törvénynek megfelelő verzió kezelés. Bármilyen vendégek tályékoztatására szolgáló dokumentum készítése és tárolása nyomtatási lehetőséggel is.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>NTAK, VISA</h3>
            <p>
              Kötelező vendég adat szolgáltatás biztosítása. Napi zárás utáni adatküldés nehézségeinek megoldása ként a rendszer automatikusan addig próbálkozik amíg az adatszolgáltatás nem teljesül.
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>
          <div class="cart">
            <h3>AI, kimutatások, elemzések</h3>
            <p>
              Minél alaposabban ismered a vendégeid, annál magasabb színvonalú szolgáltatást nyújthatsz, annál több bevételt rhetsz el.<br>
              Mesterséges inteligenciával támogatott adatelemzést végzük, melynek segítségével kimutatásokat biztosítunk a munkához és segítségével növelhető a hatékonyság.  
            </p>
            <?php /* <i style="--icon:var(--icon-key)"></i><?php */ ?>
          </div>          
        </div>
