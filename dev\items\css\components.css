@charset "UTF-8";
/* Components - Komponensek beállításai */

.formbox{
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  gap: clamp(.25rem, 4%, 1rem);
  & .form{
    position: relative;
    line-height: 100%;
    &>textarea:not([id^="jodit"]) + label,
    &>select + label,
    &>input:not([type="checkbox"], [type="radio"]) + label{
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      padding: 1rem 0.8rem 0;
      pointer-events: none;
      border: 1px solid transparent;
      transform-origin: 0 0;
      transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
      color: var(--link-color, initial);
      background-color: transparent;
    }
    &:has(textarea[id^="jodit"]){ padding-top: 1.6rem }
    &>textarea[id^="jodit"] + label{
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      padding: 0 0.8rem 0;
      pointer-events: none;
      border: 1px solid transparent;
      color: var(--link-color, initial);
    }
    &>div.help{
      display: none;
      position: absolute;
      pointer-events: none;
      border: 1px solid transparent;
    }
  }
}

input,
select,
textarea{
  border: 1px solid var(--form-border-color, transparent);
  border-radius: .5rem;
  background-color: var(--form-bg-color, initial);
  color: var(--form-color, initial);
}

input[type="submit"],
::file-selector-button, /* fájlfeltöltés gombja */
button,
.btn{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 3rem;
  padding: .5rem 1rem;
  border: 1px solid transparent;
  border-radius: .5rem;
  /* vertical-align: middle; */
  inline-size: fit-content;
  touch-action: manipulation; /* dupla kattintás és más telefonos téves eseméníek letiltása */
  user-select: none; /* kiválasztás letiltása téves esemény miatt */
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  background-color: var(--btn-bg-color);
  color: var(--btn-color);
  &:focus,
  &:hover{
    background-color: var(--btn-hover-bg-color);
    text-decoration: none;
  }
  &:before{ margin-right: .25rem }
}

textarea,
select,
input[type=text],
input[type=number],
input[type=search],
input[type=email],
input[type=password],
input[type=date]{
  width: 100%;
  height: 3rem;
  padding-top: 1rem;
  text-indent: 0.8rem;
  &:focus{
    border-color: var(--form-focus-border-color, transparent);
    background-color: var(--form-focus-bg-color, initial);
    color: var(--form-focus-color, initial);
    & ~ label{
      opacity: 0.8;
      transform: scale(0.9) translateY(-0.65rem) translateX(0.1rem);
    }
  }
  &:not(:placeholder-shown) ~ label{
    opacity: 0.8;
    transform: scale(0.9) translateY(-0.65rem) translateX(0.1rem);
  }
  &:placeholder-shown ~ label:after{
    content: attr(:placeholder)
  }
}

select{
  & option:nth-child(even){ background-color: var(--form-select-bg-color, initial) }
  &::-ms-expand{ display: none }
}

textarea{
  height: 10rem;
  &:focus{
    position: inherit;
    z-index: 1;
  }
}

[style^="--icon:"]:before,
[style*=" --icon:"]:before{
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: FontIcon;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  content: var(--icon);
}

[style^="--toicon:"]{
  &::after{
    content: var(--toicon);
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    padding: 1em .5em;
    border-radius: 0 .5rem .5rem 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: FontIcon;
    font-style: normal;
    font-weight: normal;
    pointer-events:none;
    background-color: var(--form-select-bg-color);
  }
  &>div{
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 2em;
    border-radius: 0 .5rem .5rem 0;
    cursor: pointer;
  }
}

input{
  &[type=checkbox],
  &[type=radio]{
    display: none;
    & + label{
      display: inline-block;
      position: relative;
      height: 3rem; /* 3rem - 4px */
      line-height: 3rem;
      cursor: pointer;
    }
  }
  &[type=number]{ appearance: textfield }
  &:user-invalid{
    animation: shake 300ms;
    border-color: var(--form-invalid-color, red) !important;
  }
  @supports not selector(:user-invalid){
    &:invalid{
      animation: shake 300ms;
      border-color: var(--form-invalid-color, red) !important;
    }
  }
}

.input-message,
.tooltip{
  display: none;
  min-width: 220px;
  width: fit-content;
  text-align: left;
  padding: .2rem 0 0 .8rem;
  &:has( .error:not( [hidden] )){ display: inline-block }
}
[data-tooltip]{
  &:not( [data-tooltip="line"] ){
    & > .tooltip{
      z-index: calc(infinity);
      position: absolute;
      max-width: 300px;
      padding: 10px;
      border-radius:4px;
      background:#8ad92e;
      color:#333;
      box-shadow: 5px 5px 8px #888;
    }
    & [data-tooltip="top"]{
      & > .tooltip{
        top: -3rem;
        &:after{
          top:auto;
          bottom:0;
          left:15px;
          margin-bottom:-8px;
          border-top: 8px solid #8ad92e;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom:0;
        }
      }
    }
  }
  & > input:focus ~ .tooltip{ display: inline-block }
  & > input:not(:focus) ~ .tooltip > .info{ display: none }
}

.toggle-buttons{
  height: 3rem;
  & > input[type=hidden]{
    & ~ div{
      display: flex;
      flex-flow: row nowrap;
      width: 100%;
      height: 100%;
      padding-top: 1.4rem;
      border-radius: .5rem;
      border: 1px solid var(--form-border-color, transparent);
      background-color: var(--form-bg-color, initial);
      color: var(--form-color, initial);
      & > span{
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        &:first-child{ border-radius: 0 0 0 .5rem }
        &:last-child{ border-radius: 0 0 .5rem 0 }
        &:hover{ border: 1px solid var(--form-focus-border-color, #000) }
      }
    }
    & ~ label{
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      padding: 1rem 0.8rem 0;
      pointer-events: none;
      border: 1px solid transparent;
      transform-origin: 0 0;
      transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
      color: var(--link-color, initial);
      background-color: transparent;
      opacity: 0.8;
      transform: scale(0.9) translateY(-0.65rem) translateX(0.1rem);
    }
  }
}

.toggle{
  height: 3rem;
  & > input[type=checkbox]{
    position: absolute;
    display: none;
    & ~ label{
      width: 100%;
      border-radius: .5rem;
      border: 1px solid var(--form-focus-border-color, transparent);
      background-color: var(--form-bg-color, initial);
      color: var(--form-color, initial);
      & > span{
        display: inline-block;
        text-align: center;
        width: 50%;
        height: 100%;
        background-color: var(--cart-bg-color, initial);
        &:first-child{
          filter:blur(0);
          background-color: var(--btn-bg-color, initial);  
          border-radius: .5rem 0 0 .5rem;
        }
        &:last-child{
          filter:blur(1.5px);
          background-color: initial;
          border-radius: 0 .5rem .5rem 0;
          &:hover{
            background-color: var(--form-focus-bg-color, initial);
            filter:blur(0);
          }
        }
      }
    }
    &:checked ~ label{
      & > span{
        &:last-child{
          filter: blur(0);
          background-color: var(--btn-bg-color, initial);
        }
        &:first-child{
          filter:blur(1.5px);
          background-color: initial;
          &:hover{
            background-color: var(--form-focus-bg-color, initial);
            filter: blur(0);
          }
        }
      }
    }
    
  }
}
.checen input + label{
  width: calc(50% + 1.5rem);
  &::after{
    right: 0;
    position: absolute;
    text-align: center;
    font-family: FontIcon;
    font-weight: 500;
    pointer-events: none;
    font-size: var(--size-font);
    vertical-align: middle;
    content: "";
    display: inline-block;
    width: 3rem;
    height: 3rem;
    border: 1px solid var(--form-border-color);
    background-color: var(--form-bg-color);
  }
}
.cheaft input + label{
  padding-right:  3.5em;
  margin-left:  0.8em;
  &::after{
    right: 0;
    position: absolute;
    text-align: center;
    font-family: FontIcon;
    font-weight: 500;
    pointer-events: none;
    font-size: var(--size-font);
    vertical-align: middle;
    content: "";
    display: inline-block;
    width: 3rem;
    height: 3rem;
    border: 1px solid var(--form-border-color);
    background-color: var(--form-bg-color);
  }
}
.chebef input + label{
  padding-left: 3.5em;
  &::before{
    left: 0;
    position: absolute;
    text-align: center;
    font-family: FontIcon;
    font-weight: 500;
    pointer-events: none;
    font-size: var(--size-font);
    vertical-align: middle;
    content: "";
    display: inline-block;
    width: 3rem;
    height: 3rem;
    border: 1px solid var(--form-border-color);
    background-color: var(--form-bg-color);
  }
}
.checen input:checked + label:after,
.chebef input:checked + label:before,
.cheaft input:checked + label:after{
  content: var(--icon-ok);
  color: var(--form-color);
}
[data-to],
[data-on]{
  display: inline-block;
  &:before,
  &:after{
    position: absolute;
    top: 0;
    text-align: center;
    font-family: FontIcon;
    font-weight: 500;
    pointer-events: none;
    font-size: 1rem;
    vertical-align: middle;
    width: 3rem;
    height: 3rem;
    line-height: 3rem;
  }
}
[data-on]{  
  &:before{
    content:attr(data-on);
    left: 0;
  }
  &>input{ padding-left: 3rem }
}
[data-to]{
  &:after{
    content:attr(data-to);
    right: 0;
  }
  &>input{ padding-right: 3rem }
}

.h2on[data-on]:before{ width: calc(1.5 * 3rem) }
.h2on[data-on]>input{ padding-left: calc(1.5 * 3rem) }

.disabled,
input[type=text]:read-only,
:disabled{ background: var(--sample-disabled) repeat }

@keyframes shake{
  25%{ transform: translateX( 4px ) }
  50%{ transform: translateX( -4px ) }
  75%{ transform: translateX( 4px ) }
}

.cart{
  display: inline-block;
  min-width: 320px;
  padding: .5rem;
  border-radius: .5rem;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--cart-bg-color);
  color: var(--cart-color);
  & header{
    display: flex;
    justify-content: space-between;
    min-height: 2rem;
    &>h5,
    &>h6{
      font-weight: 600;
      display: inline-block;
      font-size: 1rem;
      text-overflow: ellipsis;
    }
  }
  &>article{
    padding: .5rem;
    margin-top: .5rem;
    border-radius: .5rem;
    background-color: var(--cart-article-bg-color);
  }
}

[data-datatable]{
  border-radius: .25rem;
  background-color: var(--table-dt-bg-color, initial);
  & .controls{
    display: flex;
    justify-content: space-between;
    margin-bottom: .5rem;
    & form.search{ width: fit-content }
  }
  &>div.tablescroll{
    overflow: auto;
    scroll-behavior: smooth;
    scroll-snap-type: inline mandatory;
  }
}

table{
  width: 100%;
  margin: 0 auto;
  border-collapse: separate;
  border-spacing: 0 .25rem;
  border-radius: .25rem;
  background-color: var(--table-bg-color);
  color: var(--table-color);
  &>thead{
    &>tr{
      &>th{
        height: clamp( 56px, 3rem , 70px );
        padding: 0 .4em;
        border-bottom: 1px solid rgba(0, 0, 0, 0.3);
        scroll-snap-align: center;
        position: sticky;
        top: 0px;
        background-color: var(--table-thead-bg-color);
        color: var(--table-thead-color);
        &[data-order]{
          cursor: pointer;
          &>i{
            &::before{ margin-left: .25rem }
          }
          &.desc>i::before{ content: var(--icon-sort-down) }
          &.asc>i::before{ content: var(--icon-sort-up) }
        }
        &.celfix{
          left: 0px;
          z-index: 1;
        }
      }
    }
  }
  &>tbody{
    &>tr{
      &>td{
        /*height: max( 3rem , 68px );*/
        padding: 0 .4em;
        border-spacing: .25rem;
        box-sizing: content-box;
        scroll-snap-align: center;
        &>i[style*="--icon"],
        &>a[style*="--icon"]{
          display: table-cell;
          text-align: center;
          vertical-align: middle;
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          background-color: var(--primary);
          &:before{ color: var(--dark) }
          &:hover{ background-color: var(--secondary) }
        }
      }
      &:last-child{
        &>td{ border-bottom: 1px solid rgba(0, 0, 0, 0.3) }
      }
      & .colfix{
        position: sticky;
        left: 0px;
      }
    }
  }
}

.pagination{
  display: flex;
  justify-content: space-between;
  width: 100%;
  &>ul{
    display: inline-table;
    &>li{
      display: table-cell;
      padding: .25rem .5rem;
      &>select{
        min-width: 4rem;
        height: 100%;
        padding-top: 0;
      }
    }
  }
}

html{
  scrollbar-gutter: stable;
  &>body {
    &:has(dialog[open]){ overflow: hidden }
  }
}
.dialog-background{
  animation: fadeIn 1s ease both;
  background: rgb(255 255 255 / 40%);
  backdrop-filter: blur(4px);
}
@keyframes fadeIn {
  from{ opacity: 0 }
  to{ opacity: 1 }
}

dialog{
  min-width: 280px;
  margin-top: 5dvh;
  margin-inline: auto;
  border-radius: .5rem;
  &.minimize{
    z-index: 100;
    position: fixed;
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    /*height: 100%;*/
    margin: 0;
    border-radius: 0;
    border: none;
    overflow: auto;
  }
  & header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.2rem;
    padding: 0 1.5rem;
    min-height: 4rem;
    & h6{
      margin: 0;
      font-size: 1.2rem;
    }
    & div{
      display: flex;
      justify-content: space-between;
      gap: .5rem;
    }
  }
  
  & section{
    overflow: auto;
    max-height: calc( 90dvh - 8rem );
    padding: 0.6rem 1.5rem;
  }
  
  & footer{
    display: flex;
    justify-content: flex-end;
    min-height: 4rem;
    padding: .5rem;
    gap: .5rem;
    &  button{
      text-transform: uppercase;
    }
  }

  &[data-dialog="status-error"] > div > section{
    background-color: var(--danger-color);
    color: var(--bg-color);
  }
  &[data-dialog="status-success"] > div > section{
    background-color: var(--success-color);
    color: var(--bg-color);
  }
  &[data-dialog="status-warning"] > div > section{
    background-color: var(--warning-color);
    color: var(--bg-color);
  }
  &[data-dialog="status-info"] > div > section{
    background-color: var(--info-color);
    color: var(--bg-color);
  }
}

.col1{ max-width: calc(10% - .9rem); width: calc(10% - .9rem) }
.col2{ max-width: calc(20% - .8rem); width: calc(20% - .8rem) }
.col3{ max-width: calc(30% - .7rem); width: calc(30% - .7rem) }
.col4{ max-width: calc(40% - .6rem); width: calc(40% - .6rem) }
.col5{ max-width: calc(50% - .5rem); width: calc(50% - .5rem) }
.col6{ max-width: calc(60% - .4rem); width: calc(60% - .4rem) }
.col7{ max-width: calc(70% - .3rem); width: calc(70% - .3rem) }
.col8{ max-width: calc(80% - .2rem); width: calc(80% - .2rem) }
.col9{ max-width: calc(90% - .1rem); width: calc(90% - .1rem) }
.col0{ max-width: 100%; width: 100% }
.col15{ max-width: calc(15% - .85rem); width: calc(15% - .85rem) }
.col25{ max-width: calc(25% - .75rem); width: calc(25% - .75rem) }
.col33{ max-width: calc(33.3% - .66rem); width: calc(33.3% - .66rem) }
.col35{ max-width: calc(35% - .65rem); width: calc(35% - .65rem) }
.col45{ max-width: calc(45% - .55rem); width: calc(45% - .55rem) }
.col55{ max-width: calc(55% - .45rem); width: calc(55% - .45rem) }
.col66{ max-width: calc(66.6% - .33rem); width: calc(66.6% - .33rem) }
.col75{ max-width: calc(75% - .25rem); width: calc(75% - .25rem) }
