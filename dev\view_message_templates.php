  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Üzenet sablonok</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új sablon</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?= table::datatable( 
          'messagetemplateslist',
          [ 'allapotstr' => ['th' => 'Állapot', 'status' => 'signs[0]'],
            'name' => ['th' => 'Megnevezés'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés::id',
                'trash-empty:event:Törlés:/messages/template/del/:id:confirm:where:db'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script src="/shared/js/jodit/jodit.min.js"></script>
  <link href="/shared/js/jodit/jodit.min.css" rel="stylesheet">
  <script type="module">
    import { datatable, dialog, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd, inputJE
      switch( ifunction ){
        case 'messagetemplateslist_edit':
          var editor = new Jodit( document.getElementById( 'joditeditor' ), { "toolbarButtonSize": "small" } )
        break;
        case 'messagetemplateslist_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_template' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/messagetemplate',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'A sablon módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break;
        case 'messagetemplateslist_edit_close':
          location.replace( '/message/templates' )
        break;
        case 'messagetemplateslist_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( {
            url: '/modal/messagetemplatedel',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/message/templates' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a sablon!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break;
      }
    }

    window.addEventListener('DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új sablon',
          label: 'Megnevezés'
        },
        eventManagerFunctions
      } )
    } )
  </script>