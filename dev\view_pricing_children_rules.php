  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5><PERSON><PERSON><PERSON><PERSON> s<PERSON>ok</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn" style="--icon:var(--icon-plus-circled)" href="/pricing/children_rules?create"> <PERSON><PERSON> sza<PERSON></a>
          <?php //} ?>
        </span>
      </header>
      <article>
        <?php if( response::$vw->view->children_rules ?? 0 ){ ?>
          <?php $age = 0; foreach( response::$vw->view->children_rules as $childrenRules ){ ?>  
            <form name="form_children_rules" method="post">
              <input type="hidden" name="id" value="<?= $childrenRules->id ?>">
              <ul class="formbox">
                <li class="form col5 bgonto" data-to="év">
                  <input type="text" placeholder="" value="<?= $age ?>" disabled>
                  <label>Kortól</label>
                </li>
                <li class="form col5 bgonto" data-to="év">
                  <input type="text" name="age" placeholder="" value="<?= $childrenRules->age ?? '17' ?>">
                  <label>Korig</label>
                </li>
                <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
                  <select name="is_price" placeholder="">
                    <option value="0"<?= ( isset( $childrenRules->price ))? '' : ' selected' ?>>Nem</option>
                    <option value="1"<?= ( isset( $childrenRules->price ))? ' selected' : '' ?>>Igen</option>
                  </select>
                  <label>Fogadjuk</label>
                </li>
                <li class="form col5 bgonto<?= ( isset( $childrenRules->price ))? '' : ' not-show' ?>" data-to="Ft/éj">
                  <input type="text" name="price" placeholder="" value="<?= $childrenRules->price ?? 0 ?>">
                  <label>Ár</label>
                </li>
                <li class="form col5<?= ( isset( $childrenRules->price ))? '' : ' not-show' ?>" style="--toicon:var(--icon-angle-double-down)">
                  <select name="is_cot" placeholder="">
                    <option value="0"<?= ( isset( $childrenRules->cot ))? '' : ' selected' ?>>Nem</option>
                    <option value="1"<?= ( isset( $childrenRules->cot ))? ' selected' : '' ?>>Igen</option>
                  </select>
                  <label>Gyerekágy biztosított</label>
                </li>
                <li class="form col5 bgonto<?= ( isset( $childrenRules->price ) and isset( $childrenRules->cot ))? '' : ' not-show' ?>" data-to="Ft/éj">
                  <input type="text" name="cot" placeholder="" value="<?= $childrenRules->cot ?? 0 ?>">
                  <label>Gyerekágy ár</label>
                </li>
                <li class="form col5<?= ( isset( $childrenRules->price ))? '' : ' not-show' ?>" style="--toicon:var(--icon-angle-double-down)">
                  <select name="is_extra_bed" placeholder="">
                    <option value="0"<?= ( isset( $childrenRules->extra_bed ))? '' : ' selected' ?>>Nem</option>
                    <option value="1"<?= ( isset( $childrenRules->extra_bed ))? ' selected' : '' ?>>Igen</option>
                  </select>
                  <label>Pótágy biztosított</label>
                </li>
                <li class="form col5 bgonto<?= ( isset( $childrenRules->price ) and isset( $childrenRules->extra_bed ))? '' : ' not-show' ?>" data-to="Ft/éj">
                  <input type="text" name="extra_bed" placeholder="" value="<?= $childrenRules->extra_bed ?? 0 ?>">
                  <label>Pótágy ár</label>
                </li>
                <li class="form col0">
                  <textarea name="children_rule_description"><?= html_entity_decode( setup::$company->configuration->children_rule_description ?? '')?></textarea>
                  <label>Gyermekekre vonatkozó szabályok</label>
                </li>
                <li class="form col0">
                  <input type="submit" name="btn_mentes" class="btn" value="Módosít">
                  <input type="submit" name="btn_torles" class="btn" value="Töröl">
                </li>
              </ul>
            </form>
          <?php } ?>
        <?php }else{ ?>
          Jelenleg nincsenek gyerek szabályok.
        <?php } ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { $, $$ } from '/shared/js/spritzer/index.js'

    window.addEventListener( 'DOMContentLoaded', () => {
      function resetNotShow( event ){
        event.target.closest( 'li' ).nextElementSibling.classList.toggle( 'not-show', event.target.value === '0' )
        if( event.target.closest( '[name="is_price"]' )){
          console.log('IS PRICE ' + event.target.value)
          if( event.target.value === '0' ){
            $( 'select[name="is_cot"]' ).closest( 'li' ).classList.add( 'not-show' )
            $( 'select[name="is_extra_bed"]' ).closest( 'li' ).classList.add( 'not-show' )
            $( 'select[name="is_cot"]' ).value = '0'
            $( 'select[name="is_extra_bed"]' ).value = '0'
            $( 'input[name="cot"]' ).closest( 'li' ).classList.add( 'not-show' )
            $( 'input[name="extra_bed"]' ).closest( 'li' ).classList.add( 'not-show' )
          }else{
            $( 'select[name="is_cot"]' ).closest( 'li' ).classList.remove( 'not-show' )
            $( 'select[name="is_extra_bed"]' ).closest( 'li' ).classList.remove( 'not-show' )
          }
        }
      }

      $$( 'select:is( [name="is_price"], [name="is_cot"], [name="is_extra_bed"] )' )
        .forEach( select => select.addEventListener( 'change', resetNotShow ))
    } )
  </script>