<?php
/**
 * Hitelesítés kezel<PERSON>e
 *
 * Hitelesítés és a hozzá kapcsolódó működési feltételek kezelése, hitelesítés, jogosultság
 * Szükséges
 *     class
 *   - [valid](@ref valid)
 *     session változók
 *   - $_SESSION[USER] felhasználó ID vagy 0
 *   - $_SESSION[USER_PROJECT] felhasználó projekt ID (régi modul)
 *   - $_SESSION[COMPANY] aktuális vállalkozás ID
 *   - $_SESSION[USER_RIGHTS] string felhasználó jogosultság 0 access_levels 0-n access_rights
 *   - $_SESSION[COMPANY_RIGHTS] string vállalkozás jogosultság 0-n service_rights
 *   - $_SESSION[REGCEGDB] hány cég van
 *   - $_SESSION[WSAK] websiteaudit kód (analitika)
 *   - $_SESSION[LNG] aktuális nyelv! (pl. hu, en, de ...)
 *
 * @method `get_project();`      Projekt beállítások és jogosultságok beolvasása
 * @method `control();`          Felhasználó ellenőrzése
 * @method `check_psw();`        Jelszó ellenőrzés
 * @method `create_psw();`       Jelszó generálás
 * @method `get_company_link();` Kiolvassa a CEG azonosítóját az url link vagy ceg_id alapján
 * @method `get_rights();`       Jogosultságok kiolvasása a céghez
 * @method `is_right();`        Jogosultság ellenőrzés
 *
 * <AUTHOR> Róbert <<EMAIL>>
 * @copyright Copyright (c) 2023, Tánczos Róbert
 * 
 * @version 2.0.1
 * @since 2.0.1 2024.02.13 Fizetési csomagok kezelése
 * @since 2.0.0 2023.07.25 Teljes áttérés az új adatbázis táblaszerkezetre
 * @since 1.1.0 2022.09.23 `is_right();` fügvény bevezetése
 * @since 1.0.0 2022.05.05 Új verzió követés. Új néven. Fájlnévben jelölve. Optimalizálás
 */

class setup extends model_basic{
  public static $project;
  public static $user;
  public static $user_project;
  public static $company;
  
  /**   * Alapértékek beállítása
   * @param integer $project_id projekt azonosító
   */
  
  public static function get_project( $project_id ){
    $_SESSION['USER'] = $_SESSION['USER'] ?? 0;
    if( $project = self::get( 'shared.projects', $project_id, OBJECT_TYPE )){
      $project->configuration = json_decode( $project->configuration ?? '{}' );
      $project->access_level = json_decode( $project->access_level ?? '[]' );
      $project->access_rights = json_decode( $project->access_rights ?? '[]' );
      $project->service_rights = json_decode( $project->service_rights ?? '[]' );
      self::$project = $project;
      if( !$_SESSION['USER'] )
        $_SESSION['USER_RIGHTS'] = '0' . str_repeat( '0', count( $project->access_rights ));
      $_SESSION['COMPANY_RIGHTS'] = str_repeat( '0', count( $project->service_rights ));
    }
  }

  /** Felhasználó ellenőrzése
   * projektből a beállítások: project->configuration->
   *   registration_method: 0 email/jelszó 1 email/nincs jelszó 2 email/jelszó/név 3 email/jelszó/név/becenév
   *   is_company: 0 nincs 1 van 2 több van 3 van de nem használt
   *   is_credit: 0 nincs 1 van általános 2 van belső
   *   email_confirmation: 0 nem 1 igen
   *   person_only: 0 igen 1 nem
   * @param string $to ha belépett hová ugorjon gyökértől meggadva pl: /rendszer/1
   */
  public static function control( $to = null, $csrf = false ){
    // Kijelentkezés
    if( http::$route[0] == 'logout' and $_SESSION['USER'] ){
      $_SESSION['USER'] = 0;
      unset( $_SESSION['USER_PROJECT'] );
      unset( $_SESSION['COMPANY'] );
      http::gourl();
    }
    //bejelentkezés / elfelejtett jelszó
    if( http::$route[0] == 'login' or http::$route[0] == 'forgotpwd' ){
      if( http::cleanPost( 'log' ) == 'in' and ( !$csrf or ( http::cleanPost( 'csrf_token' ) and http::csrfCheck()))){
        $_SESSION['USER'] = 0;
        if( $email = http::cleanPost( 'email', 'email' )){
          if( $user = self::get_userEmail( $email )){
            $user = (object) $user;
            // elfelejtett jelszó
            if( http::$route[0] == 'forgotpwd' ){
              /*
              $fejlec=""; //email fejléc összeállítás
              $fejlec.="MIME-Version: 1.0\n";
              $fejlec.="Content-Type: text/plain; charset=utf-8\n";
              $fejlec.="Errors-to: <EMAIL>\n";
              $fejlec.="From: ".$this->mailf."\n";
              $fejlec.="Return-Path: ".$this->mailf;
              mb_internal_encoding("UTF-8"); //email fejléc eddig
              mail($this->db->row['email'],mb_encode_mimeheader($this->t('Elfelejtett jelszó'), "UTF-8", "Q"),$this->t('jelszó').':'.$this->db->row['jelszoi'],$fejlec);
              */
            }else{
              /*
              if($this->reg_rekord->bonuszkelt<date('Y-m-d H:i') and $this->reg_rekord->bonusz>0){
                $this->reg_rekord->kredit -= $this->reg_rekord->bonusz;
                $this->reg_rekord->bonusz = 0;
                $this->db->query('UPDATE adat.reg SET kredit='.$this->reg_rekord->kredit.',bonusz=0 WHERE reg_id=?','i',array($this->reg_rekord->reg_id));
              }
              */
              self::check_psw( $user, $to );
            }
          }else response::add( 'error', 'Nem megfelelő email vagy jelszó!' );
        }else response::add( 'error', 'form', ['email' => 'Nem megfelelő emailcím!'] );
      }
      if( response::$vw->error ?? 0 ){
        self::save( 'shared.logbook', [['user_id', -1, 'i'],
                                       ['project_id', $_SESSION['PROID'], 'i'],
                                       ['user_name', $_POST['email']],
                                       ['password', $_POST['psw']],
                                       ['ip', $_SERVER['REMOTE_ADDR'] ?? ''],
                                       ['user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '']] );
      }
    }
    //regisztráció
    if( http::$route[0] == 'registration' ){
      if(( $_POST['reglepes'] ?? 0 ) and ( !$csrf or ( http::cleanPost( 'csrf_token' ) and http::csrfCheck()))){
        switch( http::cleanPost( 'reglepes' )){
          case 1:
            $_SESSION['USER'] = $_SESSION['USEROLD'] = 0;
            if( $email = http::cleanPost( 'email', 'email' )){
              if( $user = self::get_userEmail( $email )){
                $_SESSION['USEROLD'] = $user['id'];
                if( $user['password'] ?? 0 )
                  response::add( 'error', 'Ez az email cím már létezik!<br>Lépj be vele, vagy kérj jelszó emlékeztetőt!' );
                else{
                  $_POST['reglepes'] = 2;
                  $_SESSION['EMAILOLD'] = $email;
                }
              }
            }else response::add( 'error', 'Nem megfelelő email cím!' );
            if( !( response::$vw->error ?? 0 ) and $_POST['reglepes'] == 1 ){
              if( !$_SESSION['USEROLD'] ){
                if( $id = self::save( 'shared.users', [['email', $email]] )){
                  $_SESSION['USEROLD'] = $id;
                  if( self::$project->configuration->registration_method ?? 0 and self::$project->configuration->registration_method == 1 ){ // nincs jelszó
                    $_SESSION['USER'] = $_SESSION['USEROLD'];
                    unset( $_SESSION['USEROLD'] );
                    $_POST['reglepes'] = 3;
                  }else{
                    $_POST['reglepes'] = 2;
                    $_SESSION['EMAILOLD'] = $email;
                  }
                }else response::add( 'error', 'Nem sikerült a regisztráció!<br>Ügyfélszolgálat tud segíteni.' );
              }else response::add( 'error', 'Ismeretlen hiba!<br>Ügyfélszolgálat tud segíteni.' );
            }
          break;
          case 2:
            if( $email = http::cleanPost( 'email', 'email' )){
              if( $email == $_SESSION['EMAILOLD'] ){
                $psw = http::cleanPost( 'psw' );
                if( !$errorCode = valid::validPsw( $psw, 8, '11111' )){
                  if( $psw == http::cleanPost( 'psw_again' )){
                    //if( http::cleanPost( 'aszf' )){
                      $psw_hash = password_hash( $psw, PASSWORD_BCRYPT );
                      self::save( 'shared.users', [['	password', $psw],
                                                   ['	password_hash', $psw_hash]], ['id', $_SESSION['USEROLD'], 'i'] );
                      if( $_SESSION['PROID'] > 0 ){
                        $user_project_id = self::save( 'shared.user_projects', [
                          ['user_id', $_SESSION['USEROLD'], 'i'], 
                          ['project_id', $_SESSION['PROID'], 'i']
                        ] );
                        $company_id = self::save( 'shared.companies', [ ['created_user_id', $_SESSION['USEROLD'], 'i']] );
                        self::save( 'shared.usages', [
                          ['user_project_id', $user_project_id, 'i'], 
                          ['company_id', $company_id, 'i']
                        ] );
                      }
                      //unset( $_SESSION );
                      $_SESSION['USER'] = 0;
                      if( self::$project->configuration->email_confirmation ?? 0 and self::$project->configuration->email_confirmation ){
                        // ha kell email visszaigazolás akkor ide jön az összeállítás
                        $message = '???';
                        $_POST['reglepes'] = 4;
                      }else{
                      
                        // email küldés ide...
                      
                        $_POST['reglepes'] = 3;
                      }
                    //}else response::add( 'error', 'ÁSZF elfogadása szükséges!' );
                  }else response::add( 'error', 'form', ['psw_again' => 'Nem egyezik az újra megadott jelszó!'] );
                }else
                  switch( $errorCode ){
                    case 1: response::add( 'error', 'form', ['psw' => 'Nincs megadva a jelszó!'] ); break;
                    case 2: response::add( 'error', 'form', ['psw' => 'Nem megfelelő karakter van a jelszóban!'] ); break;  
                    case 3: response::add( 'error', 'form', ['psw' => 'Rövid a jelszó! Legalább 8 karakter'] ); break;  
                    case 4: response::add( 'error', 'form', ['psw' => 'Túl hosszú a jelszó! Maximum 30 karakter'] ); break;  
                    case 5: response::add( 'error', 'form', ['psw' => 'Nem tartalmaz számot a jelszó!'] ); break;  
                    case 6: response::add( 'error', 'form', ['psw' => 'Nem tartalmaz betűt a jelszó!'] ); break;
                    case 7: response::add( 'error', 'form', ['psw' => 'Nem tartalmaz kisbetűt a jelszó!'] ); break;
                    case 8: response::add( 'error', 'form', ['psw' => 'Nem tartalmaz nagybetűt a jelszó!'] ); break;
                    case 9: response::add( 'error', 'form', ['psw' => 'Nem tartalmazhat csak azonos karaktereket a jelszó!'] ); break;
                  }
              }else{
                $_SESSION['USER'] = 0;
                unset( $_SESSION['EMAILOLD'] );
                response::add( 'error', 'form', ['email' => 'Nem megfelelő emailcím!'] ); // Megváltozott emailcím, form manipuláció
                $_POST['reglepes'] = 1;
              }
            }else{
              $_SESSION['USER'] = 0;
              unset( $_SESSION['EMAILOLD'] );
              response::add( 'error', 'form', ['email' => 'Nem megfelelő emailcím!'] );
              $_POST['reglepes'] = 1;
            }
          break;
          case 3:
          
          break;
          case 4:
          
          break;
        }
      }else $_SESSION['USER'] = 0;
    }
  }

  /** Jelszó ellenőrzés
   * @param string $to ha belépett hová ugorjon
   */
  public static function check_psw( $user, $to = '' ){
    if( $psw = http::cleanPost( 'psw' )){
      if( password_verify( $psw, $user->password_hash )){
        $_SESSION['USER'] = $user->id;
        self::save( 'shared.logbook', [['user_id', $_SESSION['USER'], 'i'],
                                       ['project_id', $_SESSION['PROID'], 'i'],
                                       ['ip', $_SERVER['REMOTE_ADDR'] ?? ''],
                                       ['user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '']] );
        if( $_SESSION['PROID'] ){
          if( $user_projects = self::list_usages( $_SESSION['USER'], $_SESSION['PROID'] )){
            self::$user_project = ( object ) $user_projects[0];
            $_SESSION['COMPANY'] = self::$user_project->company_id;
            $_SESSION['USER_RIGHTS'] = self::$user_project->level_and_rights ?? '0';
            // Utolsó látogatás frissítése
            self::save_visit( $_SESSION['USER'], $_SESSION['PROID'], $_SESSION['COMPANY'] );

            if( count( $user_projects ) > 1 ){
              // több modul tartozik hozzá választási oldalra küldjük
              foreach( $user_projects as $row ) $company_ids[] = $row['company_id'];
              $_SESSION['SELECT_COMPANY'] = self::list_select_company( $company_ids );
            }
          }else{
            if( !self::$user_project = self::get( 'shared.user_projects', 'user_id='.$_SESSION['USER'].' AND project_id='.$_SESSION['PROID'], OBJECT_TYPE )){
              // Nincs modul-ja a reg-nek ezért készül
              self::save( 'shared.user_projects', [['user_id', $_SESSION['USER'], 'i'], 
                                                   ['project_id', $_SESSION['PROID'], 'i']] );
              self::$user_project = self::get( 'shared.user_projects', 'user_id='.$_SESSION['USER'].' AND project_id='.$_SESSION['PROID'], OBJECT_TYPE );
            }
            $_SESSION['COMPANY'] = self::$user_project->company_id = self::$user_project->rights = 0;        
          }
          $_SESSION['USER_PROJECT'] = self::$user_project->id;
        }
        if( $user->level ?? 0 ) $_SESSION['USER_RIGHTS'][0] = 'z';
        unset( $user );
        http::gourl( $to );
      }else{ 
        response::add( 'error', 'Nem megfelelő email vagy jelszó!');
        self::save( 'shared.logbook', [['user_id', $user->id, 'i'],
                                       ['project_id', $_SESSION['PROID'], 'i'],
                                       ['password', $_POST['psw']],
                                       ['ip', $_SERVER['REMOTE_ADDR'] ?? ''],
                                       ['user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '']] );
      }
    }else{ 
      response::add( 'error', 'Nem megfelelő email vagy jelszó!');
      self::save( 'shared.logbook', [['user_id', $user->id, 'i'],
                                     ['project_id', $_SESSION['PROID'], 'i'],
                                     ['password', $_POST['psw']],
                                     ['ip', $_SERVER['REMOTE_ADDR'] ?? ''],
                                     ['user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '']] );
    }
  }

  /** Jelszó generálás
   */
  public static function create_psw(){
    return bin2hex( random_bytes( 8 ));
  }

  /**
   * Regisztráció kiirása a fájlba
   * @param string  $email
   * @param string  $jelszoi
   * @param integer $bonusz
   */  
  public static function reginsert( $email, $jelszoi, $bonusz = 0, $bonusznap = 0 ){
    $jelszo = md5( $jelszoi );
    $psw_hash = password_hash( $jelszoi, PASSWORD_BCRYPT );
    if( $_SESSION['REG'] = self::save_authReg( [['email', $email],
                                                ['becenev', $_POST['becenev'] ?? $_POST['neve'].' '.$_POST['nevek']],
                                                ['neve', $_POST['neve']],
                                                ['nevek', mb_convert_case( $_POST['nevek'], MB_CASE_TITLE,"utf-8" )],
                                                ['nfc', $_POST['nfc'] ?? 'Vállalkozás'],
                                                ['password', $psw_hash],
                                                ['jelszo', $jelszo],
                                                ['jelszoi', $jelszoi],
                                                ['bonusz', $bonusz, 'i'],
                                                ['kredit', $bonusz, 'i'],
                                                ['telefon', $_POST['telefon'] ?? ''],
                                                ['bonuszkelt',(( $bonusz > 0 ) ? date( 'Y-m-d H:i:s', strtotime( ' +'.$bonusznap.' day' ))
                                                                  : date( 'Y-m-d H:i:s' ))]
                                              ] )){
      $_SESSION['EMAILOLD'] = $email;
      $_SESSION['REGBECNEV'] = $_POST['becenev'] ?? $_POST['neve'].' '.$_POST['nevek'];
      $_SESSION['REGKREDIT'] = $bonusz;
      $_SESSION['REGBONUSZ'] = $bonusz;
      $_SESSION['USER_RIGHTS'] = '0';
      $_SESSION['COMPANY_RIGHTS'] = '';
      $_SESSION['REGCEGNEV'] = '';
      $_SESSION['REGCEGLINK'] = '';
      $_SESSION['COMPANY'] = 0;
      $_SESSION['REGCEGDB'] = 0;
      if( $_SESSION['PROID'] != 0 )
        self::save_authModul( [$_SESSION['REG'], $_SESSION['PROID']] );
    }
  }

  /** Kiolvassa a CEG azonosítóját az url link vagy ceg_id alapján
   * @param  string $clink
   * @retval int    $ceg_id 
   */
  public static function get_company_link( $link ){
    $res = false;
    if( is_numeric( $link ))
      $res = self::get( 'shared.companies', $link, OBJECT_TYPE );
    else
      $res = self::get( 'shared.companies', 'link_name="'.$link.'"', OBJECT_TYPE );
    return $res;
  }

  /**
   * Kiolvassa a REGTAX adatokat a reg_id alapján
   * @param  int $reg_id
   * @retval int 
   */
  public static function rekord_regtax( $reg_id = 0 ){
    $reg_id = ( $reg_id )? $reg_id : $_SESSION['REG'] ?? 0;
    return ( $reg_id )? self::get_authRegTax( $reg_id ) : 0;
  }

  /** Jogosultság ellenőrzés
   *
   * @param  int    $index jogok indexe
   * @param  string $who   felhasználó vagy cég 'user' | 'company'
   * @retval boolean true|false
   */
  public static function is_right( $index, $who = 'company' ){
    switch( $who ){
      case 'user':
        if( $_SESSION['USER_RIGHTS'][0] == 'z' or $_SESSION['USER_RIGHTS'][0] == 'a' ) return true; // z rendszergazda, mindenhez joga van
        if(( $index ) and ( $_SESSION['USER_RIGHTS'][$index] ?? 0 ) and $_SESSION['USER_RIGHTS'][$index] == '1' ) return true;
      break;
      case 'company':
        if(( $_SESSION['COMPANY_RIGHTS'][$index] ?? 0 ) and $_SESSION['COMPANY_RIGHTS'][$index] == '1' ) return true;
      break;
    }
    return false;
  }

  /** Jogosultságok kiolvasása a céghez
   */
  public static function get_rights(){
    if( $service_contracts = self::list( 'shared.service_contracts', 'company_id = '.$_SESSION['COMPANY'].' AND project_id = '.$_SESSION['PROID'].' AND (termination_date IS NULL OR termination_date > NOW())' )){
      // van szerződése(i)
      foreach( $service_contracts as $contract )
        foreach( json_decode( $contract['fee_packages'], true ) as $package )
          if( $service_fee_package = setup::get( 'shared.service_fee_packages', $package, OBJECT_TYPE ))
            if( $service_fee_package->rights ?? 0 )
              foreach( str_split( $service_fee_package->rights ) as $key2 => $right )
                if( $right ) $_SESSION['COMPANY_RIGHTS'][ $key2 ] = $right;
    }else
      if( $service_fee_package = setup::get( 'shared.service_fee_packages', 'MID(signs,1,1)="2" AND MID(signs,2,1)="0" AND project_id = '.$_SESSION['PROID'], OBJECT_TYPE )){
        if( $service_fee_package->rights ?? 0 )
          foreach( str_split( $service_fee_package->rights ) as $key2 => $right )
            if( $right ) $_SESSION['COMPANY_RIGHTS'][ $key2 ] = $right;
      }else return false;
    return true;
  }

  /** Jogosultságok kiolvasása a felhasználóhoz
   */
  public static function supportTicketFormHtml( $category_ids = null ){
    $categories = self::list(
      'shared.support_ticket_categories',
      (( $category_ids ?? 0 )? 'id IN('.$category_ids.')' : 0 )
    );
    ?>
    <form name="form_support_ticket" method="post">
      <ul class="formbox">
        <?= http::csrfToken() ?>
        <li class="form col0"><input type="text" name="user_name" placeholder="" value="<?= $_POST['user_name'] ?>"><label>Név</label></li>
        <li class="form col0"><input type="text" name="email" placeholder="" value="<?= $_POST['email'] ?>"><label>Email</label></li>
        <li class="form col0"><input type="text" name="phone" placeholder="" value="<?= $_POST['phone'] ?>"><label>Telefon</label></li>
        <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
          <select name="category_id" placeholder="">
            <?php if( $categories ) foreach( $categories as $category ){ ?>
              <option value="<?= $category['id'] ?>"<?= ( $_POST['category_id'] == $category['id'] )? ' selected' : '' ?>><?= $category['name'] ?></option>
            <?php } ?>
          </select>
          <label>Bejelentés jellege</label>
        </li>
        <li class="form col0"><input type="text" name="title" placeholder="" value="<?= $_POST['title'] ?>"><label>Tárgy</label></li>
        <li class="form col0"><textarea name="notification" placeholder=""><?= $_POST['notification'] ?></textarea><label>Üzenet</label></li>
        <li><input class="btn" type="submit" name="btn_support_ticket" value="Küldés"></li>
      </ul>
    </form>
    <?php
  }

  public static function save_supportTicketPost(){
    $ok = false;
    $_POST = http::cleanPost();
    if(( $_POST['btn_support_ticket'] ?? 0 ) and http::csrfCheck()){
      $_POST['notification'] = htmlspecialchars( $_POST['notification'] ?? '', ENT_QUOTES, 'UTF-8' );
      $_POST['email'] = filter_var( $_POST['email'] ?? '', FILTER_SANITIZE_EMAIL );
      if( $_POST['email'] and $_POST['notification'] !='' ){
        $_POST['phone'] = htmlspecialchars( $_POST['phone'] ?? '', ENT_QUOTES, 'UTF-8' );
        $_POST['title'] = htmlspecialchars( $_POST['title'] ?? $_POST['notification'] , ENT_QUOTES, 'UTF-8' );
        $_POST['user_name'] = htmlspecialchars( $_POST['user_name'], ENT_QUOTES, 'UTF-8' );
        $_POST['category_id'] = (int) $_POST['category_id'];
        self::save(
          'shared.support_tickets',
          [
            ['project_id', $_SESSION['PROID'], 'i'],
            ['company_id', $_SESSION['COMPANY'], 'i'],
            ['user_id', $_SESSION['USER'] ?? 0, 'i'],
            ['category_id', $_POST['category_id'], 'i'],
            ['email', $_POST['email']],
            ['phone', $_POST['phone']],
            ['title', $_POST['title']],
            ['notification', $_POST['notification']]
          ]
        );
        $ok = true;
      }
    }
    return $ok;
  }
  
  /*
  function uuid($data = null) {
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
  }
  */
}