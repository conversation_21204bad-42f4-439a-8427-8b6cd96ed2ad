<header>
  <h6>Lakóegys<PERSON>g képek</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_lakoegysegkep_modosit">
    <input type="hidden" name="fotosor" value="1">
    <ul class="formbox">
      <li class="form col6">
        <input type="text" placeholder="" value="<?= $_POST['szobaszam'] ?? ''?>" disabled>
        <label>Neve</label>
      </li>
      <li class="form col4">
        <input type="text" placeholder="" value="<?= $_POST['epulet'] ?? ''?>" disabled>
        <label>Épület</label>
      </li>
    </ul>
    <ul class="formbox form-ddfile">
      <li class="col0 ddfilebox has-advanced-upload">
        <span class="box__icon" style="--icon:var(--icon-upload)">max <?= (20-count($kids ?? []))?> db</span>
        <input class="box__input" id="foto" type="file" name="foto[]" data-multiple-caption="{count} kiválasztott fájl" multiple>
        <label for="foto"><strong>Válassz fájlt</strong><span class="box__dragndrop"> vagy húzd ide</span></label>
      </li>
      <li class="box__uploading">Feltöltés&hellip;</li>
      <li class="box__success">Kész! <a href="#" class="box__restart" role="button">További fájlok?</a></li>
      <li class="box__error">Hiba! <span></span>. <a href="#" class="box__restart" role="button">Próbáld újra!</a></li>

      <li class="col0 flex" id="kepablak">
        <?php $id=1;while(file_exists('upload/'.$_SESSION['COMPANY'].'/'.$_POST['lakoegyseg_id'].'_'.$id.'.jpg')){ ?>
        <div draggable="false">
          <a style="--icon:var(--icon-trash-empty)"
              href = "<?= http::$path?>/lakoegyseg/ed/<?= $_POST['lakoegyseg_id']?>/del/<?= $id?>"
              title = "Törlés"
            onclick = "return confirm('Biztos törlöd?')"></a>
          <img data-kid="<?= $id?>" src="/upload/<?= $_SESSION['COMPANY'] ?>/<?= $_POST['lakoegyseg_id'].'_'.$id.'.jpg?'.md5(date("YmdHis"))?>">
        </div>
        <?php $id++;} ?>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="callback" name="btn_modositas" title="Lakóegység képeinek mentése">Módosít</button>
  <button class="close">Kilép</button>
</footer>