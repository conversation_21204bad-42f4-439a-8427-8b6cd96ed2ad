<?php
/**
 * @file  model_ical.php
 * @brief Ical lekérések
 */

require_once 'shared/app/icalendar/zapcallib.php';

class ical{
  const MIN_SYNC_INTERVAL = 5; // Percek száma két szinkronizáció között
  const ICAL_DIR = 'upload/ical/';

  public static function get_export_filename( $lakoegyseg_id ){
    return $lakoegyseg_id . 'l' . md5( $lakoegyseg_id . 'tren' ) . '.ics';
  }

  public static function import_icals(){
    $message = date('Y-m-d H:i:s');
    
    $szinkronnaptarak = db::list_bookingSynchrosIcal();
    if( !$szinkronnaptarak )
      return $message . "\nNincsenek szinkronizálandó naptárak!";
    
    foreach( $szinkronnaptarak as $szinkronnaptar ){
      $message .= "\n" . $szinkronnaptar['lakoegyseg_id'] . ' ' . $szinkronnaptar['csatorna_id'] . '| ';
      $result = self::import_ical(
        $szinkronnaptar['lakoegyseg_id'], 
        $szinkronnaptar['csatorna_id'], 
        $szinkronnaptar['company_id']
      );
      $message .= $result;
    }
    
    return $message;
  }

  /**
   * Egy iCal importálása
   * @param int $lakoegyseg_id A lakóegység azonosítója
   * @param int $csatorna_id A csatorna azonosítója
   * @param int $ceg_id A cég azonosítója (opcionális)
   * @return string Üzenet az importálás eredményéről
   */
  public static function import_ical( $lakoegyseg_id, $csatorna_id, $ceg_id = 0 ){
    $message = '';
    $exportlakoegysegek = [];
    
    // Szinkronnaptar lekérése
    $szinkronnaptar = db::get_szinkronnaptar( $lakoegyseg_id, $csatorna_id );
    if( !$szinkronnaptar )
      return "Nincs ilyen csatorna! $csatorna_id";
    
    // Link ellenőrzése
    if( empty( $szinkronnaptar['link'] ))
      return 'Nincs csatornánál link beállítva!';
    
    // Időkorlát ellenőrzése
    $lastSync = $szinkronnaptar['utolso'] ?? null;
    if( $lastSync ){
      $now = new DateTime();
      $last = new DateTime( $lastSync );
      $interval = $now->diff( $last );
      
      $minutesDiff = $interval->h * 60 + $interval->i;
      
      if( $minutesDiff < self::MIN_SYNC_INTERVAL && !$interval->invert )
        return sprintf(
          '5 percenként kérhető újraolvasás! %s %s %d %d %d',
          $now->format( 'Y-m-d H:i:s' ),
          $lastSync,
          $now->getTimestamp(),
          $last->getTimestamp(),
          $now->getTimestamp() - $last->getTimestamp()
        );
    }
    
    // iCal letöltése
    $icalfile = $szinkronnaptar['link'];
    $icalfeed = @file_get_contents( $icalfile );
    if( !$icalfeed )
      return "Nem olvasható a $icalfile link!";
    
    // iCal ellenőrzése
    if( strlen( $icalfeed ) <= 8 || !strpos( '#' . $icalfeed, $szinkronnaptar['ellenorzo'] ))
      return 'Hiányos ICAL (Nincs ellenőrzőkód) ! ' . $szinkronnaptar['ellenorzo'];
    
    // iCal feldolgozása
    $icalobj = new ZCiCal( $icalfeed );
    $kulsoazonositok = [];
    
    if( !isset( $icalobj->tree->child ))
      return 'Üres iCal fájl!';
    
    // Utolsó szinkronizáció idejének frissítése
    db::save_bookingSynchrosTimeUpdate( $szinkronnaptar['szinkronnaptar_id'] );
    $message .= 'Import kiolvasás sikeres!';
    
    // Események feldolgozása
    foreach( $icalobj->tree->child as $node ){
      if( $node->getName() != 'VEVENT' ) continue;
      
      $event = self::parseIcalEvent( $node );
      if( !$event['erkezes'] or !$event['tavozas'] ) continue;
      
      $kulsoazonositok[] = $event['kulsoazonosito'];
      $exportlakoegysegek[] = $lakoegyseg_id;
      
      // Foglalás ellenőrzése és kezelése
      $message .= self::handleBooking(
        $event,
        $csatorna_id,
        $lakoegyseg_id,
        $ceg_id
      );
    }
    
    // Lemondások kezelése
    $lemondottak = self::handleCancellations(
      $lakoegyseg_id,
      $csatorna_id,
      $ceg_id,
      $kulsoazonositok
    );
    
    if( count( $lemondottak )){
      $message .= ' Lemondott foglalások: ' . implode( ', ', $lemondottak );
      $exportlakoegysegek[] = $lakoegyseg_id;
    }
    
    // Exportálás
    if( count( $exportlakoegysegek )){
      $exportlakoegysegek = array_unique( $exportlakoegysegek );
      foreach( $exportlakoegysegek as $id )
        self::export_ical( $id, $ceg_id );
    }
    
    return $message;
  }
  
  /**
   * iCal esemény adatainak kinyerése
   * @param object $node Az iCal esemény node
   * @return array Az esemény adatai
   */
  private static function parseIcalEvent( $node ){
    $erkezes = $tavozas = false;
    $kulsoazonosito = $hivatkozasinev = '';
    
    foreach( $node->data as $key => $value ){
      if( $key == 'DTSTART' )
        $erkezes = substr( $value->getValues(), 0, 4 ) . '-' . substr( $value->getValues(), 4, 2 ) . '-' . substr( $value->getValues(), 6 );
      if( $key == 'DTEND' )
        $tavozas = substr( $value->getValues(), 0, 4 ) . '-' . substr( $value->getValues(), 4, 2 ) . '-' . substr( $value->getValues(), 6 );
      if( $key == 'UID' )
        $kulsoazonosito = $value->getValues();
      if( $key == 'SUMMARY' )
        $hivatkozasinev = $value->getValues();
    }
    
    return [
      'erkezes' => $erkezes,
      'tavozas' => $tavozas,
      'kulsoazonosito' => $kulsoazonosito,
      'hivatkozasinev' => $hivatkozasinev
    ];
  }
  
  /**
   * Foglalás kezelése
   * @param array $event Az esemény adatai
   * @param int $csatorna_id A csatorna azonosítója
   * @param int $lakoegyseg_id A lakóegység azonosítója
   * @param int $ceg_id A cég azonosítója
   * @return string Üzenet a foglalás kezeléséről
   */
  private static function handleBooking( $event, $csatorna_id, $lakoegyseg_id, $ceg_id ){
    $message = '';
    $napokfo = [];
    
    // Napok számának kiszámítása
    if( $event['erkezes'] and $event['tavozas'] ){
      $difnap = ( strtotime( $event['tavozas'] ) - strtotime( $event['erkezes'] )) / 24 / 60 / 60;
      for( $i = 0; $i < $difnap; $i++ ) $napokfo[] = 0;
    }
    
    // Meglévő foglalás ellenőrzése
    $foglalas = db::get_foglalasCsatorna( $csatorna_id, $event['kulsoazonosito'], $ceg_id );
    
    if( $foglalas ){
      $message.= "\n".'- Foglalás már létezik: '.$event['erkezes'].' - '.$event['tavozas'].' '.$foglalas['foglalva'];
      
      // Lemondott foglalás visszaállítása
      if( $foglalas['lemondva'] ?? false ){
        db::save_foglalas( [['lemondva', null]], $foglalas['foglalas_id'] );
        $message .= 'Lemondva foglalás ';
      }
    }else{
      // Új foglalás létrehozása
      $foglalas_id = db::save_foglalasCsatorna(
        [
          $csatorna_id,
          date( 'Y-m-d H:i:s' ),
          $event['hivatkozasinev']
        ],
        $ceg_id
      );
      
      if( $foglalas_id ){
        db::save_foglallakoegyseg( [
          ['foglalas_id', $foglalas_id, 'i'],
          ['lakoegyseg_id', $lakoegyseg_id, 'i'],
          ['erkezes', $event['erkezes']],
          ['napokfo', json_encode($napokfo, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)],
          ['kulsoazonosito', $event['kulsoazonosito']]
        ] );
        $message .= 'Érkezett új foglalás!';
      }
    }
    
    return $message;
  }
  
  /**
   * Lemondások kezelése
   * @param int $lakoegyseg_id A lakóegység azonosítója
   * @param int $csatorna_id A csatorna azonosítója
   * @param int $ceg_id A cég azonosítója
   * @param array $kulsoazonositok Az aktív foglalások külső azonosítói
   * @return array A lemondott foglalások azonosítói
   */
  private static function handleCancellations( $lakoegyseg_id, $csatorna_id, $ceg_id, $kulsoazonositok ){
    $lemondottak = [];
    
    // Aktuális foglalások lekérése
    $foglalasok = db::list_foglalAktualis( $lakoegyseg_id, $csatorna_id, $ceg_id );
    if( !$foglalasok ) return $lemondottak;
    
    // Lemondások kezelése
    foreach( $foglalasok as $foglalas )
      if( count( $kulsoazonositok ) and !in_array( $foglalas['kulsoazonosito'], $kulsoazonositok )){
        db::save_foglalas( [['lemondva', date( 'Y-m-d H:i:s' )]], $foglalas['foglalas_id'] );
        $lemondottak[] = $foglalas['foglalas_id'];
      }
    
    return $lemondottak;
  }

  /**
   * iCal exportálása
   * @param int $lakoegyseg_id A lakóegység azonosítója
   * @param int $ceg_id A cég azonosítója (opcionális)
   * @return bool Sikeres volt-e az exportálás
   */
  public static function export_ical( $lakoegyseg_id, $ceg_id = 0 ){
    $icalfile = self::ICAL_DIR . self::get_export_filename( $lakoegyseg_id );
    $ical = new ZCiCal();
    
    // Foglalások lekérése
    $foglaltak = db::list_foglalAktualis( $lakoegyseg_id, 0, $ceg_id );
    if( $foglaltak )
      foreach ($foglaltak as $key => $foglalt) {
        $vevent = new ZCiCalNode( "VEVENT", $ical->curnode );
        $vevent->addNode( new ZCiCalDataNode( "DTSTAMP:" . ZCiCal::fromSqlDateTime()));
        $vevent->addNode( new ZCiCalDataNode( "DTSTART:" . ZCiCal::fromSqlDateTime( $foglalt['erkezes'] )));
        $vevent->addNode( new ZCiCalDataNode( "DTEND:" . ZCiCal::fromSqlDateTime( $foglalt['tavozas'] )));
        $vevent->addNode( new ZCiCalDataNode( "UID:" . md5( $foglalt['foglallakoegyseg_id'] ) . '@tren.hu' ));
      }
    
    // Fájl mentése
    try{
      if( !is_dir( self::ICAL_DIR ))
        mkdir( self::ICAL_DIR, 0755, true );
      
      if( $f = fopen($icalfile, 'w' )){
        fwrite( $f, $ical->export());
        fclose( $f );
        return true;
      }else{
        user_error( 'Fájl nyitási hiba: ' . $icalfile );
        return false;
      }
    }catch( Exception $e ){
      user_error( 'iCal exportálási hiba: ' . $e->getMessage());
      return false;
    }
  }
}
