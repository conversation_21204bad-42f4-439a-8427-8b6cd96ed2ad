<header>
  <h6><?= response::$vw->view->invoice->type.': '.response::$vw->view->invoice->invoice_number ?></h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_invoice">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <ul class="formbox">
      <li class="form col5">
        <input type="text" list="booking_id" name="booking_id" placeholder="" value="<?= $_POST['booking_id'] ?? 'Ismeretlen!' ?>">
        <label>Foglalás</label>
        <datalist id="booking_id">
          <?php if( response::$vw->view->bookings ?? 0 ){ ?>
          <?php   foreach( response::$vw->view->bookings as $booking ){ ?>
          <option value="<?= $booking->foglalas_id ?>">
            <?= $booking->foglalas_id.' '.$booking->kapcsolattarto.' '.$booking->foglalva.' '.$booking->fizetendo.' '.$booking->eloleg ?>
          </option>
          <?php } } ?>
        </datalist>
      </li>
      <li class="form col5">
        <input type="text" name="payday" placeholder="" value="<?= $_POST['payday'] ?? 'Nincs!' ?>">
        <label>Fizetés napja</label>
      </li>
      <li class="form col0">
        <input type="text" placeholder="" value="<?= response::$vw->view->invoice->customer->name ?? 'Magánszemély' ?>" disabled>
        <label>Vevő</label>
      </li>
      <li class="form col5">
        <input type="text" placeholder="" value="<?= response::$vw->view->invoice->transaction ?? '' ?>" disabled>
        <label>NAV azonosító</label>
      </li>
      <li class="form col5">
        <input type="text" placeholder="" value="<?= response::$vw->view->invoice->grossAmount ?? '' ?>" disabled>
        <label>Végösszeg</label>
      </li>
      <li class="form col5">
        <input type="text" placeholder="" value="<?= response::$vw->view->invoice->issue_date ?>" disabled>
        <label>Kiállítás napja</label>
      </li>
      <li class="form col5">
        <input type="text" placeholder="" value="<?= response::$vw->view->invoice->execution_date ?>" disabled>
        <label>Teljesítés napja</label>
      </li>
      <li class="form col0">
        <hr>
      </li> 
      <?php if( response::$vw->view->invoice->items ?? 0 ){ ?>
      <?php   foreach( response::$vw->view->invoice->items as $item ){ ?>
      <li class="form col0">
        <input type="text" placeholder="" value="<?= $item->name ?? '' ?>" disabled>
        <label>Termék</label>
      </li>
      <li class="form col25">
        <input type="text" placeholder="" value="<?= $item->quantity ?? '' ?>" disabled>
        <label>Mennyiség</label>
      </li>
      <li class="form col25">
        <input type="text" placeholder="" value="<?= $item->unitName ?? '' ?>" disabled>
        <label>M.egység</label>
      </li>
      <li class="form col5">
        <input type="text" placeholder="" value="<?= $item->price_net ?? '' ?>" disabled>
        <label>Nettó egységár</label>
      </li>

      <?php } } ?>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <?php if( !( $_POST['sending_time'] ?? 0 )){ ?>
  <button class="callback" name="btn_modositas" title="Számla mentése">Módosít</button>
  <?php } ?>
</footer>