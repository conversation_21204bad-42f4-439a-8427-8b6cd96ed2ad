  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Lakóegység típusok</h5>
        <span>
          <?php //if($_SESSION['REGJOG'] == '7' or ($_SESSION['REGJOG'][0] == '1' and $_SESSION['REGJOG'][4] == '1')){ ?>
          <a class="btn create" style="--icon:var(--icon-plus-circled)"> Új típus</a>
          <?php //} ?>
          </span>
      </header>
      <article>
        <?php if( $_SESSION['accommodation_units'] ?? 0 ) unset( $_SESSION['accommodation_units'] ) ?>
        <?php if( $_COOKIE['accommodation_units'] ?? 0 ) unset( $_COOKIE['accommodation_units'] ) ?>
        <?= table::datatable( 
          'accommodation_unit_types',
          [ 'name' => ['th' => 'Megnevezés'],
            '*1' => [
              'th' => '',
              'icons' => [
                'edit:modal:Szerkesztés',
                'trash-empty:event:Törlés:::confirm:where:is_trash_empty',
                'bed:event:Lakóegységek'
              ]
            ]
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { datatable, dialog, ajax } from '/shared/js/spritzer/index.js'

    var eventManagerFunctions = function( data ){
      let ifunction = data[1],
          id = data[2] || null,
          fd = null
      switch( ifunction ){
        case 'accommodation_unit_types_edit_callback':
          event.preventDefault()
          fd = new FormData( document.forms.namedItem( 'form_types' ) )
          fd.append( 'btn_modositas', true )
          ajax( {
            url: '/modal/accommodation_unit_type_save',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                dialog( {type: 'status:success', content: 'Adatok módosítva', timeDelay: 6000} )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a mentés!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'accommodation_unit_types_edit_close':
          location.replace( '/accommodationunittypes' )
        break
        case 'accommodation_unit_types_trash-empty':
          fd = new FormData()
          fd.append( 'id', id )
          ajax( {
            url: '/modal/accommodation_unit_types_del',
            body: fd,
            done: ( back ) => {
              let response = JSON.parse( back.response )
              if( response.ok )
                location.replace( '/accommodationunittypes' )
              else
                dialog( {type: 'status:error', content: 'Sikertelen a törlés, használatban van a típus!', timeDelay: 6000} )
            },
            fail: ( err ) => {
              dialog( {type: 'status:error', content: 'Sikertelen művelet!', timeDelay: 6000} )
            }
          } )
        break
        case 'accommodation_unit_types_bed':
          location.replace( '/accommodationunits/'+id )
        break
      }
    }

    window.addEventListener('DOMContentLoaded', () => {
      datatable( {
        create: {
          title: 'Új lakóegység típus',
          label: 'Megnevezés'
        },
        eventManagerFunctions
      } )
    } )
  </script>