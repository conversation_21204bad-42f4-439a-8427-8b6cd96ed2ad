<?php
/*
if( $_SESSION['LEID'] ?? 0 and $_SESSION['LEID'] == 'undefined' ){
  unset( $_SESSION['NAP1'] );
  unset( $_SESSION['NAP2'] );
  unset( $_SESSION['LEID'] );
  unset( $_SESSION['EJ'] );
}

Árfolyam lekérés API-n
http://api.napiarfolyam.hu/?bank=otp&valuta=eur&datum=********

Naptár állapotok színnel:
Visszaigazolt*  - csak jelöljük
Számlázva*      - csak jelöljök van szamla noz NULL
Nem fizetetett* - border piros  fizetendo == SUM fizet

Lefoglalva     kék              van foglalas
Bejelentkezve  narancs          date>=erkezes AND date<=tavozas
Kijelentkezve  szürke           date>tavozas

zárolt         piros             
*/

$_SESSION['NAP'] = $_SESSION['NAP'] ?? date( 'Y-m-d', strtotime( '-2 day' ));
if( $_GET['step'] ?? 0 ){
  switch( substr( $_GET['step'], 0, 2 )){
    case 'VN': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( $_SESSION['NAP'].' -1 day' )); break;
    case 'EN': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +1 day' )); break;
    case 'V7': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( $_SESSION['NAP'].' -1 week' )); break;
    case 'E7': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +1 week' )); break;
    case 'VH': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( $_SESSION['NAP'].' -1 month' )); break;
    case 'EH': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +1 month' )); break;
    case 'MA': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( '-2 day' )); break;
    case 'DA': $_SESSION['NAP'] = date( 'Y-m-d', strtotime( substr( $_GET['step'], 2 ).' -2 day' )); break;
  }
}
$_SESSION['MAXNAP'] = 100;

/* foglalás kijelölés törlése
if(( http::$route[1] ?? 0 ) and http::$route[1] == 'nofoglalas' ){
  unset( $_SESSION['NAP1'] );
  unset( $_SESSION['NAP2'] );
  unset( $_SESSION['LEID'] );
  unset( $_SESSION['EJ'] );
  array_splice( http::$route, 1 );
} */

$szobak = db::list_accommodationUnits( 'MID(au.signs,1,1)=2' );
response::add( 'view', 'szabadlakoegysegek', $szobak );

for( $i = 0; $i < $_SESSION['MAXNAP']; $i++ )
  $szabad[date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +'.$i.' day' ))] = ['nap' => 'E', 'ar' => false];

if( $szobak )
  foreach( $szobak as $szoba )
    $foglalaslist[ $szoba['name'].' max '.$szoba['places'].' főmax'.$szoba['id'] ] =
      ['foglalt' => [],'szabad' => ( !isset( $_SESSION['LEID'] ) or $_SESSION['LEID'] == $szoba['id'] )? $szabad : []];

if( $foglalasok = db::list_foglalt( $_SESSION['NAP'] )){
  $le = '';
  foreach( $foglalasok as $foglalas ){
    if( $foglalas['erkezes'] > date( 'Y-m-d', strtotime( $_SESSION['NAP'].' +'.$_SESSION['MAXNAP'].' day' ))) continue;
    if( $foglalas['name'] <> $le ){
      $le = $foglalas['name'];
      $leid = $le.' max '.$foglalas['ferohely'].' főmax'.$foglalas['lakoegyseg_id'];
      $nap = $_SESSION['NAP'];
      $utolso='';
      $szabad = $foglalaslist[$leid]['szabad'];
    }
    $szla = null;
    if( $invoices = db::list_invoices( 'booking_id='.$foglalas['foglalas_id'] ))
      foreach( $invoices as $invoice )
        switch( $invoice['signs'][1] ){
          case 2: $szla.= '<b title="Előlegszámla: '.$invoice['invoice_number'].'">E</b> '; break;
          case 3: $szla.= '<b title="Számla: '.$invoice['invoice_number'].'">S</b> '; break;
          case 4: $szla.= '<b title="Érvénytelenítő számla: '.$invoice['invoice_number'].'">É</b> '; break;
        }

    $foglalt = ['foglal_id' => $foglalas['foglallakoegyseg_id'],
                'foglalas_id' => $foglalas['foglalas_id'],
                'csatorna' => ( $foglalas['megnevezes'] ?? '' ),
                'kapcsolattarto' => $foglalas['kapcsolattarto'],
                'foglalva' => $foglalas['foglalva'],
                'erkezes' => $foglalas['erkezes'],
                'tavozas' => $foglalas['tavozas'],
                'ej' => $foglalas['ej'],
                'eloleg' => $foglalas['eloleg'],
                'fizetendo' => $foglalas['fizetendo'],
                'fizetve' => $foglalas['fizetve'],
                'vendegfo' => $foglalas['vendegfo'],
                'eloleg' => $foglalas['eloleg'],
                'email' => $foglalas['email'],
                'telefon' => $foglalas['telefon'],
                'megjegyzes' => $foglalas['megjegyzes'],
                'szla' => $szla,
                'div' => []];
    
    if( $foglalas['erkezes'] < $nap ){
      if( $nap < $foglalas['tavozas'] ){
        $difnap = ( strtotime( $foglalas['tavozas'] ) - strtotime( $nap )) / 24 / 60 / 60;
        $difnap = ( $difnap > $_SESSION['MAXNAP'] )? $_SESSION['MAXNAP'] : $difnap;
      }else $difnap = 0;
      $foglalt['div']['db'] = $difnap;
      if( $difnap )
        for( $i=0; $i < $difnap; $i++ )
          unset( $szabad[date( 'Y-m-d', strtotime( $nap.' +'.$i.' day' ))] );
      $szabad[$foglalas['tavozas']]['nap'] = 'T';
      $nap = $foglalas['tavozas'];
    }else{
      $nap = $foglalas['erkezes'];
      if( date('Y-m-d', strtotime( $_SESSION['NAP'].' +'.$_SESSION['MAXNAP'].' day' )) < $foglalas['tavozas'] )
        $difnap = ( strtotime( $_SESSION['NAP'].' +'.$_SESSION['MAXNAP'].' day' ) - strtotime( $nap )) / 24 / 60 / 60;
      else
        $difnap = (( strtotime( $foglalas['tavozas'] ) - strtotime( $nap )) / 24 / 60 / 60 );
      $foglalt['div']['db'] = ( $difnap > 1 )? $difnap-1 : 0;
      if(( $szabad[$foglalas['erkezes']] ?? 0 ) and $szabad[$foglalas['erkezes']]['nap'] == 'T' ){
        unset( $szabad[$foglalas['erkezes']]['nap'] );
        unset( $szabad[$foglalas['erkezes']] );
      }
      $difnap--;
      if( $difnap )
        for( $i=0; $i < $difnap; $i++ )
          unset( $szabad[date( 'Y-m-d', strtotime( $nap.' +'.( $i+1 ).' day' ))] );
      $szabad[$foglalas['tavozas']]['nap'] = 'T';
      $nap = $foglalas['tavozas'];
    }
    $foglalaslist[$leid]['foglalt'][] = $foglalt;
  }
}

foreach( $foglalaslist as $leid => $lakoegyseg ){
  $le = explode( 'max', $leid );
  if( isset( $_SESSION['LEID'] ) and $_SESSION['LEID'] == $le[2] ){
    http::$route[1] = 'foglalas';
    if( isset( $_SESSION['NAP1'] )) $szabad[$_SESSION['NAP1']]['nap'] = 'F';
    if( isset( $_SESSION['NAP2'] )){
      $_SESSION['EJ'] = (( strtotime( $_SESSION['NAP2'] ) - strtotime( $_SESSION['NAP1'] )) / 24 / 60 / 60 );
      if( $_SESSION['EJ'] > 1 ){
        for( $i=0; $i < $_SESSION['EJ']; $i++ )
          $foglalaslist[$leid]['szabad'][date( 'Y-m-d', strtotime( $_SESSION['NAP1'].' +'.( $i+1 ).' day' ))]['nap'] = 'F';
      }
    }
  }
}

response::add( 'view','foglalaslist',$foglalaslist );
//echo '<pre>' . var_export($foglalaslist, true) . '</pre>';
