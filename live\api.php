<?php
$root = 'https://tren.hu';
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');
if( $ceg = api::apikeykontrol() ){
  if( api::$request['post'] ?? 0 )
    api::$request['post'] = json_decode( api::$request['post'] );
  $res = [ 'status' => true, 'ceg' => $ceg ];
  if( $ceg['configuration']->is_occupancy and ( api::$request['foglaltsag'] ?? false ) ){
    $foglalt = [];
    if( $szobak = db::list_accommodationUnits( 'MID(au.signs,1,1)=2', $ceg['id'] ) )
      foreach( $szobak as $szoba )
        $foglalt[ $szoba['name'].' max '.$szoba['places'].' fő' ] = '';
    if( $foglalasok = db::list_foglalt( date( 'Y-m-d' ), $ceg['id'] ) ){
      $le = '';
      $nap = $napmax = date( 'Y-m-d' );
      foreach( $foglalasok as $foglalas ){
        if( $foglalas['szobaszam'] <> $le ){
          $le = $foglalas['szobaszam'];
          $nap = date( 'Y-m-d' );
          $utolso='';
        }
        if( $nap > $foglalas['erkezes'] ){
          if( $nap == $foglalas['tavozas'] ){
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'T';
            $utolso = 'T';
            $utolsonap = $nap;
          }else{
            $difnap = ( strtotime( $foglalas['tavozas'] ) - strtotime( $nap ) ) / 24 / 60 / 60;
            if( $difnap > 1 )
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'F', $difnap );
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'T';
            $utolsonap = $nap = $foglalas['tavozas'];
            $utolso = 'T';
          }
        }else{
          if( $nap == $foglalas['erkezes'] ){
            if( $utolso == 'T' ){
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] = substr( $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ],0,strrpos( $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ],$foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ][strlen( $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ])-1] ) );
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'C';
            }else
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'E';
          }else{
            $difnap = ( strtotime( $foglalas['erkezes'] ) - strtotime( $nap ) ) / 24 / 60 / 60;
            if( $utolso == 'T' )
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'S', $difnap - 1 );
            else
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'S', $difnap );
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'E';
            $nap = $foglalas['erkezes'];
          }
          $difnap = ( strtotime( $foglalas['tavozas'] ) - strtotime( $nap ) ) / 24 / 60 / 60;
          if( $difnap > 1 )
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'F', $difnap -1 );
          $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'T';
          $nap = $foglalas['tavozas'];
          $utolso = 'T';
        }
      }

      if( api::$request['maxkelt'] ?? 0 ){
        $difnap = ( strtotime( api::$request['maxkelt'] ) - strtotime( date('Y-m-d') ) ) / 24 / 60 / 60;
        if( $difnap > 0 and $difnap < ( api::$request['maxnap'] ?? 30 ) )
          api::$request['maxnap'] = $difnap;
        if( $difnap < 0 )
          api::$request['maxnap'] = 0;
      }
      foreach( $foglalt as $szoba => $foglalas ){
        $foglalt[$szoba] .= str_repeat( 'S', api::$request['maxnap'] ?? 30 );
        $foglalt[$szoba] = substr($foglalt[$szoba], 0, api::$request['maxnap'] ?? 30 );
      }

      $res = $res + [ "foglalt" => $foglalt ];
    }
  }
  
  if( api::$request['szolgaltatasok'] ?? 0 ){
    if( $szolgaltatas = db::list_szolgaltatas() )
      $res = $res + [ "szolgaltatasok" => $szolgaltatas ];
  }
  /*
  if( $ceg['configuration']->is_booking and api::$request['foglalas7'] ?? 0 ){
    if( $foglalas7 = idoszakszabad7( 1 ) )
      $res = $res + ["foglalas7" => $foglalas7];
  }
  
  if( $ceg['configuration']->is_booking and api::$request['foglalas7h'] ?? 0 ){
    if( $foglalas7 = idoszakszabad7( 2 ) )
      $res = $res + ["foglalas7h" => $foglalas7];
  }
  */
  if( $ceg['configuration']->is_booking ){
    $holnap = date('Y-m-d', strtotime( '1 day' ) );
    if( $foglalasdateminmax = db::get_foglalasMinMax( $ceg['id'] ) ){
      $foglalasdatemin = ( $foglalasdateminmax['min'] ?? 0 )? ( $foglalasdateminmax['min'] > $holnap )? $foglalasdateminmax['min'] : $holnap : $holnap;
      $foglalasdatemax = ( $foglalasdateminmax['max'] ?? 0 )? $foglalasdateminmax['max'] : date('Y-12-31');
    }else{
      $foglalasdatemin = $holnap;
      $foglalasdatemax = date('Y-12-31');
    }
    $res = $res + ["foglalasdatemin" => $foglalasdatemin, "foglalasdatemax" => $foglalasdatemax];
  }
  
  if( ( api::$request['page'] ?? 0 ) and api::$request['page'] != '' ){
    $page = false;
    switch( api::$request['page'] ){
      case 'home':
        $ceg_id = $ceg['id'];
        $menu_link = api::$request['page'];
        require_once 'shared/modul/cont_page.php';
        $res = $res + [ "page" => $page ];
      break;
      case 'galeria':
        if( api::$request['galeria'] ?? 0 ){
          if( $galeria = db::get_galeria( api::$request['galeria'] ) ){
            $kepek = [];
            $res = $res + [ "galeria" => $galeria ];
            $dir = opendir( './upload/'.$ceg['id'] );
            while( ( $file = readdir( $dir ) ) !== false ){
              if( $file == '.' or $file == '..' or substr( $file, 0, strlen( 'g'.api::$request['galeria'].'_' ) ) != 'g'.api::$request['galeria'].'_' )
                continue;
              $kepek[] = $file;
            }
            $res = $res + [ "kepek" => $kepek ];
          }
        }elseif( $galeria = db::list_galeria( $ceg['id'] ) ){
          $kepdb = [];
          $dir = opendir( './upload/'.$ceg['id'] );
          while( ( $file = readdir( $dir ) ) !== false ){
            if( $file == '.' or $file == '..')
              continue;
            $f = explode( '_', $file );
            $kepdb[substr( $f[0], 1 )]++;
          }
          
          $res = $res + [ "galeriak" => $galeria, "kepdb" => $kepdb ];
        }
      break;
      case 'szobak':
        if( $szobak = db::list_accommodationUnits( 'MID(au.signs,1,1)=2', $ceg['id'] ) ){
          foreach( $szobak as $key => $szoba ){
            $szobak[$key]['aktualisar'] = aktualisar( $szoba['id'] );
            $szoba['features'] = ( $szoba['features'] ?? 0 )? json_decode( $szoba['features'], true ) : null;
            if( $szoba['features'] ?? 0 ){
              $jellemzok = db::list_accommodationUnitFeatures( implode( ',', $szoba['features'] ) );
              $szobak[$key]['features'] = $jellemzok;
            }
          }
          $res = $res + [ "szobak" => $szobak ];
        }
      break;
      case 'szoba':
        if( api::$request['id'] ){
          if( $szoba = db::list_accommodationUnits( 'id='.api::$request['id'], $ceg['id'] ) ){
            $szoba['aktualisar'] = aktualisar( $szoba['id'] );
            $szoba['features'] = ( $szoba['features'] ?? 0 )? json_decode( $szoba['features'], true ) : null;
            if( $szoba['features'] ?? 0 ){
              $jellemzok = db::list_accommodationUnitFeatures( implode( ',', $szoba['features'] ) );
              $szoba['features'] = $jellemzok;
            }
            $res = $res + [ "szoba" => $szoba ];
          }
        }
      break;
      case 'foglalas':
        $lakoegysegek = [];
        $fizetendo = $error = $foglalas_id = 0;
        // POST SUBMIT keresés indítás
        if( api::$request['post']->btn_foglalas ?? 0 ){
          $szabad = idoszakszabadszobak( api::$request['post']->erkezes, api::$request['post']->tavozas, api::$request['post']->het );
          if( $szabad['szobak'] ){
            $res = $res + [ "szobak" => $szabad['szobak'] ];
            $rowspan = 0;
            foreach( $szabad['szobak'] as $szoba )
              $rowspan += count( $szoba['aktualisar'] ?? [] );
            $res = $res + [ "rowspan" => $rowspan ];
          }
          $res = $res + [ "haz" => $szabad['haz'], "maxferohely" => $szabad['maxferohely'] ];
        }
        // Foglalás lekérése ID jön
        if( ( api::$request['post']->foglalas_id ?? 0 ) and api::$request['post']->foglalas_id ){
          if( $foglalas = db::get_foglalas( api::$request['post']->foglalas_id ) ){
            $vendegfo = $foglalas['vendegfo'];
            $email = $foglalas['email'];
            $foglalas_id = ( $foglalas['foglalva'][0] != '0' )? 0 : $foglalas['foglalas_id']; // Nem lezárt foglalás akkor 0
            if( $foglaltlakoegysegek = db:: list_foglallakoegyseg( $foglalas['foglalas_id'] ) ){
              $lakoegysegnevek = '';
              foreach( $foglaltlakoegysegek as $foglaltlakoegyseg ){
                $erkezes = $foglaltlakoegyseg['erkezes'];
                $ej = count( json_decode( $foglaltlakoegyseg['napokfo'] ) );
                $tavozas = date( 'Y-m-d', strtotime( $erkezes.' '.$ej.' day' ) );
                if( $lakoegyseg = db::get_lakoegyseg( $foglaltlakoegyseg['lakoegyseg_id'] ) ){
                  if( $foglaltlakoegyseg['lakoegyseg_ar_id'] and $lakoegyseg_ar = db::get_lakoegysegAr( $foglaltlakoegyseg['lakoegyseg_ar_id'] ) )
                    $fizetendo += $ej * $lakoegyseg_ar['ar'];
                  $lakoegyseg['lakoegyseg_ar_id'] = $foglaltlakoegyseg['lakoegyseg_ar_id'];
                  if( $lakoegysegnevek != '' ) $lakoegysegnevek .= ', ';
                  $lakoegysegnevek .= $lakoegyseg['szobaszam'];
                  $lakoegysegek[] = $lakoegyseg;
                }else
                  $error[] = 'Nincs ilyen lakóegység! '.$foglaltlakoegyseg['lakoegyseg_id'];
              }
            }
          }
        }

        // POST SUBMIT keresés eredményéből lakóegység kiválasztva
        if( api::$request['post']->btn_foglal ?? 0 ){
          $erkezes = api::$request['post']->erkezes;
          $tavozas = api::$request['post']->tavozas;
          api::$request['post']->valasztott = json_decode( api::$request['post']->valasztott );
          foreach( api::$request['post']->valasztott as $valaszt ){
            $value = explode( '|', $valaszt );
            if( $lakoegyseg = db::get_lakoegyseg( $value[0] ) ){
              if( $lakoegyseg_ar = db::get_lakoegysegAr( $value[1] ) ){
                $fizetendo += api::$request['post']->ej * $lakoegyseg_ar['ar'];
                $lakoegyseg['lakoegyseg_ar_id'] = $value[1];
                if( $lakoegysegnevek ?? 0 ) $lakoegysegnevek .= ', ';
                $lakoegysegnevek .= $lakoegyseg['szobaszam'];
                $lakoegysegek[] = $lakoegyseg;
              }else
                $error[] = 'Nincs ilyen lakóegység ár! '.$value[1];
            }else
              $error[] = 'Nincs ilyen lakóegység! '.$value[0];
          }
          if( !$error ){
            $eloleg = 0;
            if( ( $ceg['configuration']->events->advancePayment->rate ?? 0 ) and $ceg['configuration']->events->advancePayment->rate > 0 ){
              // van előleg fizetés
              if( round( ( strtotime( $erkezes ) - strtotime( date('Y-m-d') ) ) / 60 / 60 / 24 ) > $ceg['configuration']->events->cancellation->deadline ){
                // van elegendő idő az előleg fizetésre
                $eloleg = round($fizetendo * $ceg['configuration']->events->advancePayment->rate / 100 );
              }else
                $eloleg = $fizetendo;
            }
            if( $foglalas_id = db::save_foglalas( [['ceg_id', $ceg['id'], 'i'],
                                                   ['csatorna_id', 4, 'i'],
                                                   ['eloleg', $fizetendo / 2, 'i'],
                                                   ['fizetendo', $fizetendo, 'i' ],
                                                   ['vendegfo', api::$request['post']->vendegfo, 'i' ],
                                                   ['email', api::$request['post']->email]] ) ){
              $email = api::$request['post']->email;
              $vendegfo = api::$request['post']->vendegfo;
              for( $i = 0; $i < api::$request['post']->ej; $i++ )
                $napokfo[] = 0;
              foreach( $lakoegysegek as $lakoegyseg )
                db::save_foglallakoegyseg([['foglalas_id', $foglalas_id, 'i'],
                                              ['lakoegyseg_id', $lakoegyseg['lakoegyseg_id'], 'i'],
                                              ['lakoegyseg_ar_id', $value[1], 'i'],
                                              ['erkezes', api::$request['post']->erkezes],
                                              ['napokfo', json_encode($napokfo, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)]]);
              foreach( api::$request['post'] as $key => $value ){
                if( substr( $key, 0, 5 ) == 'szolg' ){
                  if( $szolgaltatas = db::get_szolgaltatas( substr( $key, 5 ) ) ){
                    db::save_foglalszolgaltatas([['foglalas_id', $foglalas_id, 'i'],
                                                    ['szolgaltatas_id', substr( $key, 5 ), 'i'],
                                                    ['ar', $szolgaltatas['ar'], 's']]);
                  }
                }
              }

              if( 
                ( $ceg['configuration']->events->booking->messageTemplate->operator ?? 0 )
                and $ceg['configuration']->events->booking->messageTemplate->operator > 0
              )
                // Email a szállásadónak
                if( $template = db::get( DATABASENAME.'.message_templates', $ceg['configuration']->events->booking->messageTemplate->operator ) ){
                  $subject = str_replace( '[FOGLALASID]', $foglalas_id, $template['subject'] );
                  $re = base64_encode( 'foglalas|'.$foglalas_id );
                  $body = str_replace( ['[FOGLALASID]', '[FOGLALASLINK]'], [$foglalas_id, 'https://tren.hu?re='.$re], $template['body'] );
                  
                  $message_id = db::save( DATABASENAME.'.messages', [
                    ['company_id', $ceg['id'], 'i'],
                    ['booking_id', $foglalas_id, 'i'],
                    ['device', 3, 'i'],
                    ['type', 0, 'i'],
                    ['sender', SENDER_EMAIL],
                    ['fromto', $ceg['configuration']->supplierInfo->email],
                    ['name', $subject],
                    ['body', $body]
                  ] );

                  $res = mailer::speedsend(
                    'TREN All-In-One (#'.$message_id.') '.$subject,
                    $body,
                    $ceg['configuration']->supplierInfo->email,
                    SENDER_EMAIL
                  );
                  if( gettype( $res ) == 'boolean' )
                    db::save( DATABASENAME.'.messages', [['sending_time', date( 'Y-m-d H:i:s' )]], $message_id );
                }
            }
          }else{
            // TODO hibaüzenet
          }
        }

        $res = $res + ['foglalas' => ['erkezes' => api::$request['post']->erkezes ?? $erkezes,
                                      'tavozas' => api::$request['post']->tavozas ?? $tavozas,
                                      'het' => api::$request['post']->het ?? 0,
                                      'fo' => $vendegfo,
                                      'lakoegysegnevek' => $lakoegysegnevek,
                                      'ar' => $fizetendo,
                                      'email' => $email,
                                      'telefon' => $foglalas['telefon'] ?? '',
                                      'kapcsolattarto' => $foglalas['kapcsolattarto'] ?? '',
                                      'megjegyzes' => $foglalas['megjegyzes'] ?? '',
                                      'foglalas_id' => $foglalas_id
                                    ],
                      'lakoegysegek' => $lakoegysegek];
      break;
      case 'foglalasaim':
        // POST SUBMIT foglalás végleges mentése
        if( api::$request['post']->btn_lefoglalom ?? 0 ){
          // kapcsolattartó reg-be felvétele
          if( $kapcsolattarto = setup::get_userEmail( api::$request['post']->email ) ){
            // már létezik az adatbázisban
            // TODO email cím jóváhagyás kell vagy jelszó ha van!
            $regkontrolkell = $kapcsolattarto['jelszoi'];
            $kapcsolattarto_id = $kapcsolattarto['reg_id'];
          }else{
            // nincs és felvesszük
            $nev = explode( ' ', api::$request['post']->kapcsolattarto );
            $neve = array_shift( $nev );
            $nevek = '';
            if( count( $nev ) ) $nevek = implode( ' ', $nev );
            $reg = [['email', api::$request['post']->email],
                    ['telefon', api::$request['post']->telefon],
                    ['neve', mb_convert_case( $neve, MB_CASE_TITLE,"utf-8" )],
                    ['nevek', mb_convert_case( $nevek, MB_CASE_TITLE,"utf-8" )],
                    ['becenev', api::$request['post']->kapcsolattarto]
                   ];
            if( ( api::$request['post']->regisztral ?? 0 ) and api::$request['post']->regisztral ){
              // ha kért regisztrációt jelszó generálás
              $psw = setup::create_psw();
              $psw_hash = password_hash( $psw, PASSWORD_BCRYPT );
              $reg = array_merge( $reg, [['jelszoi', $psw], ['password', $psw_hash]] );
            }
            $kapcsolattarto_id = setup::save_reg( $reg );
          }
          if( $kapcsolattarto_id ){
            // Ha nincs még modul része a regisztráltnak
            if( !model::get_regmodul1( $kapcsolattarto_id, $_SESSION['PROID'], $ceg['id'] ) ){
              model::save_regmodul( [['reg_id', $kapcsolattarto_id, 'i'],
                                     ['projekt_id', $_SESSION['PROID'], 'i'],
                                     ['ceg_id', $ceg['id'], 'i']] );
            }
            // Számlázási adatok elmentése vagy frissítése
            $regtax = model::get_regtax( $kapcsolattarto_id, $_SESSION['PROID'], $ceg['id'] );
            model::save_regtax( [['reg_id', $kapcsolattarto_id, 'i'],
                                  ['projekt_id', $_SESSION['PROID'], 'i'],
                                  ['ceg_id', $ceg['id'], 'i'],
                                  ['adohu', api::$request['post']->szadosz ?? ''],
                                  ['snev', api::$request['post']->sznev],
                                  ['scim_telepules', api::$request['post']->szirsz.' '.api::$request['post']->sztelepules],
                                  ['scim_pontos', api::$request['post']->szutca]
                                ],
                                ( $regtax )? ['regtax_id', $regtax['regtax_id'], 'i'] : 0 );
            // Foglalás véglegesítése
            db::save_foglalas( [['reg_id', $kapcsolattarto_id, 'i'],
                                ['foglalva', date('Y-m-d H:i:s')],
                                ['telefon', api::$request['post']->telefon],
                                ['kapcsolattarto', api::$request['post']->kapcsolattarto],
                                ['megjegyzes', api::$request['post']->megjegyzes]], api::$request['post']->foglalas_id );

            // Email küldés a foglalásról
            mailer::speedsend( 'Foglalás ['.api::$request['post']->foglalas_id.'] érkezett '.date('Y-m-d H:i:s'),
                               'Megtekinthető: <a href="https://trsoft.hu/naptar/modosit/'.api::$request['post']->foglalas_id.'">ITT</a>',
                               'Tölgyfa panzió <<EMAIL>>',
                               mb_convert_case( $neve, MB_CASE_TITLE,"utf-8" ).' '.mb_convert_case( $nevek, MB_CASE_TITLE,"utf-8" ).
                               '<'.api::$request['post']->email.'>' );
            
            // Visszaigazoló oldal TODO kidolgozni!
            //if( $page = db::get_page( 'Foglalás köszönő!', $ceg['id'] ) )
            //  $res = $res + [ "ok" => true, "page" => $page ];
            $res = $res + [ "ok" => true ];
          }
        }
      break;
    }
  }
  response::add( 'api', $res );
}else $res = api::$error;
api::response( $res );

function idoszakszabad7( $het ){
  global $ceg;
  $erkezesek = [];
  if( $idoszakok = db::list_idoszakok( $ceg['id'], $het ) ){
    foreach( $idoszakok as $idoszak ){
      for( $i = 0; $i < 7; $i++ ){
        if( $idoszak['erknap'][$i] ){
          if( isset( $erkezesek[$i + 1] ) ){
            if( $erkezesek[$i + 1]['kezd'] > $idoszak['kezd'] )
              $erkezesek[$i + 1]['kezd'] = $idoszak['kezd'];
            if( $erkezesek[$i + 1]['vege'] < $idoszak['vege'] )
              $erkezesek[$i + 1]['vege'] = $idoszak['vege'];
          }else
            $erkezesek[$i + 1] = ['kezd' => $idoszak['kezd'], 'vege' => $idoszak['vege']];
        }
      }
    }
  }
  if( count( $erkezesek ) ){
    foreach( $erkezesek as $nap => $idoszak ){
      if( date('m.d') < $idoszak['kezd'] )
        $erkezes = date( 'Y-' ).str_replace('.', '-', $idoszak['kezd']);
      else
        $erkezes = date( 'Y-m-d' );
      if( date( 'N' ) <= $nap )
        $erkezes = date( 'Y-m-d', strtotime( $erkezes.' +'.( $nap-date( 'N' ) ).' day' ) );
      else
        $erkezes = date( 'Y-m-d', strtotime( $erkezes.' +'.( 7-date( 'N' )+$nap ).' day' ) );
      while( $erkezes < date( 'Y.' ).$idoszak['vege'] ){
        $szabad = idoszakszabadszobak( $erkezes, date( 'Y-m-d', strtotime( $erkezes.' +7 day' ) ), $het );
        if( ($szabad['szobak']?? 0) and count($szabad['szobak']))
          $szabadidoszak[] = $erkezes.'-'.date( 'Y-m-d', strtotime( $erkezes.' +7 day' ) );
        $erkezes = date( 'Y-m-d', strtotime( $erkezes.' +7 day' ) );
      }
    }
  }
  return $szabadidoszak ?? false;
}

function idoszakszabadszobak( $erkezes, $tavozas, $het = NULL ){
  global $ceg;
  if( $foglaltlakoegysegek = db::list_foglallakoegysegFoglalt( $ceg['id'], $erkezes, $tavozas ) ){
    foreach( $foglaltlakoegysegek as $lakoegyseg ) $list[] = $lakoegyseg['lakoegyseg_id'];
      $foglaltlakoegysegek = $list;
  }
  $maxf = 0;
  $ej = ( strtotime( $tavozas ) - strtotime( $erkezes ) ) / 60 / 60 / 24;

  if( $het == 2 ){ // teljes ház
    if( !$foglaltlakoegysegek ){
      if( $arak = db::get_arTeljes( $ceg['id'], date( 'm-d', strtotime( $erkezes ) ) ) ){
        $szobak[] = ['egyfosagyszam' => $arak['egyfosagyszam'],
                     'ketfosagyszam' => $arak['ketfosagyszam'],
                     'leirasrovid' => $arak['leirasrovid'],
                     'leiras' => $arak['leiras'],
                     'aktualisar' => ['arnap' => $arak['arnap'],
                                      'maxfo' => $arak['erknap'],
                                      'maxfo' => $arak['maxfo'],
                                         'id' => $arak['lakoegyseg_ar_id'],
                                         'ar' => $arak['ar']]];
      }
    }
  }else{
    if( $szobak = db::list_lakoegyseg( $ceg['id'], 1 , ( $foglaltlakoegysegek )? implode( ',', $foglaltlakoegysegek ) : false ) )
      foreach( $szobak as $key => $szoba ){
        if( $arak = db::list_lakoegysegAr( $szoba['lakoegyseg_id'], date( 'm-d', strtotime( $erkezes ) ) ) ){
          $minnap = 1;
          foreach( $arak as $ar ){
            if( !is_null( $het ) and $ej >= $ar['minnap'] ){
              if( ( $het and $ar['arnap'] == 7 ) or
                  ( !$het and $ar['arnap'] != 7 ) ){
                $szobak[$key]['aktualisar'][] = ['arnap' => $ar['arnap'],
                                                 'minnap'=> $ar['minnap'],
                                                 'maxfo' => $ar['maxfo'],
                                                    'id' => $ar['lakoegyseg_ar_id'],
                                                    'ar' => $ar['ar']];
                if( $ar['arnap'] == 1 and $ar['minnap'] > 1 and $ar['minnap'] > $minnap )
                  $minnap = $ar['minnap'];
              }
            }
          }
          if( $minnap > 1 )
            foreach( $szobak[$key]['aktualisar'] as $key2 => $ar )
              if( $ar['arnap'] == 1 and $ar['minnap'] < $minnap )
                unset( $szobak[$key]['aktualisar'][$key2] );
        }
        $maxf += $szoba['ferohely'];
      }
  }
  return ['haz'=> ( $foglaltlakoegysegek )? 0 : 1, 'maxferohely' => $maxf, 'szobak'=> $szobak];
}

function aktualisar( $lakoegyseg_id ){
  $arki = '';
  if( $arak = db::list_lakoegysegAr( $lakoegyseg_id, date('m-d') ) ){
    foreach( $arak as $ar ){
      $arki.= ( ( $arki != '' )? '<br>' : '' ) . number_format( $ar['ar'], 0, '.', ' ') . '&nbsp;Ft/' . ( ( $ar['arnap']-1 )? $ar['arnap'] : '' ) .'éj' . ( ( $ar['maxfo'] )? '/' . $ar['maxfo'].'fő' : '' );
      if( $ar['erknap'] == '1111111' )
        $arki.= '<br>Érkezés bármelyik nap!';
      else{
        $arki.= '<br>Lehetséges érkezés:';
        for( $i=0; $i<7; $i++ )
          $arki.= ( $ar['erknap'][$i] )? ' '.['Hétfő','Kedd','Szerda','Csütörtök','Péntek','Szombat','Vasárnap'][$i] : '';
     }
      if( $ar['arnap'] == 1 and $ar['minnap'] > 1 ){
        $arki.= '<br>Minimum éjszakák száma: '.$ar['minnap']. 'éj';
      }
      $arki.= '<br>';
    }
  }
  return ( $arki != '' )? $arki : 0;
}