@charset "UTF-8";
/* Reset - alap<PERSON><PERSON><PERSON><PERSON><PERSON> */
::before, ::after, * {
  box-sizing: border-box;
  text-rendering: optimizeLegibility;
}
* {
  margin: 0;
  padding: 0;
  &:focus {
    outline: none;
  }
  /*
  &:focus-visible {
    outline: 2px solid magenta;
    outline-offset: 2px;
  }
  */
}
:root { isolation: isolate }
body {
  min-height: 100vh;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}
img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
}
img, fieldset { border: none }
p, h1, h2, h3, h4, h5, h6 { overflow-wrap: break-word }
input, button, textarea, select { font: inherit }
button,
input[type=button],
input[type=reset],
input[type=submit],
input[type=checkbox],
input[type=radio],
select{ cursor: pointer }
button[disabled],
input[disabled] { cursor: default }