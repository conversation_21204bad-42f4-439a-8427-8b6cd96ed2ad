<header>
  <h6>Üzenet</h6>
  <div>
    <span style="--icon:var(--icon-resize-full)" class="fullscreen" data-fsicon="--icon-resize-full|--icon-resize-small"></span>
    <span class="close" style="--icon:var(--icon-cancel)"></span>
  </div>
</header>
<section>
  <form name="form_message">
    <input type="hidden" name="id" value="<?= $_POST['id'] ?>">
    <ul class="formbox">
      <li class="form col7">
        <input type="text" name="sending_time" placeholder="" value="<?= $_POST['sending_time'] ?? '' ?>" disabled>
        <label>Küldés ideje</label>
      </li>
      <li class="form col3">
        <button class="send"<?= ( $_POST['booking_id'] and ( $_POST['to'] ?? 0 ) and $_POST['to'] != '' )? '' : ' disabled' ?>><?= ( $_POST['sending_time'] ?? 0 )? 'Újra küld' : 'Küld' ?></button>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="device" placeholder="">
          <option value="3" selected>Email</option>
        </select>
        <label>Eszköz</label>
      </li>
      <li class="form col5" style="--toicon:var(--icon-angle-double-down)">
        <select name="type" placeholder="" disabled>
          <option value="0"<?= ($_POST['type'] == 0 )? ' selected' : '' ?>>Rendszertől</option>
          <option value="1"<?= ($_POST['type'] == 1 )? ' selected' : '' ?>>Szállásadótól</option>
          <option value="2"<?= ($_POST['type'] == 2 )? ' selected' : '' ?>>Foglalótól</option>
        </select>
        <label>Tipus</label>
      </li>
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="booking_id" placeholder="">
          <option value="0">Válasz foglalást</option>
          <?php foreach( response::$vw->view->bookings as $booking ){ ?>
          <option value="<?= $booking->foglalas_id ?>"<?= ( $_POST['booking_id'] == $booking->foglalas_id )? ' selected' : '' ?>><?= $booking->foglalas_id.' '.$booking->kapcsolattarto.' '.$booking->email ?></option>
          <?php } ?>
        </select>
        <label>Foglalás</label>
      </li>
      <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
        <select name="template_id" placeholder="">
          <option value="0">Sablon nélkül</option>
          <?php foreach( response::$vw->view->templates as $template ){ ?>
          <option value="<?= $template->id ?>"<?= ( $_POST['template_id'] == $template->id )? ' selected' : '' ?>><?= $template->name ?></option>
          <?php } ?>
        </select>
        <label>Sablon</label>
      </li>
      <li class="form col0">
        <input type="text" name="sender" placeholder="" value="<?= $_POST['sender'] ?>" readonly>
        <label>Feladó</label>
      </li>
      <li class="form col0">
        <input type="text" name="to" placeholder="" value="<?= $_POST['to'] ?? '' ?>" readonly>
        <label>Címzett</label>
      </li>
      <li class="form col0">
        <input type="text" name="subject" placeholder="" value="<?= $_POST['subject'] ?? '' ?>">
        <label>Tárgy szöveg</label>
      </li>
      <li class="form col0">
        <textarea id="joditeditor" name="body"><?= html_entity_decode($_POST['body'] ?? '')?></textarea>
        <label>Üzenet</label>
      </li>
    </ul>
  </form>
</section>
<footer>
  <button class="close">Kilép</button>
  <?php if( !( $_POST['sending_time'] ?? 0  ) ){ ?>
  <button class="callback" name="btn_modositas" title="Sablon mentése">Módosít</button>
  <?php } ?>
</footer>