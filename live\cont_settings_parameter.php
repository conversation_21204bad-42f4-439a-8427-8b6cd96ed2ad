<?php
if( $_POST['btn_settings_parameter'] ?? 0 ){
  http::cleanPost();
  if( !( response::$vw->error ?? 0 ) ){
    setup::$company->name = $_POST['name'];
    setup::$company->configuration->supplierInfo->ntakNumber = $_POST['ntak_number'] ?? '';
    setup::$company->configuration->supplierInfo->registrationNumber = $_POST['registration_number'] ?? '';
    setup::$company->configuration->supplierInfo->accommodation_units_number = $_POST['accommodation_units_number'] ?? 1;
    setup::$company->configuration->supplierInfo->number_of_places = $_POST['number_of_places'] ?? 1;
    if( !isset( setup::$company->configuration->events )) setup::$company->configuration->events = (object) [];
    if( !isset( setup::$company->configuration->events->advancePayment )) setup::$company->configuration->events->advancePayment = (object) [];
    setup::$company->configuration->events->advancePayment->rate = $_POST['advance_payment'];
    setup::$company->configuration->events->advancePayment->deadline = $_POST['advance_payment_deadline'];
    if( !isset( setup::$company->configuration->events->cancellation )) setup::$company->configuration->events->cancellation = (object) [];
    setup::$company->configuration->events->cancellation->deadline = $_POST['cancellation_deadline'];
    if( !isset( setup::$company->configuration->events->dataRequest )) setup::$company->configuration->events->dataRequest = (object) [];
    setup::$company->configuration->events->dataRequest->deadline = $_POST['data_request_deadline'];
    if( setup::is_right( 0 )){
      if( !isset( setup::$company->configuration->invoiceNav )) setup::$company->configuration->invoiceNav = (object) [];
      setup::$company->configuration->invoiceNav->login = $_POST['invoice_nav_login'];
      setup::$company->configuration->invoiceNav->passwd = $_POST['invoice_nav_passwd'];
      setup::$company->configuration->invoiceNav->xmlKey = $_POST['invoice_nav_xml_key'];
      setup::$company->configuration->invoiceNav->xmlKeyChange = $_POST['invoice_nav_xml_key_change'];
      setup::$company->configuration->invoiceNav->accountBlock = $_POST['invoice_nav_account_block'];
    }
    setup::save_configuration( $_SESSION['COMPANY'], setup::$company->configuration );
    setup::save( 'shared.companies', [['name', setup::$company->name]], ['id', $_SESSION['COMPANY'], 'i'] );
  }
}
$_POST['name'] = $_POST['name'] ?? setup::$company->name ?? '';
$_POST['ntak_number'] = $_POST['ntak_number'] ?? setup::$company->configuration->supplierInfo->ntakNumber ?? '';
$_POST['registration_number'] = $_POST['registration_number'] ?? setup::$company->configuration->supplierInfo->registrationNumber ?? '';
$_POST['accommodation_units_number'] = $_POST['accommodation_units_number'] ?? setup::$company->configuration->supplierInfo->accommodation_units_number ?? 1;
$_POST['number_of_places'] = $_POST['number_of_places'] ?? setup::$company->configuration->supplierInfo->number_of_places ?? 1;
$_POST['advance_payment'] = $_POST['advance_payment'] ?? setup::$company->configuration->events->advancePayment->rate ?? 0;
$_POST['advance_payment_deadline'] = $_POST['advance_payment_deadline'] ?? setup::$company->configuration->events->advancePayment->deadline ?? '';
$_POST['cancellation_deadline'] = $_POST['cancellation_deadline'] ?? setup::$company->configuration->events->cancellation->deadline ?? '';
$_POST['data_request_deadline'] = $_POST['data_request_deadline'] ?? setup::$company->configuration->events->dataRequest->deadline ?? '';

$_POST['invoice_nav_login'] = $_POST['invoice_nav_login'] ?? setup::$company->configuration->invoiceNav->login ?? '';
$_POST['invoice_nav_passwd'] = $_POST['invoice_nav_passwd'] ?? setup::$company->configuration->invoiceNav->passwd ?? '';
$_POST['invoice_nav_xml_key'] = $_POST['invoice_nav_xml_key'] ?? setup::$company->configuration->invoiceNav->xmlKey ?? '';
$_POST['invoice_nav_xml_key_change'] = $_POST['invoice_nav_xml_key_change'] ??  setup::$company->configuration->invoiceNav->xmlKeyChange ?? '';
$_POST['invoice_nav_account_block'] = $_POST['invoice_nav_account_block'] ?? setup::$company->configuration->invoiceNav->accountBlock ?? '';