<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');
$res = [];
if( setup::$company = api::authentication((( http::$route[3] ?? 0 ) and http::$route[3] == 'documentation' )? '' : 'apikey' ) ){
  array_shift( http::$route );
  array_shift( http::$route );
  array_shift( http::$route );
  $res = ( in_array( http::$route[0] ?? '', ['pages'] ))
    ? $res + [ "ceg" => setup::$company ]
    : [];

  if(( setup::$company['configuration']->is_booking ?? 0 ) and setup::$company['configuration']->is_booking ){
    $foglalasdatemin = date( 'Y-m-d', strtotime( '1 day' ));
    $foglalasdatemax = date( 'Y-12-31' );
    $res = $res + ["foglalasdatemin" => $foglalasdatemin, "foglalasdatemax" => $foglalasdatemax];
  }

  if(( setup::$company['configuration']->is_occupancy ?? 0 ) and setup::$company['configuration']->is_occupancy and ( api::$request['foglaltsag'] ?? false )){
    $foglalt = [];
    if( $szobak = db::list_accommodationUnits( 'MID(au.signs,1,1)=2', setup::$company['id'] ) )
      foreach( $szobak as $szoba )
        $foglalt[ $szoba['name'].' max '.$szoba['places'].' fő' ] = '';
    if( $foglalasok = db::list_foglalt( date( 'Y-m-d' ), setup::$company['id'] ) ){
      $le = '';
      $nap = $napmax = date( 'Y-m-d' );
      foreach( $foglalasok as $foglalas ){
        if( $foglalas['name'] <> $le ){
          $le = $foglalas['name'];
          $nap = date( 'Y-m-d' );
          $utolso='';
        }
        if( $nap > $foglalas['erkezes'] ){
          if( $nap == $foglalas['tavozas'] ){
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'T';
            $utolso = 'T';
            $utolsonap = $nap;
          }else{
            $difnap = ( strtotime( $foglalas['tavozas'] ) - strtotime( $nap ) ) / 24 / 60 / 60;
            if( $difnap > 1 )
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'F', $difnap );
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'T';
            $utolsonap = $nap = $foglalas['tavozas'];
            $utolso = 'T';
          }
        }else{
          if( $nap == $foglalas['erkezes'] ){
            if( $utolso == 'T' ){
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] = substr( $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ],0,strrpos( $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ],$foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ][strlen( $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ])-1] ) );
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'C';
            }else
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'E';
          }else{
            $difnap = ( strtotime( $foglalas['erkezes'] ) - strtotime( $nap ) ) / 24 / 60 / 60;
            if( $utolso == 'T' )
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'S', $difnap - 1 );
            else
              $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'S', $difnap );
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'E';
            $nap = $foglalas['erkezes'];
          }
          $difnap = ( strtotime( $foglalas['tavozas'] ) - strtotime( $nap ) ) / 24 / 60 / 60;
          if( $difnap > 1 )
            $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= str_repeat( 'F', $difnap -1 );
          $foglalt[ $le.' max '.$foglalas['ferohely'].' fő' ] .= 'T';
          $nap = $foglalas['tavozas'];
          $utolso = 'T';
        }
      }

      if( api::$request['maxkelt'] ?? 0 ){
        $difnap = ( strtotime( api::$request['maxkelt'] ) - strtotime( date('Y-m-d') ) ) / 24 / 60 / 60;
        if( $difnap > 0 and $difnap < ( api::$request['maxnap'] ?? 30 ) )
          api::$request['maxnap'] = $difnap;
        if( $difnap < 0 )
          api::$request['maxnap'] = 0;
      }
      foreach( $foglalt as $szoba => $foglalas ){
        $foglalt[$szoba] .= str_repeat( 'S', api::$request['maxnap'] ?? 30 );
        $foglalt[$szoba] = substr($foglalt[$szoba], 0, api::$request['maxnap'] ?? 30 );
      }
      $res = $res + [ "foglalt" => $foglalt ];
    }
  }

  if( api::$request['szolgaltatasok'] ?? 0 ){
    if( $szolgaltatas = db::list_szolgaltatas() )
      $res = $res + [ "szolgaltatasok" => $szolgaltatas ];
  }

  switch( $_SERVER['REQUEST_METHOD'] ?? 'GET' ){
    case 'GET':
      switch( http::$route[0] ?? 0 ){
        case 'pages':
          switch( http::$route[1] ?? 0 ){
            case 'home':
              $reviews = db::list_reviews( setup::$company['id'], 'MID(r.signs,1,1) = 2' );
              $res = $res + [ "reviews" => $reviews ];
              $ratings = db::list_ratings( setup::$company['id'], 'MID(r.signs,2,1) = 1' );
              $res = $res + [ "ratings" => $ratings ];
              if( http::$route[2] ?? 0 ){
                api::verifyToken( http::$route[2] );
                $menu_link = http::$route[1];
                $ceg_id = setup::$company['id'];
                require_once 'shared/modul/cont_page.php';
                $res = $res + [ "page" => $page ];
              }
            break;
            case 'accommodation_units':
              if(( http::$route[2] ?? 0 ) and is_numeric( http::$route[2] )){
                if( $szoba = db::list_accommodationUnits( 'au.id='.http::$route[2], setup::$company['id'] )){
                  $szoba = $szoba[0];
                  $szoba['aktualisar'] = curentPrices( $szoba['id'] );
                  $szoba['features'] = ( $szoba['features'] ?? 0 )? json_decode( $szoba['features'], true ) : null;
                  if( $szoba['features'] ?? 0 ){
                    $jellemzok = db::list_accommodationUnitFeatures( implode( ',', $szoba['features'] ));
                    $szoba['features'] = $jellemzok;
                  }
                  $res = $res + [ "szoba" => $szoba ];
                }
              }elseif( $szobak = db::list_accommodationUnits( 'MID(au.signs,1,1)=2', setup::$company['id'] )){
                foreach( $szobak as $key => $szoba ){
                  $szobak[$key]['aktualisar'] = curentPrices( $szoba['id'] );
                  $szoba['features'] = ( $szoba['features'] ?? 0 )? json_decode( $szoba['features'], true ) : null;
                  if( $szoba['features'] ?? 0 ){
                    $jellemzok = db::list_accommodationUnitFeatures( implode( ',', $szoba['features'] ));
                    $szobak[$key]['features'] = $jellemzok;
                  }
                }
                $res = $res + [ "szobak" => $szobak ];
              }
            break;
            case 'galleries':
              if( $galleries = db::list( DATABASENAME.'.galleries', 'company_id='.setup::$company['id'], 'name' )){
                $res = $res + [ 'galleries' => $galleries ];
                $kepek = [];
                if(( http::$route[2] ?? 0 ) and is_numeric( http::$route[2] ) and $gallery = db::get( DATABASENAME.'.galleries', intval( http::$route[2] ))){
                  $res = $res + [ "gallery" => $gallery ];
                  
                  $sequence = json_decode( $gallery['image_sequence'], true );
                  $sequence = array_map( fn( $value ): int => intval( $value ), $sequence );
                  foreach( $sequence as $pic ){
                    if( file_exists('upload/'.setup::$company['id'].'/pic'.sprintf( "%04d", $pic ).'.jpg' ) )
                      $kepek[] = 'pic'.sprintf( "%04d", $pic ).'.jpg';
                  }
                }else
                  foreach( $galleries as $gallery )
                    $sequence = json_decode( $gallery['image_sequence'], true );
                    $sequence = array_map( fn( $value ): int => intval( $value ), $sequence );
                    foreach( $sequence as $pic )
                      if( file_exists('./upload/'.setup::$company['id'].'/pic'.sprintf( "%04d", $pic ).'.jpg' ) )
                        $kepek[] = 'pic'.sprintf( "%04d", $pic ).'.jpg';
                $res = $res + ["images" => $kepek]; 
              }else
                $res = $res + ['error' => ['code' => 404, 'message' => 'Gallery not found', 'details' => '']];
            break;
          }
        break;
        case 'booking': // Foglalt időpontokat adja vissza lakóegységenként
          if( $foglalasok = db::list_foglalt( date( 'Y-m-d' ), setup::$company['id'] ) ){
            $foglalt = [];
            $le = '';
            $nap = $napmax = date( 'Y-m-d' );
            foreach( $foglalasok as $foglalas ){
              if( $foglalas['lakoegyseg_id'] <> $le ){
                $le = $foglalas['lakoegyseg_id'];
                $nap = date( 'Y-m-01' );
                $utolso='';
                $foglalt[ $le ] = '';
              }
              if( $nap > $foglalas['erkezes'] ){
                if( $nap == $foglalas['tavozas'] ){
                  $foglalt[ $le ] .= '3';
                  $utolso = 'T';
                  $utolsonap = $nap;
                }else{
                  $difnap = round( ( strtotime( $foglalas['tavozas'] ) - strtotime( $nap ) ) / 24 / 60 / 60 );
                  if( $difnap > 1 )
                    $foglalt[ $le ] .= str_repeat( '2', $difnap );
                  $foglalt[ $le ] .= '3';
                  $utolsonap = $nap = $foglalas['tavozas'];
                  $utolso = 'T';
                }
              }else{
                if( $nap == $foglalas['erkezes'] ){
                  if( $utolso == 'T' ){
                    $foglalt[ $le ] = substr( $foglalt[ $le ],0,strrpos( $foglalt[ $le ],$foglalt[ $le ][strlen( $foglalt[ $le ])-1] ) );
                    $foglalt[ $le ] .= '2';
                  }else
                    $foglalt[ $le ] .= '1';
                }else{
                  $difnap = round( ( strtotime( $foglalas['erkezes'] ) - strtotime( $nap ) ) / 24 / 60 / 60 );
                  if( $utolso == 'T' )
                    $foglalt[ $le ] .= str_repeat( '0', $difnap - 1 );
                  else
                    $foglalt[ $le ] .= str_repeat( '0', $difnap );
                  $foglalt[ $le ] .= '1';
                  $nap = $foglalas['erkezes'];
                }
                $difnap = round( ( strtotime( $foglalas['tavozas'] ) - strtotime( $nap ) ) / 24 / 60 / 60 );
                if( $difnap > 1 )
                  $foglalt[ $le ] .= str_repeat( '2', $difnap - 1 );
                $foglalt[ $le ] .= '3';
                $nap = $foglalas['tavozas'];
                $utolso = 'T';
              }
            }
            $reserved = $foglalt;
          }else $reserved = false;
          $res = $res + [ 'reserved' => $reserved ];
        break;
        case 'accommodationfee': // Egy adott lakóegység szállásköltségét adja vissza
          if( http::$route[1] ?? 0 )
            api::verifyToken( http::$route[1] );
          $accommodationfee = periodAccommodationFee(
            api::$request['accommodation_unit_id'],
            strtotime( api::$request['start'] ),
            strtotime( api::$request['stop'] ),
            api::$request['guests'] ?? 1
          );
          $res = $res + [ 'guests' => api::$request['guests'] ?? 1, 'accommodationfee' => $accommodationfee ];
        break;
        case 'coupon': // Kupon kódot ellenőriz és ha érvényes kedvezményt számol
          if( http::$route[1] ?? 0 )
            api::verifyToken( http::$route[1] );
          if( $coupon = db::get( DATABASENAME.'.coupons', 'company_id='.setup::$company['id'].' AND code="'.api::$request['couponcode'].'"' ) ){
            if( $coupon['validity_start'] <= date( 'Y-m-d' ) and $coupon['validity_end'] >= date( 'Y-m-d' ) and
                $coupon['booking_period_start'] <= api::$request['start'] and $coupon['booking_period_end'] >= api::$request['stop'] and
                $coupon['signs'][0] == '2' and ( $coupon['signs'][2] == '0' or $coupon['redemption'] == 0 ) ){
              $days = ( strtotime( api::$request['stop'] ) - strtotime( api::$request['start'] )) / 24 / 60 / 60;
              if( $coupon['min_booking_days'] <= $days )
                if( $coupon['signs'][1] == '0' or $coupon['signs'][1] == '1' )
                  $res = $res + [ 'discount' => ( $coupon['signs'][1] == '0' )?
                    $coupon['discount'] : 
                    round( api::$request['accommodationfee'] * $coupon['discount'] / 100 ), 'description' => $coupon['description'] ];
                else{
                  // napi kedvezmény
                  $discount_day = 0;
                  $step = strtotime( api::$request['start'] );
                  while( $step < strtotime( api::$request['stop'] ) ){
                    $weekDay = date( 'N', $step );
                    if( $coupon['weekly_start_day'] <= $coupon['weekly_stop_day'] ){
                      if( $weekDay >= $coupon['weekly_start_day'] and $weekDay <= $coupon['weekly_stop_day'] ) $discount_day++;
                    }else{
                      if( $weekDay >= $coupon['weekly_start_day'] ) $discount_day++;
                      if( $weekDay <= $coupon['weekly_stop_day'] ) $discount_day++;
                    }
                    $step += 24*60*60;
                  }
                  $res = $res + [ 'discount' => $discount_day * $coupon['discount'], 'description' => $coupon['description'] ];
                }
              else
                $res = $res + [ 'discount' => 0, 'description' => 'Kevés a foglalt napok száma! min: '.$coupon['min_booking_days'].' nap' ];   
            }else
              $res = $res + [ 'discount' => 0, 'description' => 'Nincs ilyen érvényes kupon!' ];
          }else
            $res = $res + [ 'discount' => 0, 'description' => 'Nincs ilyen érvényes kupon!' ];
        break;
        case 'extraselect': // Kiegészítő szolgáltatások listáját adja vissza html <ul> tegbenben
          $services = db::list_services( setup::$company['id'] );
          if( $services ){
            $html = '<ul class="formbox">';
            foreach( $services as $service ){
              switch( $service['signs'][0] ){
                case 'T':
                  $html.=
                    '<li class="extra form col5">'.
                    '  <img src="https://tren.hu/upload/'.setup::$company['id'].'/ser_'.$service['id'].'.jpg">'.
                    '  <h3>'.$service['name'].'</h3>'.
                    '  <p>'.$service['description'].'</p>'.
                    '  <center><a class="btn" href="https://'.$service['link'].'" target="_blank">Rendelek</a></center>'.
                    '</li>';
                break;
                case 'K':
                  $html.=
                    '<li class="extra form col5">'.
                    '  <img src="https://tren.hu/upload/'.setup::$company['id'].'/ser_'.$service['id'].'.jpg">'.
                    '  <h3>'.$service['name'].'</h3>'.
                    ( $service['price'] ? '<span>'.$service['price'].' '.$service['unit'].'</span>' : '' ).
                    '  <p>'.$service['description'].'</p>';
                  if( $service['price'] ){
                    
                  }else{
                    $html.=
                      '  <select name="ser'.$service['id'].'" data-id="'.$service['id'].'" data-name="'.$service['name'].'">'.
                      '    <option value="0">Nem kérek</option>';
                    $selects = json_decode( $service['selects'], true );
                    foreach( $selects as $select )
                      $html.= '<option value="'.$select['price'].'">'.$select['text'].'</option>';
                    $html.=
                    '  </select>'.
                    '  <label>Mennyiség</label>';
                  }
                  $html.= '</li>';
                break;
              }
            }
            $html.= '</ul>';
            $services = $html;
          }
          $res = $res + ['extraselect' => $services ];
        break;
        case 'advertall': // Felugró ablak vizsgálata, hogy szükséges e a megjelenítése és html generálása
          if( http::$route[1] ?? 0 )
            api::verifyToken( http::$route[1] );
          if( api::$request['create'] ?? 0 )
            $dif = round( ( strtotime( date( 'Y-m-d H:i:s' ) ) - strtotime( api::$request['create'] ) ) );
          if( !( $dif ?? 0 ) or $dif > 60 ){
            if( $popup = db::get(
              DATABASENAME.'.popups',
              'company_id='.setup::$company['id'].' AND
               MID(signs,1,1)>1 AND 
               validity_start<="'.date( 'Y-m-d' ).'" AND 
               validity_end>="'.date( 'Y-m-d' ).'"' )
            ){
              $res = $res + [ 'advertall' => [
                'data' => [
                  'page' => api::$request['page'],
                  'create' => date( 'Y-m-d H:i:s' ),
                  'popup_id' => $popup['id']
                ],
                'popup' =>
                  '<dialog class="advertall">'.
                  '  <img src="https://tren.hu/upload/'.setup::$company['id'].'/pic'.sprintf( "%04d", $popup['image_id'] ).'.webp" alt="">'.
                  '  <a>X</a>'.
                  ( ( $popup['link'] ?? 0 )?
                  '  <div><a class="btn" href="'.$popup['link'].'">'.$popup['link_name'].'</a></div>' :
                  '' ).
                  '</dialog>'.
                  '<script>
                    document.addEventListener( "DOMContentLoaded", () => {
                      const
                        advertall = document.querySelector( "dialog.advertall" ),
                        advertallClose = advertall.querySelector( "a:not(.btn)" )
                      advertallClose.addEventListener( "click", ( ec ) => {
                        advertall.close()
                      } )
                      advertall.showModal()
                    } )
                  </script>'
              ]];
            }
          }
        break;
      }
    break;
    case 'POST':
      switch( http::$route[0] ?? 0 ){
        case 'pages':
          switch( http::$route[1] ?? 0 ){
            case 'booking':
              $lakoegysegek = [];
              $fizetendo = $error = $foglalas_id = 0;
              // POST SUBMIT keresés indítás
              if( api::$request['post']['btn_foglalas'] ?? 0 ){
                $szabad = bookableAccommodationUnits( api::$request['post']['erkezes'], api::$request['post']['tavozas'] );
                if( $szabad ){
                  $res = $res + [ "szobak" => $szabad ];
                  $rowspan = 0;
                  foreach( $szabad as $szoba )
                    $rowspan += count( $szoba['opportunities'] ?? [] );
                  $res = $res + [ "rowspan" => $rowspan ];
                }
                //$res = $res + [ "haz" => $szabad['haz'], "maxferohely" => $szabad['maxferohely'] ];
              }
              // Foglalás lekérése ID jön
              if(( api::$request['post']['foglalas_id'] ?? 0 ) and api::$request['post']['foglalas_id'] ){
                if( $foglalas = db::get_foglalas( api::$request['post']['foglalas_id'] )){
                  $vendegfo = $foglalas['vendegfo'];
                  $email = $foglalas['email'];
                  $fizetendo = $foglalas['fizetendo'];
                  $foglalas_id = ( $foglalas['foglalva'] ?? 0 )? 0 : $foglalas['foglalas_id']; // Nem lezárt foglalás akkor 0
                  if( $foglaltlakoegysegek = db::list_foglallakoegyseg( $foglalas['foglalas_id'] )){
                    $lakoegysegnevek = '';
                    foreach( $foglaltlakoegysegek as $foglaltlakoegyseg ){
                      $erkezes = $foglaltlakoegyseg['erkezes'];
                      $ej = count( json_decode( $foglaltlakoegyseg['napokfo'] ));
                      $tavozas = date( 'Y-m-d', strtotime( $erkezes.' '.$ej.' day' ));
                      if( $lakoegysegnevek != '' ) $lakoegysegnevek .= ', ';
                      $lakoegysegnevek .= $foglaltlakoegyseg['name'];
                      if( $lakoegyseg = db::get( DATABASENAME.'.accommodation_units', intval( $foglaltlakoegyseg['lakoegyseg_id'] )))
                        $lakoegysegek[] = $lakoegyseg;
                      else
                        $error[] = 'Nincs ilyen lakóegység! '.$foglaltlakoegyseg['lakoegyseg_id'];
                    }
                  }else $deleteCookie = 1;
                }
              }
      
              // POST SUBMIT keresés eredményéből lakóegység kiválasztva
              if( api::$request['post']['btn_foglal'] ?? 0 ){
                $erkezes = api::$request['post']['erkezes'];
                $tavozas = api::$request['post']['tavozas'];
                api::$request['post']['valasztott'] = json_decode( api::$request['post']['valasztott'] );
                $lakoegysegnevek = '';
                foreach( api::$request['post']['valasztott'] as $valaszt ){
                  $valasztottLakoegyseg = explode( '|', $valaszt );
                  if( $lakoegyseg = db::get( DATABASENAME.'.accommodation_units', intval( $valasztottLakoegyseg[0] ))){
                    if( $lakoegyseg_ar = periodAccommodationFee( $lakoegyseg['id'], strtotime( $erkezes ), strtotime( $tavozas ), $valasztottLakoegyseg[1] )){
                      $fizetendo += $lakoegyseg_ar;
                      if( $lakoegysegnevek ?? 0 ) $lakoegysegnevek .= ', ';
                      $lakoegysegnevek .= $lakoegyseg['name'];
                      $lakoegysegek[] = $lakoegyseg;
                    }else
                      $error[] = 'Nincs ilyen lakóegység ár!';
                  }else
                    $error[] = 'Nincs ilyen lakóegység! '.$valasztottLakoegyseg[0];
                }
                if( !$error ){
                  $eloleg = 0;
                  if(
                    ( setup::$company['configuration']->events->advancePayment->rate ?? 0 ) and
                    setup::$company['configuration']->events->advancePayment->rate > 0
                  ){
                    // van előleg fizetés
                    if( round(( strtotime( $erkezes ) - strtotime( date('Y-m-d'))) / 60 / 60 / 24 ) > 
                        setup::$company['configuration']->events->cancellation->deadline ){
                      // van elegendő idő az előleg fizetésre
                      $eloleg = round($fizetendo * setup::$company['configuration']->events->advancePayment->rate / 100 );
                    }else
                      $eloleg = $fizetendo;
                  }
                  if( $foglalas_id = db::save_foglalas( [['ceg_id', setup::$company['id'], 'i'],
                                                         ['csatorna_id', 4, 'i'],
                                                         ['eloleg', $eloleg, 'i'],
                                                         ['fizetendo', $fizetendo, 'i' ],
                                                         ['vendegfo', api::$request['post']['vendegfo'], 'i' ],
                                                         ['email', api::$request['post']['email']]] )){
                    $email = api::$request['post']['email'];
                    $vendegfo = api::$request['post']['vendegfo'];
                    for( $i = 0; $i < api::$request['post']['ej']; $i++ )
                      $napokfo[] = 0;
                    foreach( $lakoegysegek as $lakoegyseg )
                      db::save_foglallakoegyseg([['foglalas_id', $foglalas_id, 'i'],
                                                    ['lakoegyseg_id', $lakoegyseg['id'], 'i'],
                                                    ['erkezes', api::$request['post']['erkezes']],
                                                    ['napokfo', json_encode($napokfo, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)]]);
                    foreach( api::$request['post'] as $key => $value ){
                      if( substr( $key, 0, 5 ) == 'szolg' ){
                        if( $szolgaltatas = db::get_szolgaltatas( substr( $key, 5 ))){
                          db::save_foglalszolgaltatas([['foglalas_id', $foglalas_id, 'i'],
                                                          ['szolgaltatas_id', substr( $key, 5 ), 'i'],
                                                          ['ar', $szolgaltatas['ar'], 's']]);
                        }
                      }
                    }
      
                    // Email a szállásadónak
                    if( $template = db::get( DATABASENAME.'.message_templates', 1 )){
                      require_once 'def_message.php';
                      $subject = messageReplaceVariables( $template['subject'], $foglalas_id );
                      $body = messageReplaceVariables( $template['body'], $foglalas_id );

                      $message_id = db::save( DATABASENAME.'.messages', [
                        ['company_id', setup::$company['id'], 'i'],
                        ['booking_id', $foglalas_id, 'i'],
                        ['device', 3, 'i'],
                        ['type', 0, 'i'],
                        ['sender', SENDER_EMAIL],
                        ['fromto', setup::$company['configuration']->supplierInfo->email],
                        ['name', $subject],
                        ['body', $body],
                        ['embeds', json_encode( ['items/tren-all-in-one.png,logo,logo.png'], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES )]
                      ] );
    
                      $ok = mailer::speedsend(
                        '(#TM'.$message_id.')2 '.$subject,
                        $body,
                        setup::$company['configuration']->supplierInfo->email,
                        SENDER_EMAIL,
                        null,
                        ['items/tren-all-in-one.png,logo,logo.png']
                      );
                      if( gettype( $ok ) == 'boolean' )
                        db::save( DATABASENAME.'.messages', [['sending_time', date( 'Y-m-d H:i:s' )]], $message_id );
                    }
                  }
                }else{
                  // TODO hibaüzenet
                }
              }
      
              $res = $res + ['foglalas' => ['erkezes' => api::$request['post']['erkezes'] ?? $erkezes,
                                            'tavozas' => api::$request['post']['tavozas'] ?? $tavozas,
                                            'het' => api::$request['post']['het'] ?? 0,
                                            'fo' => $vendegfo ?? 0,
                                            'lakoegysegnevek' => $lakoegysegnevek ?? '',
                                            'ar' => $fizetendo ?? 0,
                                            'email' => $email ?? '',
                                            'telefon' => $foglalas['telefon'] ?? '',
                                            'kapcsolattarto' => $foglalas['kapcsolattarto'] ?? '',
                                            'megjegyzes' => $foglalas['megjegyzes'] ?? '',
                                            'foglalas_id' => $foglalas_id ?? 0,
                                            'delete_cookie' => $deleteCookie ?? 0
                                          ],
                            'lakoegysegek' => $lakoegysegek];
            break;
            case 'mybookings':
              // POST SUBMIT foglalás végleges mentése
              if( api::$request['post']['btn_lefoglalom'] ?? 0 ){
                // kapcsolattartó reg-be felvétele
                if( $kapcsolattarto = setup::get_userEmail( api::$request['post']['email'] )){
                  // már létezik az adatbázisban
                  // TODO email cím jóváhagyás kell vagy jelszó ha van!
                  $kapcsolattarto_id = $kapcsolattarto['id'];
                }else{
                  // nincs és felvesszük
                  $nev = explode( ' ', api::$request['post']['kapcsolattarto'] );
                  $neve = array_shift( $nev );
                  $nevek = '';
                  if( count( $nev ) ) $nevek = implode( ' ', $nev );
                  $reg = [
                    ['email', api::$request['post']['email']],
                    //['telefon', api::$request['post']['telefon']],
                    ['name', mb_convert_case( $neve, MB_CASE_TITLE,"utf-8" )],
                    ['first_name', mb_convert_case( $nevek, MB_CASE_TITLE,"utf-8" )],
                    ['nickname', api::$request['post']['kapcsolattarto']]
                  ];
                  if( ( api::$request['post']['regisztral'] ?? 0 ) and api::$request['post']['regisztral'] ){
                    // ha kért regisztrációt jelszó generálás
                    $psw = setup::create_psw();
                    $psw_hash = password_hash( $psw, PASSWORD_BCRYPT );
                    $reg = array_merge( $reg, [['password', $psw], ['password_hash', $psw_hash]] );
                  }
                  $kapcsolattarto_id = setup::save( 'shared.users', $reg );
                }
                if( $kapcsolattarto_id ){
                  if( !$user_project = setup::get( 'shared.user_projects', 'user_id='.$kapcsolattarto_id.' AND project_id='.$_SESSION['PROID'] ))
                    $user_project_id = db::save(
                      'shared.user_projects',
                      [['user_id', $kapcsolattarto_id, 'i'], ['project_id', $_SESSION['PROID'], 'i']]
                    );
                  else $user_project_id = $user_project['id'];
                  if( !setup::get( 'shared.usages', 'company_id='.setup::$company['id'].' AND user_project_id='.$user_project_id ))
                    db::save(
                      'shared.usages',
                      [['user_project_id', $user_project_id, 'i'], ['company_id', setup::$company['id'], 'i']]
                    );
                  // Számlázási adatok
                  $invoice_info = null;
                  if( api::$request['post']['sznev'] ?? 0 ){
                    $invoice_info = json_encode( [
                      'name' => api::$request['post']['sznev'],
                      'address' => api::$request['post']['szutca'] ?? '',
                      'zip' => api::$request['post']['szirsz'] ?? '',
                      'city' => api::$request['post']['sztelepules'] ?? '',
                      'tax_number' => api::$request['post']['szadosz'] ?? ''
                    ] );
                  }
                  
                  // Foglalás véglegesítése
                  db::save_foglalas( [['reg_id', $kapcsolattarto_id ?? 0, 'i'],
                                      ['foglalva', date('Y-m-d H:i:s')],
                                      ['telefon', api::$request['post']['telefon']],
                                      ['kapcsolattarto', api::$request['post']['kapcsolattarto']],
                                      ['invoice_info', $invoice_info ],
                                      ['megjegyzes', api::$request['post']['megjegyzes']]], api::$request['post']['foglalas_id'] );
      
                  // Email küldés a foglalásról

                  mailer::speedsend( 'Foglalás ['.api::$request['post']['foglalas_id'].'] érkezett '.date('Y-m-d H:i:s'),
                                      'Megtekinthető: <a href="https://trsoft.hu/naptar/modosit/'.api::$request['post']['foglalas_id'].'">ITT</a>',
                                      'Tölgyfa vendégház <<EMAIL>>',
                                      mb_convert_case( api::$request['post']['kapcsolattarto'], MB_CASE_TITLE,"utf-8" ).
                                      '<'.api::$request['post']['email'].'>' );
                  
                  // Visszaigazoló oldal TODO kidolgozni!
                  //if( $page = db::get_page( 'Foglalás köszönő!', $ceg['id'] ) )
                  //  $res = $res + [ "ok" => true, "page" => $page ];
                  $res = $res + [ "foglalas_ok" => true ];
                }
              }
            break;
          }
        break;
        case 'booking': // Foglalás mentése az adatbázisba egy lakóegység esetén
          user_error( 'INFOBooking '. json_encode(api::$request));
          $ej = round( ( strtotime( api::$request['tavozas'] ) - strtotime( api::$request['erkezes'] ) ) / 60 / 60 / 24 );
          $szallasdij = periodAccommodationFee(
            api::$request['accommodation_unit_id'],
            strtotime( api::$request['erkezes'] ),
            strtotime( api::$request['tavozas'] ),
            api::$request['guests'] ?? 1
          );

          $kuponkod = '';
          $kedvezmeny = 0;
          if( $coupon = db::get( DATABASENAME.'.coupons', 'company_id='.setup::$company['id'].' AND code="'.api::$request['couponcode'].'"' ) ){
            if( $coupon['validity_start'] <= date( 'Y-m-d' ) and $coupon['validity_end'] >= date( 'Y-m-d' ) and
                $coupon['booking_period_start'] <= api::$request['erkezes'] and $coupon['booking_period_end'] >= api::$request['tavozas'] and
                $coupon['signs'][0] == '2' and ( $coupon['signs'][2] == '0' or $coupon['redemption'] == 0 ) ){
              $days = ( strtotime( api::$request['tavozas'] ) - strtotime( api::$request['erkezes'] ) ) / 24 / 60 / 60;
              if( $coupon['min_booking_days'] <= $days ){
                $kuponkod = $coupon['code'];
                if( $coupon['signs'][1] == '0' or $coupon['signs'][1] == '1' )
                  $kedvezmeny = ( $coupon['signs'][1] == '0' )? $coupon['discount'] : round( $szallasdij * $coupon['discount'] / 100 );
                else{
                  // napi kedvezmény
                  $discount_day = 0;
                  $step = strtotime( api::$request['erkrzes'] );
                  while( $step < strtotime( api::$request['tavozas'] ) ){
                    $weekDay = date( 'N', $step );
                    if( $coupon['weekly_start_day'] <= $coupon['weekly_stop_day'] ){
                      if( $weekDay >= $coupon['weekly_start_day'] and $weekDay <= $coupon['weekly_stop_day'] ) $discount_day++;
                    }else{
                      if( $weekDay >= $coupon['weekly_start_day'] ) $discount_day++;
                      if( $weekDay <= $coupon['weekly_stop_day'] ) $discount_day++;
                    }
                    $step += 24*60*60;
                  }
                  $kedvezmeny = $discount_day * $coupon['discount'];
                }
              }
            }
          }

          $fizetendo = $szallasdij - $kedvezmeny;

          $eloleg = 0;
          if( ( setup::$company['configuration']->events->advancePayment->rate ?? 0 ) and setup::$company['configuration']->events->advancePayment->rate )
            if( round( ( strtotime( api::$request['erkezes'] ) - strtotime( date( 'Y-m-d' ) ) ) / 60 / 60 / 24 ) > setup::$company['configuration']->events->cancellation->deadline )
              $eloleg = round( $fizetendo * setup::$company['configuration']->events->advancePayment->rate / 100 );
            else
              $eloleg = $fizetendo;

          $invoice_info = null;
          if( api::$request['inv_last_name'] ?? 0 ){
            $invoice_info = json_encode( [
              'name' => api::$request['inv_last_name'].' '.api::$request['inv_first_name'],
              'address' => api::$request['inv_address'] ?? '',
              'zip' => api::$request['inv_zipcode'] ?? '',
              'city' => api::$request['inv_city'] ?? '',
              'tax_number' => api::$request['inv_tax_number'] ?? ''
            ] );
          }
          if( $foglalas_id = db::save_foglalas(
            [
              ['ceg_id', setup::$company['id'], 'i'],
              ['csatorna_id', 4, 'i'],
              ['eloleg', $eloleg, 'i'],
              ['fizetendo', $fizetendo, 'i' ],
              ['szallasdij', $szallasdij, 'i' ],
              ['kuponkod', $kuponkod ],
              ['kedvezmeny', $kedvezmeny, 'i' ],
              ['vendegfo', api::$request['guests'], 'i' ],
              ['email', api::$request['customer_email'] ?? ''],
              ['telefon', api::$request['customer_phone'] ?? ''],
              ['kapcsolattarto', ( api::$request['last_name'] ?? '' ).' '. ( api::$request['first_name'] ?? '' )],
              ['megjegyzes', api::$request['comment'] ?? ''],
              ['invoice_info', $invoice_info ]
            ]
          ) ){
            for( $i = 0; $i < $ej; $i++ )
              $napokfo[] = 0;
            db::save_foglallakoegyseg(
              [
                ['foglalas_id', $foglalas_id, 'i'],
                ['lakoegyseg_id', api::$request['accommodation_unit_id'], 'i'],
                ['erkezes', api::$request['erkezes']],
                ['napokfo', json_encode($napokfo, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)]
              ]
            );

            $service_value = 0;
            foreach( api::$request as $key => $value )
              if( substr( $key, 0, 3 ) == 'ser' and $value != '' and $value != '0' )
                if( $service = db::get( DATABASENAME.'.services', intval( substr( $key, 3 )))){
                  $mennyi = $ar = 0;
                  if( $service['ar'] ){
                    $ar = $value * $service['ar'];
                    $mennyi = $value;
                  }else
                    $ar = $value;
                  db::save_foglalszolgaltatas( [
                    ['foglalas_id', $foglalas_id, 'i'],
                    ['szolgaltatas_id', substr( $key, 3 ), 'i'],
                    ['ar', $ar, 'i'],
                    ['mennyi', $mennyi, 'i']
                  ] );
                  $service_value += $ar;
                }
            if( $service_value ){
              $fizetendo += $service_value;
              $eloleg = 0;
              if( ( setup::$company['configuration']->events->advancePayment->rate ?? 0 ) and setup::$company['configuration']->events->advancePayment->rate )
                if( round( ( strtotime( api::$request['erkezes'] ) - strtotime( date( 'Y-m-d' ) ) ) / 60 / 60 / 24 ) > setup::$company['configuration']->events->cancellation->deadline )
                  $eloleg = round( $fizetendo * setup::$company['configuration']->events->advancePayment->rate / 100 );
                else
                  $eloleg = $fizetendo;
              db::save_foglalas(
                [
                  ['eloleg', $eloleg, 'i'],
                  ['fizetendo', $fizetendo, 'i' ]             
                ], $foglalas_id
              );
            }
            // Email a szállásadónak
            if( $template = db::get( DATABASENAME.'.message_templates', 1 )){
              require_once 'def_message.php';
              $subject = messageReplaceVariables( $template['subject'], $foglalas_id );
              $body = messageReplaceVariables( $template['body'], $foglalas_id );
              
              $message_id = db::save( DATABASENAME.'.messages', [
                ['company_id', setup::$company['id'], 'i'],
                ['booking_id', $foglalas_id, 'i'],
                ['device', 3, 'i'],
                ['type', 0, 'i'],
                ['sender', SENDER_EMAIL],
                ['fromto', setup::$company['configuration']->supplierInfo->email],
                ['name', $subject],
                ['body', $body],
                ['embeds', json_encode( ['items/tren-all-in-one.png,logo,logo.png'], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES )]
              ] );

              $res = mailer::speedsend(
                '(#TM'.$message_id.')1 '.$subject,
                $body,
                setup::$company['configuration']->supplierInfo->email,
                SENDER_EMAIL,
                null,
                ['items/tren-all-in-one.png,logo,logo.png']
              );
              if( gettype( $res ) == 'boolean' )
                db::save( DATABASENAME.'.messages', [['sending_time', date( 'Y-m-d H:i:s' )]], $message_id );
            }else user_error( 'Nincs message_template!' );
          }
        break;
        case 'subscriber': // Felirarkozó mentése az adatbázisba
          $user_id = 0;
          if( $user = db::get( 'shared.users', 'email="'.api::$request['email'].'"' ) ){
            $user_id = $user['id'];
          }else{
            $user_id = db::save(
              'shared.users',
              [
                ['email', api::$request['email']],
                ['name', api::$request['name']],
                ['first_name', api::$request['first_name']]
              ]
            );
            $user_project_id = db::save(
              'shared.user_projects',
              [
                ['user_id', $user_id, 'i'],
                ['project_id', 37, 'i']
              ]
            );
            db::save(
              'shared.usages',
              [
                ['user_project_id', $user_project_id, 'i'],
                ['company_id', setup::$company['id'], 'i']
              ]
            );
          }
          if( $user_id ){
            db::save(
              DATABASENAME.'.subscribers',
              [
                ['company_id', setup::$company['id'], 'i'],
                ['user_id', $user_id, 'i'],
                ['consent_start', date( 'Y-m-d' )]
              ]
            );
          }
        break;
      }
    break;
  }
  response::add( 'api', $res );
}else{
  array_shift( http::$route );
  array_shift( http::$route );
  array_shift( http::$route );
  switch( http::$route[0] ?? 0 ){
    case 'documentation':
      switch( http::$route[1] ?? 0 ){
        case 'booking':
          $res = ['booking' => [
            'GET' => [
              'brief' => 'Foglalások lekérése',
              'description' => 'Foglalás egy lakóegység esetén',
              'parameters' => 'Nincs'
            ],
            'POST' => [
              'brief' => 'Foglalás',
              'description' => 'Foglalás egy lakóegység esetén',
              'parameters' => [
                'accommodation_unit_id' => 'integer 1- lakóegység azonosító',
                'guests' => 'integer 1- vendégek száma',
                'start' => 'date érkezés dátuma pl. 2025-05-31',
                'stop' => 'date távozás dátuma'
              ]
            ]
          ]];
        break;
        case 'accommodationfee':
          $res = ['accommodationfee' => [
            'GET' => [
              'brief' => 'Szállásdíj számítás',
              'description' => '
                Szállásdíj számítás egy lakóegység esetén. A paraméterek küldése tokenizálható (base64|hmac|jwt).
                Tokenizálás esetén az url kiegészül a tokennel (/{token}).
              ',
              'parameters' => [
                'accommodation_unit_id' => 'integer 1- lakóegység azonosító',
                'guests' => 'integer 1- vendégek száma',
                'start' => 'date érkezés dátuma pl. 2025-05-31',
                'stop' => 'date távozás dátuma'
              ],
              'response' => [
                'guests' => 'integer vendégek száma',
                'accommodationfee' => 'integer szállásdíj'
              ],
              'example' => '
                /accommodationfee?accommodation_unit_id=1&guests=2&start=2025-05-31&stop=2025-06-01
                /accommodationfee/jwteyJhY2NvbW1vbnVtZW50X3VuaXRfaWQiOiIxIiwiZ3Vlc3RzIjoxLCJzdGFydCI6IjIwMjUtMDUtMzEiLCJzdG9wIjoiMjAyNS0wNS0zMiJ9
              '
            ]
          ]];
        break;
        default:
          $res = ['methods' => [
            '/documentation : /GET/ Dokumentáció',
            '/documentation/{action} : /GET/ Dokumentáció adott műveletről',
            '/galleries : /GET/ Képgalériák lekérése',
            '/galleries/{id} : /GET/ Képgaléria lekérése',
            '/booking : /GET/ Foglalások lekérése',
            '/accommodationfee : /POST/ Szállásdíj számítás',
            '/coupon : /POST/ Kupon ellenőrzés',
            '/extraselect : /POST/ Extra szolgáltatások lekérése',
            '/booking : /POST/ Foglalás',
            '/subscriber : /POST/ Feliratkozás',
            '/advertall : /POST/ Reklám megjelenítése'
          ]];
      }
    break;
    default:
      $res = api::$error;
  }
}
user_error('INFORES: '.json_encode($res));
api::response( $res );

function calcGuestPrice( $guests, $guestPrices, &$accommodationfee ){
  foreach( $guestPrices as $guestPrice ){
    if( $guests ){ 
      $guest = explode( '-', $guestPrice[0] );
      switch( count( $guest )){
        case 1: if( $guests == $guest[0] ) $accommodationfee += $guestPrice['1']; break;
        default: if( $guests >= $guest[0] and $guests <= $guest[1] ) $accommodationfee += $guestPrice['1']; break;
      }
    }else{
      $accommodationfee[$guestPrice[0]] = ( $accommodationfee[$guestPrice[0]] ?? 0 )? $accommodationfee[$guestPrice[0]] + $guestPrice['1'] : $guestPrice['1'];
    }
  }
}

function periodAccommodationFee( $accommodation_unit_id, $arrival, $departure, $guests = 0 ){
  $accommodationfee = ( $guests )? 0 : [];
  $days = ( $departure - $arrival ) / 24 / 60 / 60;
  $day = $arrival;
  $is_minDays = 0;
  while( $day < $departure ){
    $calc = 1;
    if( !$is_minDays and $prices = db::list_accommodationFee( $accommodation_unit_id, date( 'N', $day )))
      while( $price = array_shift( $prices )){
        $periods = json_decode( $price['periods'], true );
        foreach( $periods as $period ){
          $guestPrices = json_decode( $price['prices'], true );
          if( $calc )
            switch( strlen( $period )){
              case 5:
                if( date( 'm-d', $day ) == substr( $period, 0, 5 )){
                  if( $price['min_days'] <= $days )
                    calcGuestPrice( $guests, $guestPrices, $accommodationfee );
                  else $is_minDays = 1;
                  $calc = 0;
                }
              break;
              case 11:
                if( date( 'm-d', $day ) >= substr( $period, 0, 5 ) and date( 'm-d', $day) <= substr( $period, 6 )){
                  if( $price['min_days'] <= $days )
                    calcGuestPrice( $guests, $guestPrices, $accommodationfee );
                  else $is_minDays = 1;
                  $calc = 0;
                }
              break;
            }
        }
      }
    $day += 24*60*60;
  }
  return ( $is_minDays )? 0 : $accommodationfee;
}
// régi - dunakanyar - deprecated
function calc_accommodationfee( $accommodation_unit_id, $guests, $start, $end ){
  $accommodationfee = 0;
  $days = ( $end - $start ) / 24 / 60 / 60;
  $step = $start;
  while( $step < $end ){
    if( $fee = db::get_accommodation_fee( $accommodation_unit_id, $guests, date( 'Y-m-d', $step ), $days ) )
      $accommodationfee += $fee['ar'];
    $step += 24*60*60;
  }
  return $accommodationfee;
}

function curentPrices( $accommodation_unit_id ){
  $arki = '';
  if( $arak = db::list_curentPrices( $accommodation_unit_id )){
    foreach( $arak as $ar ){
      $ar['prices'] = json_decode( $ar['prices'], true );
      foreach( $ar['prices'] as $price ){
        $arki.= 
          (( $arki != '' )? '<br>' : '' ).
          $ar['period_name'] . '<br>'.
          number_format( $price['1'], 0, '.', ' ' ) . '&nbsp;Ft/éj'.
          '/' . $price['0'].'fő';
        if( $ar['weekly_arrival_days'] == '1111111' )
          $arki.= '<br>Érkezés bármelyik nap!';
        else{
          $arki.= '<br>Lehetséges érkezés:';
          for( $i=0; $i<7; $i++ )
            $arki.= ( $ar['weekly_arrival_days'][$i] )? ' '.['Hétfő','Kedd','Szerda','Csütörtök','Péntek','Szombat','Vasárnap'][$i] : '';
        }
        if( $ar['min_days'] > 1 )
          $arki.= '<br>Minimum éjszakák száma: '.$ar['min_days']. 'éj';
        $arki.= '<br>';
      }
    }
  }
  return ( $arki != '' )? $arki : 0;
}

function bookableAccommodationUnits( $arrival, $departure ){
  $occupiedAccommodationUnits = db::list_occupiedAccommodationUnits( $arrival, $departure, setup::$company['id'] );
  if( $freeAccommodationUnits = db::list_accommodationUnits(
    'MID(au.signs,1,1) = 2'.(( $occupiedAccommodationUnits )? ' AND au.id NOT IN('.implode( ',', $occupiedAccommodationUnits ).')' : '' ),
    setup::$company['id']
  )){
    foreach( $freeAccommodationUnits as $key => $accommodationUnit ){
      $arrr = periodAccommodationFee( $accommodationUnit['id'], strtotime( $arrival ), strtotime( $departure ));
      if( $arrr ) $freeAccommodationUnits[$key]['opportunities'] = $arrr;
      //else $freeAccommodationUnits[$key]['opportunities'] = 'Csak több éjszakára foglalható!';
    }
  }
  return $freeAccommodationUnits;
}

function idoszakszabadszobak( $erkezes, $tavozas, $het = NULL ){
  $foglaltlakoegysegek = db::list_occupiedAccommodationUnits( $erkezes, $tavozas, setup::$company['id'] );
  $maxf = 0;

  if( $het == 2 ){ // teljes ház
    
  }else
    if( $szobak = db::list_accommodationUnits(
      'MID(au.signs,1,1) = 2'.(( $foglaltlakoegysegek )? ' AND au.id NOT IN('.implode( ',', $foglaltlakoegysegek ).')' : '' ), setup::$company['id']
    )){
      foreach( $szobak as $key => $szoba ){
        $arrr = periodAccommodationFee( $szoba['id'], strtotime( $erkezes ), strtotime( $tavozas ));
        if( $arrr ) $szobak[$key]['aktualisar'] = $arrr;
        else unset( $szobak[$key] );
      }
    }
  return ['haz'=> ( $foglaltlakoegysegek )? 0 : 1, 'maxferohely' => $maxf, 'szobak'=> $szobak];
}