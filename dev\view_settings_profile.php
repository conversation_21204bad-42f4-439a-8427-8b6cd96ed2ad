  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <?php if( !( http::$route[2] ?? 0 ) ){ ?>
      <header>
        <h5>Profiladatok</h5>
      </header>
      <article>
        <a href="/settings/profile/ec">Email csere</a> | <a href="/settings/profile/pc">Jelszó csere</a>
        <form name="form_settings" method="post" action="<?= http::$path ?>/settings/profile">
          <ul class="formbox">
            <li class="form col0">
              <input type="email" value="<?= $_POST['email'] ?? '' ?>" placeholder="" disabled>
              <label>Email</label>
            </li>
            <li class="form col0">
              <input type="text" name="nickname" value="<?= $_POST['nickname'] ?? '' ?>" placeholder="" autofocus autocomplete="off">
              <label>Felhasználónév</label>
            </li>
            <li class="form col0">
              <input type="text" name="name" value="<?= $_POST['name'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Vállalkozás neve vagy vezetéknév</label>
            </li>
            <li class="form col0">
              <input type="text" name="first_name" value="<?= $_POST['first_name'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Keresztnév</label>
            </li>
            <li><button name="btn_settings" value="1">Mentés</button></li>
          </ul>
        </form>            
      </article>
      <header>
        <h5>Számlázási adatok</h5>
      </header>
      <article>
        <form name="form_settings_billing" method="post" action="<?= http::$path ?>/settings/profile">
          <ul class="formbox">
            <li class="form col0">
              <input type="text" name="supplierName" value="<?= $_POST['supplierName'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Számlázási név</label>
            </li>
            <li class="form col0">
              <input type="text" name="taxNumber" value="<?= $_POST['taxNumber'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Magyar adószám (NAV)*</label>
            </li>
            <li class="form col0">
              <input type="text" name="communityVatNumber" value="<?= $_POST['communityVatNumber'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Közösségi adószám (EU)</label>
            </li>
            <li class="form col0" style="--toicon:var(--icon-angle-double-down)">
              <select name="countryCode" placeholder="" disabled>
                <?php foreach( response::$vw->view->countries as $country ){ ?>
                <option<?= ( $_POST['countryCode'] == $country->code2 )?' selected':'' ?>><?= $country->name ?></option>
                <?php } ?>
              </select>
              <label>Ország</label>
            </li>
            <li class="form col3">
              <input type="text" name="postalCode" value="<?= $_POST['postalCode'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Irányítószám</label>
            </li>
            <li class="form col7">
              <input type="text" name="city" value="<?= $_POST['city'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Település</label>
            </li>
            <li class="form col5">
              <input type="text" name="streetName" value="<?= $_POST['streetName'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Közterület neve</label>
            </li>
            <li class="form col25">
              <input type="text" name="publicPlaceCategory" value="<?= $_POST['publicPlaceCategory'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Jellege</label>
            </li>
            <li class="form col25">
              <input type="text" name="number" value="<?= $_POST['number'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Házszám</label>
            </li>
            <li class="form col25">
              <input type="text" name="building" value="<?= $_POST['building'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Épület</label>
            </li>
            <li class="form col25">
              <input type="text" name="staircase" value="<?= $_POST['staircase'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Lépcsőház</label>
            </li>
            <li class="form col25">
              <input type="text" name="floor" value="<?= $_POST['floor'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Emelet</label>
            </li>
            <li class="form col25">
              <input type="text" name="door" value="<?= $_POST['door'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Ajtó</label>
            </li>
            <li class="form col0">
              <input type="text" name="bankAccountNumberHUF" value="<?= $_POST['bankAccountNumberHUF'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Bankszámlaszám (HUF)</label>
            </li>
            <li class="form col0">
              <input type="text" name="bankAccountNumberEUR" value="<?= $_POST['bankAccountNumberEUR'] ?? '' ?>" placeholder="" autocomplete="off">
              <label>Bankszámlaszám (EUR)</label>
            </li>
            <li><button name="btn_settings_billing" value="1">Mentés</button></li>
          </ul> 
        </form>
      </article>
      <?php } ?>  
      <?php if( ( http::$route[2] ?? 0 ) and http::$route[2] == 'pc' ){ ?>
      <header>
        <h5>Jelszó csere</h5>
      </header>
      <article>
        <form name="form_settings_pwdchange" method="post" action="/settings/profile">
          <ul class="formbox">
            <li class="form col0">
              <input type="password" name="psw" placeholder="" required>
              <label>Új jelszó</label>
            </li>
            <li class="form col0">
              <input type="password" name="psw_again" placeholder="" required>
              <label>Új jelszó újra</label>
            </li>
            <li><button name="btn_settings_pswchange" value="1">Csere</button></li>
          </ul>
        </form>
      </article>
      <?php } ?>
      <?php if( ( http::$route[2] ?? 0 ) and http::$route[2] == 'ec' ){ ?>
      <header>
        <h5>Email csere</h5>
      </header>
      <article>
        <form name="form_settings_emailchange" method="post" action="/settings/profile">
          <ul class="formbox">
            <li class="form col0">
              <input type="text" name="email" placeholder="" required>
              <label>Új email</label>
            </li>
            <li><button name="btn_settings_emailchange" value="1">Csere</button></li>
          </ul>
        </form>
      </article>
      <?php } ?>

      <?php /* if(isset($_POST['regtax_id'])){ ?>
              <input type="hidden" name="regtax_id" value="<?php echo $_POST['regtax_id']?>">
              <?php } ?>
              <ul class="padlib gridw9 fmve">
                <li class="gridw5"><span>Cégj vagy EV szám</span><input type="text" name="cvszamhu" value="<?php echo $_POST['cvszamhu'] ?? ''?>" placeholder=""></li>
                <li class="gridw9"><span>Bankszámlaszám</span><input type="text" name="bankhu" value="<?php echo $_POST['bankhu'] ?? ''?>"></li>
                <li class="mart"><input class="btn" type="submit" name="btn_settings_billing" value="Mentés" title=""></li>
              </ul>
            </form>
          </div>
        </div>
      </div>
      */ ?>
    </div>
  </section>