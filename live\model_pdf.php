<?php

class pdf{

  public static function invoiceSupplierAndCustomer( $invoice ){
    return
      '<table width="100%" style="vertical-align: top; font-family: serif; font-size: 9pt">'.
      '  <tr>'.
      '    <td width="50%">'.
      '      ELADÓ<br><br>'.
      '      <b>'.$invoice['supplier']['name'].'</b><br>'.
      $invoice['supplier']['countryCode'].' '.$invoice['supplier']['postalCode'].' '.$invoice['supplier']['city'].'<br>'.
      $invoice['supplier']['streetName'].' '.$invoice['supplier']['publicPlaceCategory'].' '.
      $invoice['supplier']['number'].'<br>'.
      '      <b>Magyar adószám</b>: '.$invoice['supplier']['taxNumber'].
      ( ( ( $invoice['supplier']['communityVatNumber'] ?? 0 ) and $invoice['supplier']['communityVatNumber'] != '' )
        ? '      <br><b>EU adószám</b>: '.$invoice['supplier']['communityVatNumber']
        : '' ).
      ( ( $invoice['payment_method'] == 3 )
        ? '      <br><b>Bankszámlaszám</b>: '.$invoice['supplier']['bankAccountNumberHUF']
        : '' ).
      '    </td>'.
      '    <td width="50%" align="right">'.
      '      VEVŐ<br><br>'.
      '      <b>'.$invoice['customer']['name'] .'</b><br>'.
      $invoice['customer']['countryCode'].' '.$invoice['customer']['postalCode'].' '.$invoice['customer']['city']. '<br>'.
      $invoice['customer']['additionalAddressDetail'].
      ( ( ( $invoice['customer']['taxNumber'] ?? 0 ) and $invoice['customer']['taxNumber'] != '' )
        ? '      <br><b>Magyar adószám</b>: '.$invoice['customer']['taxNumber']
        : '' ).
      ( ( ( $invoice['customer']['communityVatNumber'] ?? 0 ) and $invoice['customer']['communityVatNumber'] != '' )
        ? '      <br><b>EU adószám</b>: '.$invoice['customer']['communityVatNumber']
        : '' ).
      '    </td>'.
      '  </tr>'.
      '  <tr><td colspan="2" style="border-top: 1px solid #000">&nbsp;</td></tr>'.
      '</table>';
  }

  public static function invoiceHeader( $invoice ){
    return
      '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt">'.
      '  <tr>'.
      '    <td width="50%"><img style="vertical-align: top" src="items/tren-all-in-one.png" width="80"></td>'.
      '    <td width="50%" align="right">'.
      '      <b style="font-size: 14pt">'.['','','ELŐLEGSZÁMLA','SZÁMLA'][$invoice['signs'][1]].'</b>'.
      '      <br><b>Számla sorszám</b>: '.$invoice['invoice_number'].
      '    </td>'.
      '  </tr>'.
      '  <tr><td colspan="2" style="border-top: 1px solid #000">&nbsp;</td></tr>'.
      '</table>';
  }

  public static function invoiceFooter(){
    return
      '<table width="100%" style="vertical-align: bottom; font-family: serif; font-size: 9pt">'.
      '  <tr>' .
      '    <td width="50%">Készült: TREN All-In-One szálláshelykezelő rendszer | tren.hu</td>'.
      '    <td width="50%" align="right">Oldalszám: {PAGENO}/{nbpg}</td>'.
      '  </tr>' .
      '</table>';
  }

}
