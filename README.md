# Spritzer
> <PERSON><PERSON><PERSON> Javascript keretrendszer
## Elj<PERSON>rások
- `$()`                    Szelektor lekérés egyszerűsítése. Egy elemmel tér vissza a paramétereknek megfelelően
- `$$()`                   Szelektor lekérés egyszerűsítése. Elemek tömbjével tér vissza a paramétereknek megfelelően
- `getStyle()`             Egy HTML elem adott stylus adatának lekérdezése
- `getCharCode()`          Billentyű esemény karakterkódja
- `preventDoubleSubmits()` submit dupla kattintás megakadályozása minden ürlapon
- `preventDoubleSubmit()`  submit dupla kattintás megakadályozása egy adott ürlapon
- `fullScreen()`           Egy HTML megjelenítési rész nézetének váltása teljes képernyőre
- `getDayOfYear()`         A megadott dátum hányadik napja az évnek
- `getNextDate()`          A megadot dátumhoz hozzáadja a megadot napot és visszaadja az új dátumot
- `getDiffDay()`           Két negadott dátum között vissza adja a napok számát
- `getFormatDate()`        A megadot dátumot formázza a megadott minta szerint

- `ajax()`                 Adatcsere megoldás
- `apiJSON()`              Kliens api hívás
- `loadHTML2render()`      Közvetlen HTML letöltés szerverről és megjelenítés
- `apiHTML2render()`       Közvetlen HTML betöltés api hívással szerverről és megjelenítés

- `dialog()`               Dialog box - Modális párbeszédpanel
- `dialogRebuild()`        Dialog box frissítése
- `closeDialog()`          Adott dialog box bezárása

- `datatable()`            Datatable - Table kezelő
- `insertRow()`            Táblázatba új sor beszurása
- `renderTbody()`          Táblázat tartalmának frissítése

- `loader()`               Betöltés, folyamat jelző
- `datePicker()`           Egy időpont vagy időszak vagy több időpont kijelölése
- `gallery()`              Képgaléria lapozó és kezelő
- `library()`              Elemek fogd és dobd rendezője
- `upload()`               Fogd és dobd teljes fájlfeltöltő
- `toggleButton()`         Ürlap `.toggle-buttons` elem eseménykezelője

- `validation()`           Ürlap elemek validálása

## Verzió
0.9.0 - 2023-12-30

## Alap eljárások

### Szelektor lekérés egyszerűsítése
#### $( selector, element = document )
##### Paraméterek
- **selector** *string* CSS szelektor kifejezés
- **element** *element* Csak adott elemen belül keres
##### Visszatérés
`element || null` Egy elem, vagy null ha nincs találat. A több találat van, akkor az első
##### Példák
    $( '.row' )
    $( 'select', form )

### Szelektor lekérés egyszerűsítése
#### $$( selector, element = document )
##### Paraméterek
- **selector** *string* CSS szelektor kifejezés
- **element** *element* Csak adott elemen belül keres
##### Visszatérés
`array || null` Elemek tömbje, vagy null ha nincs találat
##### Példák
    $$( '.row' )
    $$( 'select,input', form )

### Egy HTML elem adott stylus adatának lekérdezése
#### getStyle( element, styleProp )
##### Paraméterek
- **element** *element* adott elem
- **styleProp** *string* stylus
##### Visszatérés
`string || null` Egy érték string, vagy null
##### Példák
    getStyle( div, 'color' )

### Billentyű esemény karakterkód meghatározása
#### getCharCode( event )
##### Paraméterek
- **event** *object* billentyű esemény
##### Visszatérés
`integer` Karakter kódja vagy nulla

### submit dupla kattintás megakadályozása
#### preventDoubleSubmits( unlock = null )
##### Paraméterek
- **unlock** *string* `all || formName || null`. Ha `all`, akkor az összes form feloldásra kerül, ha `formName` azaz a form neve meg van adva, akkor az adott form feloldásra kerül, ha `null` akkor beállításra kerül az esemény
##### Visszatérés
Nincs
##### Leírás
Az összes `form` elemekre alkalmazza a `submit` eseményt. Esemény esetén jelölőt helyez el az ujboli küldés megakadályozására. Paraméter megadása esetén a jelölő eltávolításra kerül az esemény további működése mellett.

### submit dupla kattintás megakadályozása egy adott formon
#### preventDoubleSubmit( form, unlock = null )
##### Paraméterek
- **form** *element* adott form elem
- **unlock** *boolean* `true || null`. Ha `true`, akkor a form feloldásra kerül, ha `null` akkor a form ellenőrzésre és beállításra kerül
##### Visszatérés
Nincs
##### Leírás
Adott `form` elemen a `submit` esemény jelölőt helyez el az ujboli küldés megakadályozására. Paraméter megadása esetén a jelölő eltávolításra kerül az esemény további működése mellett.

### Egy HTML megjelenítési rész nézetének váltása teljes képernyőre
#### fullScreen( element = null )
##### Paraméterek
- **element** *element* Elem amin a fullscreen esemény van deffiniálva
##### Visszatérés
Nincs

## Adatcsere

### Ajax hívás
#### ajax( param = null )
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **url** *string* A hívandó URL címe
- **method** *string* GET || POST alapértelmezett: POST
- **async** *boolean* true || false alapértelmezett: true
- **user** *string*
- **password** *string*
- **reqHeader** *string*
- **body** *string*
- **done()** *function* Sikeres váLasz után kerül végrehajtásra a függvény. Kapott változó a visszatérési érték
- **fail()** *function* Sikertelen váLasz után kerül végrehajtásra a függvény. Kapott object érték: { code, message }
##### Visszatérés
Nincs
##### Példák
    ajax( {url: 'api/get_product/8', done: ( back ) => {console.log( back )}} )

### Kliens api hívás
#### apiJSON( param = null )
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **url** *string* Api végpont URL címe https://domain.tld/api/controller/version/endpoints...
- **method** *string* GET || POST || PUT || PATCH || DELETE alapértelmezett: GET
- **authorization** *object* JSON formátumban a paraméterek
  - **type** *string* Basic || Bearer || Digest
  - **credentials** *string* API key || Token || Egyéb
- **reqHeader** *string*
- **body** *string*
##### Visszatérés
JSON objektum
- **status**
- **messages**
- **data**
##### Példák
    apiJSON( {url: 'api/v1/products/8'} )
    apiJSON( {url: 'api/v1/init', method: 'POST', body: {user: 'user1234', pswd: '1234abcd', type: 'Bearer'}} )

### Közvetlen HTML letöltés szerverről és megjelenítés
#### loadHTML2render( param = null )
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **loadFile** *string* A letöltendő fájl URL címe
- **post** *string* POST adatok szűrők és feltételek a letöltéshez
- **html** *string* || *element* HTML elem selectora, vagy az elem ahová a fájl beillesztésre kerül
- **afterBuilding** *function* HTML fájl beillesztése után kerül végrehajtásra a függvény.
##### Visszatérés
`boolean` Igaz vagy hamis a sikeresség szerint
##### Példák
    loadHTML2render( {loadFile: 'view_table.php', html: '#table-contener'} )

### Közvetlen HTML api-val szerverről és megjelenítés
#### apiHTML2render( param = null )
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **url** *string* Api végpont URL címe
- **post** *string* POST adatok szűrők és feltételek a letöltéshez
- **html** *object* {'HTML elem neve' : 'HTML elem ahová a fájl beillesztésre kerül'} több is
- **afterBuilding** *function* HTML tartalmak beillesztése után kerül végrehajtásra a függvény.
##### Visszatérés
`boolean` Igaz vagy hamis a sikeresség szerint
##### Példák
    apiHTML2render( {url: 'api/v1/view_home/online', html: {'time': '#time-contener', 'online': '#online-contener'}} )

## Dialóg

### Modal dialog box
### Dialog box (UNDO 48px 14px min.width 288px)
### dialog( param = null )
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **type** *string* tipus megadás további almegadással ami `:` van elválasztva.
Alap tipusok: `tooltip` || `status` || `snackbar` || `toast` || `modal` || `dialog`
`status` altipusai `success` || `info` || `warning` || `danger`
- **id** *string* Dialog egyedi azonosító (neve), Nem kötelező, de hivatkozásnál hasznos, ha több van egy oldalon
- **width** *int* Ha meg van adva, akkor a dialog szélessége ennyi lesz (CSS hosszérték)
- **timeDelay** *int* Ha meg van adva, akkor adott idő (ms) után a panel bezár
- **isESCkey** *boolean* Ha `true`, akkor az ESC billenytűn való kaattintás bezárja az ablakot
- **isCloseBback** *boolean* Ha `true`, akkor backdrop háttéren kaattintás bezárja az ablakot
- **isKeepOpen** *boolean* Ha `true`, akkor (callback) jóváhagyó gomb bezárja az ablakot
- **isBtnClose** *boolean* Ha `true`, akkor van a láblécben bezárás gomb
- **isBtnCallback** *boolean* Ha `true`, akkor van a láblécben jóváhagyó gomb
- **btnCallback** *string* Gomb felirata. Ha nincs megadva akkor "Tovább"
- **btnClose** *string* Gomb felirata. Ha nincs megadva akkor "Bezár"
- **iconClose** *string* Ha meg van adva, akkor van a fejlécben bezárás ikon. Ikon megadandó pl. "var(--fa-close)"
- **iconFullscreen** *string* Ha meg van adva, akkor a panel kinyitható teljes képernyőre és visszazárható. Két ikon megadandó "|"-vel elválasztva pl. "var(--fa-expand)|var(--fa-compress)"
- **logo** *string* Panel tetejére icon logó
- **title** *string* Panel címsor szövege. Ha nincs head sincs
- **content** *string* Panel tartalom szövege
- **loadFile** *string* Ha meg van adva, akkor a panel teljes tartalma fájlból van betöltve. Az alábbi paraméterek használhatók: **width, isESCkey, isCloseBback, isKeepOpen**
- **events** *array* Tömb ami soronként egy esemény tipust nevez meg amit a dialóghoz kell adni `click` kivételével
- **eventManagers** *array* Tömb ami soronként egy eseményhez tartozó függvényt ad meg.
`{'type': {'name': [selector, function, id]}}` Sorok adatai:
   - type esemény típusa click
   - esemény neve callback | close | afterBuilding
   - class elem a kiválasztáshoz
   - függvény neve
   - id vagy paraméter tömb
`{'click': {'callback': ['.callback', 'lakoegyseglist_edit_callback', id],
               'close': ['.close','lakoegyseglist_close']}}`
##### Visszatérés
Nincs
##### Leírás
| type | logo | title | iconClose | iconFullscreen | content | isBtnClose | isBtnCallback | timeDelay | loadFile |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| `tooltip` | - | - | o | - | x | - | - | o | - |
| `status` | - | o* | o | - | x | - | - | o | - |
| `toast` | - | o | o | - | x | - | - | o | - |
| `snackbar` | o | x | o | - | x | o | o | o | - |
| `dialog` | o | x | x | o | x | x | o | - | o |
| `modal` | o | x | x | o | x | x | o | - | o |
##### Példák
    dialog( {type: 'status:succes', content: 'Sikeres művelet', timeDelay: 3000} )

### Dialog boxok alapértelmezett értékei
### dialogDefaultSetting( param = null )
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **type** *string* tipus megadás további almegadással ami `:` van elválasztva.
Alap tipusok: `status` || `snackbar` || `toast` || `modal` || `dialog`
`status` altipusai `success` || `info` || `warning` || `danger`
- **width** *int* Ha meg van adva, akkor a dialog szélessége ennyi lesz (CSS hosszérték)
- **timeDelay** *int* Ha meg van adva, akkor adott idő (ms) után a panel bezár
- **isCloseBback** *boolean* Ha `true`, akkor backdrop háttéren kaattintás bezárja az ablakot
- **isKeepOpen** *boolean* Ha `true`, akkor (callback) jóváhagyó gomb bezárja az ablakot
- **isBtnClose** *boolean* Ha `true`, akkor van a láblécben bezárás gomb
- **isBtnCallback** *boolean* Ha `true`, akkor van a láblécben jóváhagyó gomb
- **btnCallback** *string* Gomb felirata. Ha nincs megadva akkor "Tovább"
- **btnClose** *string* Gomb felirata. Ha nincs megadva akkor "Bezár"
- **iconClose** *string* Ha meg van adva, akkor van a fejlécben bezárás ikon. Ikon megadandó pl. "var(--fa-close)"
- **iconFullscreen** *string* Ha meg van adva, akkor a panel kinyitható teljes képernyőre és visszazárható. Két ikon megadandó "|"-vel elválasztva pl. "var(--fa-expand)|var(--fa-compress)"
- **logo** *string* Panel tetejére icon logó
- **title** *string* Panel címsor szövege. Ha nincs head sincs
- **content** *string* Panel tartalom szövege
##### Leírás
Ha használva van a funkció, akkor előre megadhatók alapértelmezett paraméterek és a dialog használatakor nem kell megadni, de felülbirálható

## Datatable

### Table kezelő
Minden `[data-datatable]` attributummal rendelkező elemet kikeres, majd a megfelelő paraméterekkel inicializálja a datatable-t
#### datatable( param = null )
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **events** *array* Tömb ami soronként egy esemény tipust nevez meg amit a datatable-hoz kell adni `click` és `change` kivételével
- **eventManagers** *array* Tömb ami soronként egy eseményhez tartozó függvényt ad meg.
`{'type': {'name': [selector, function, id]}}` Sorok adatai:
   - type esemény típusa click
   - esemény neve callback | close | afterBuilding
   - class elem a kiválasztáshoz
   - függvény neve
   - id vagy paraméter tömb
`{'click': {'callback': ['.callback', 'lakoegyseglist_edit_callback', id],
               'close': ['.close','lakoegyseglist_close']}}`

## UI elemek

### Betöltés jelző
#### loader( icon = null, speed = '1.5s' )

### Egy időpont vagy időszak vagy több időpont kijelölése
Minden `[data-datepicker]` attributummal rendelkező elemet kikeres, majd a megfelelő paraméterekkel inicializálja a picker-t (kiválasztót)
#### datePicker()
##### Paraméterek
**param** *object* JSON formátumban a paraméterek, ha meg vannak adva
- **mode** *string* `single` || `range` || `multiple` Csak egy időpont, Egy időszak vagy több időpont választás lehetőség. Alapértelmmezett a `single`
- **type** *string* `modal` || `onlyPicker` || `inputPicker` Fókusz megkapásakor felugró, csak választó látható, input és választó látható. Alapértelmezett a `modal`
- **today** *boolean* Ha `true`, akkor a mai napnál nem választható korábbi dátum
- **minMonth** *string* A választó megjelenítésének legrégebbi hónapja. Alapértelmezett az aktuális év januárja. Ha van `today`, akkor az aktuális hónap a legrégebbi hónap
- **maxMonth** *string* A választó megjelenítésének legújabb hónapja. Alapértelmezett az aktuális év decembere
- **minPeriodDay** *integer* időszak esetén a minimális napok száma
##### Leírás
##### Példák
    <li data-datepicker = "{mode:'range', type: 'onlyPicker'}"><input type="hidden" name="first"><input type="hidden" name="last"></li>

## Validation
### Ürlap elemek validálása

input:invalid, select:invalid CSS formázás
:valid :invalid
:user-valid :user-invalid
:required :optional
:in-range :out-of-range

<form>
  <ul>
    <li>
      <p class="info hidden"><span class="error"></span></p>
      <input class="validation" type="{email|number...}" min/max/step, minlength/maxlength, pattern, required>
      <label>Email</label>
    </li>
  </ul>
</form>