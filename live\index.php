<?php
const CLASSVERSIONS = [
  'errors'      => '1.4',
  'setup'       => '2.0',
  'api'         => '2.0',
  'http'        => '2.2',
  'response'    => '1.3',
  'table'       => '1.2',
  'valid'       => '1.0',
  'model_web'   => '1.0',
  'model_basic' => '1.0',
  'mysql'       => '2.2',
  'mailer'      => '1.1'
];
const SENDER_EMAIL = 'TREN ALL-IN-ONE<<EMAIL>>';
const DATABASENAME = 'tren';
require_once 'shared/class/autoload.php';
errors::start( 37 );

session_start( [
//  'cookie_secure'   => true, // ha true, akkor a CSRF token nem működik
  'cookie_httponly' => true,
  'cookie_samesite' => 'Strict',
  'use_strict_mode' => true
] );
session_regenerate_id( true );

$_SESSION['PROID'] = 37;
setup::get_project( $_SESSION['PROID'] );
http::route( ['home'] );

if( http::$route[0] == 'api' ){
  switch( http::$route[1] ?? 0 ){
    case 'datatable':
      switch( http::$route[2] ){
        case 'v1':
          table::datatableApi( http::$route[3], $_POST );
        break;
      }
    break;
    case 'web':
      switch( http::$route[2] ){
        case 'v1':
          require_once 'api_web_v1.php';
        break;
      }
    break;
    default:
      require_once 'api.php';
  }
  exit();
}

$_SESSION['LNG'] = 'hu';
$_SESSION['MININAV'] = $_SESSION['MININAV'] ?? 0;

setup::control( '/dashboard' );

if( ( $_GET['re'] ?? 0 ) or ( $_SESSION['RE'] ?? 0 ) ){
  $_SESSION['RE'] = $_GET['re'] ?? $_SESSION['RE'];
  if( !$_SESSION['USER'] ) http::route2( ['login'] );
}

response::add( 'body', http::$route[0] );

if( $_SESSION['USER'] ){
  setup::$user = setup::get_user( $_SESSION['USER'], OBJECT_TYPE );
  if( $_SESSION['COMPANY'] and setup::$company = setup::get_company_link( $_SESSION['COMPANY'] )){
    if( setup::$company->configuration ?? 0 ){
      setup::$company->configuration = json_decode( setup::$company->configuration );
      setup::$company->configuration->lng = setup::$company->configuration->lng ?? 'hu';
    }else
      setup::$company->configuration = json_decode( '{"lng": "hu"}' );

    if( http::$route[0] == 'modal' ){
      require_once 'modal.php';
      exit();
    }
  }

  if( !$_SESSION['COMPANY'] ) http::$route[0] = 'tocreate';
  elseif( !( setup::$company->configuration->is_generates ?? 0 )) http::$route[0] = 'generates';
  elseif( !setup::get_rights()){
    // csomag választáshoz, mert nincs szerződés és ingyenes csomag sem
    http::$route[0] = 'settings';
    http::$route[1] = 'subscription';     
  }

  if( $_SESSION['RE'] ?? 0 ){
    $re = base64_decode( $_SESSION['RE'] );
    $re = explode( '|', $re );
    switch( $re[0] ){
      case 'foglalas': http::$route[0] = 'foglalas'; break;
    }
  }
  
  response::add( 'timeout', sprintf( '%02d:%02d', floor( ini_get( 'session.gc_maxlifetime' ) / 60 ), ini_get( 'session.gc_maxlifetime' ) % 60 ));
  response::add( 'body', http::$route[0] );
  response::add( 'navtabs', 1 );
  switch( http::$route[0] ){
    case 'tocreate':
    case 'generates':
      if( file_exists( 'cont_'.http::$route[0].'.php' )) require_once 'cont_'.http::$route[0].'.php';
    break;
    case 'logout': response::add( 'body', '/' ); break;
    case 'customerservice': break;
    case 'settings':
      response::add( 'navtabs', 2 );
      if( http::$route[1] ?? 0 ){
        response::add( 'view', 'menu', ['log' => 1] );
        switch( http::$route[1] ){
          case 'subscription':
          case 'parameter':
            response::add( 'body', http::$route[0].'_'.http::$route[1] );
            require_once 'cont_'.http::$route[0].'_'.http::$route[1].'.php';
          break;
          case 'staff': // munkatársak kezelése
          case 'profile':
            response::add( 'body', http::$route[0].'_'.http::$route[1] );
            if( file_exists( $shared.'cont_'.http::$route[0].'_'.http::$route[1].'.php' ) )
              require_once $shared.'cont_'.http::$route[0].'_'.http::$route[1].'.php';
          break;
        }
      }else http::gourl( '/login' );
    break;
    case 'message':
      if( http::$route[1] ?? 0 ){
        response::add( 'view', 'menu', ['log' => 1] );
        switch( http::$route[1] ){
          case 'messages':
          case 'templates':
          case 'subscribers':
            response::add( 'body', http::$route[0].'_'.http::$route[1] );
            if( file_exists( 'cont_'.http::$route[0].'_'.http::$route[1].'.php' ) )
              require_once 'cont_'.http::$route[0].'_'.http::$route[1].'.php';
          break;
        }
      }else http::gourl( '/login' );
    break;
    case 'pricing':
      response::add( 'navtabs', 2 );
      if( http::$route[1] ?? 0 ){
        response::add( 'view', 'menu', ['log' => 1] );
        switch( http::$route[1] ){
          case 'children_rules':
          case 'prices':
          case 'periods':
            response::add( 'body', http::$route[0].'_'.http::$route[1] );
            if( file_exists( 'cont_'.http::$route[0].'_'.http::$route[1].'.php' ))
              require_once 'cont_'.http::$route[0].'_'.http::$route[1].'.php';
          break;
        }
      }else http::gourl( '/login' );
    break;
    case 'website':
      response::add( 'navtabs', 2 );
      if( http::$route[1] ?? 0 ){
        response::add( 'view', 'menu', ['log' => 1] );
        switch( http::$route[1] ){
          case 'documents':
          case 'galleries':
            response::add( 'body', http::$route[0].'_'.http::$route[1] );
            if( file_exists( 'cont_'.http::$route[0].'_'.http::$route[1].'.php' ) )
              require_once 'cont_'.http::$route[0].'_'.http::$route[1].'.php';
          break;
        }
      }else http::gourl( '/login' );
    break;
    case 'reviews':
      if( http::$route[1] ?? 0 ){
        response::add( 'view', 'menu', ['log' => 1] );
        switch( http::$route[1] ){
          case 'imports':
          case 'ratings':
            response::add( 'body', http::$route[0].'_'.http::$route[1] );
            if( file_exists( 'cont_'.http::$route[0].'_'.http::$route[1].'.php' ) )
              require_once 'cont_'.http::$route[0].'_'.http::$route[1].'.php';
          break;
        }
      }else http::gourl( '/login' );
    break;
    case 'accommodationunittypes':
      if( setup::$company->configuration->supplierInfo->accommodation_units_number == 1 )
        if( $accommodation_unit_type = db::get( DATABASENAME.'.accommodation_unit_types', 'company_id='.$_SESSION['COMPANY'] ))
          http::gourl( '/accommodationunits/'.$accommodation_unit_type['id'] );
    case 'accommodationunits':
    case 'coupons':
    case 'popups':
    case 'services':
    case 'foglalasicsatorna':
    case 'seo':
    case 'blogmanager':
    case 'channelmanager':
    case 'automation':
      response::add( 'navtabs', 2 );
    case 'dashboard':
    case 'invoices':
    case 'naptar':
    case 'foglalas':
    case 'penz':
      response::add( 'view', 'menu', ['log' => 1] );
      if( file_exists( 'cont_'.http::$route[0].'.php' ) ) require_once 'cont_'.http::$route[0].'.php';
    break;
    case 'pagemanager':
    case 'bannermanager':
      response::add( 'view', 'menu', ['log' => 1] );
      if( file_exists( 'shared/modul/cont_'.http::$route[0].'.php' ) ) require_once 'shared/modul/cont_'.http::$route[0].'.php';
    break;
    default:
      http::$route[0] = 'dashboard';
      response::add( 'body', http::$route[0] );
      response::add( 'view', 'menu', ['log' => 1] );
      if( file_exists( 'cont_'.http::$route[0].'.php' ) ) require_once 'cont_'.http::$route[0].'.php';
  }
}else
  switch( http::$route[0] ){
    case 'login':
      require_once 'cont_login.php';
      response::add('body', 'login' );
    break;
    case 'forgotpwd':
      response::add('body', 'forgotpwd' );
    break;
    
    case 'bemutato':
    case 'arak':
      response::add('view', 'menu', ['log' => 1]);
      response::add('body', http::$route[0]);
      require_once 'cont_'.http::$route[0].'.php';
    break;
    case 'home':
    case 'prices':
    case 'registration':
    break;
    default: http::gourl( '/' );
  }

header( 'Access-Control-Allow-Origin: *' );
/*
header( 'X-Frame-Options: SAMEORIGIN' );
header( 'X-Content-Type-Options: nosniff' );
header( 'Referrer-Policy: no-referrer' );
header( 'Strict-Transport-Security: max-age=31536000; includeSubDomains; preload' );
header( 'Permissions-Policy: picture-in-picture=(), geolocation=(), camera=' );
header( 'Content-Security-Policy: default-src "self"; script-src "self" "unsafe-inline"; style-src "self" "unsafe-inline"' ); //  https:
header_remove ( 'X-Powered-By' );
*/
require_once 'view_html'.( ( $_SESSION['USER'] )? '_admin' : '' ).'.php';
//$_end = getrusage();