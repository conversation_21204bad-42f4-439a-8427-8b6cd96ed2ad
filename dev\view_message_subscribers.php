  <section class="container">
    <div class="cart">
      <?= response::alert('message',0,0,5) ?>
      <?= response::alert() ?>
      <header>
        <h5>Feliratkozók</h5>
      </header>
      <article>
        <?= table::datatable( 
          'subscribers',
          [
            'name' => ['th' => 'Vezetéknév'],
            'first_name' => ['th' => 'Keresztnév'],
            'email' => ['th' => 'E-mail'],
            'consent_start' => ['th' => 'Feliratkozás']
          ]
        ) ?>
      </article>
    </div>
  </section>
  <script type="module">
    import { datatable } from '/shared/js/spritzer/index.js'

    window.addEventListener('DOMContentLoaded', () => {
      datatable()
    } )
  </script>