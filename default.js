export function $( selector, element = document ){
  return element.querySelector( selector ) || null
}

export function $$( selector, element = document ){
  return element.querySelectorAll( selector ) || null
}

export function getStyle( element, styleProp ){ 
  if( element.currentStyle )
    return element.currentStyle[styleProp]
  else if( window.getComputedStyle )
    return document.defaultView.getComputedStyle( element, null ).getPropertyValue( styleProp )
}

export function getCharCode( event ){
  return event.key !== undefined
    ? event.key.charCodeAt( 0 )
    : event.which || event.charCode || event.keyCode || 0
}

export function preventDoubleSubmits( unlock = null ){
  $$( 'form' ).forEach( form => {
    if( !unlock )
      form.addEventListener( 'submit', event => preventDoubleSubmit( event ) )
    else if( unlock === 'all' || unlock === form.name )
      form.classList.remove( 'is-submitting' )
  } )
}

export function preventDoubleSubmit( event, unlock = null ){
  if( !unlock ){
    const form = event.currentTarget
    if( form.classList.contains( 'is-submitting' ) ) event.preventDefault()
    form.classList.add( 'is-submitting' )
  }else
    event.classList.remove( 'is-submitting' )
}

export function fullScreen( element = null ){
  const
    el = element || this,
    dialog = el.closest( 'dialog' )
  if( dialog.classList.contains( 'max-screen' ) ){
    dialog.classList.remove( 'max-screen' )
    el.style = '--icon:var(${el.dataset.fsicon.split('|')[0]})'
  }else{
    dialog.classList.add( 'max-screen' )
    el.style = '--icon:var(${el.dataset.fsicon.split('|')[1]})'
  }
}

export function getDayOfYear( currentDate = new Date() ){
  return Math.floor( ( currentDate - new Date( currentDate.getFullYear(), 0, 0 ) ) / 1000 / 60 / 60 / 24 )
}

export function getNextDate( currentDate = new Date(), daysToAdd = 1 ){
  currentDate.setDate( currentDate.getDate() + daysToAdd )
  return currentDate
}

export function getDiffDay( biggerDate = new Date(), smallerDate = new Date() ){
  return Math.round( ( new Date( biggerDate ) - new Date( smallerDate ) ) / 1000 / 60 / 60 / 24 )
}

export function getFormatDate( format = 'Y-m-d', currentDate = new Date() ){ 
  let dateSting =
    currentDate.getFullYear() +'-'+
    ( '0'+ ( currentDate.getMonth() + 1 ) ).substr( -2 ) +'-'+
    ( '0' + currentDate.getDate() ).substr( -2 )
  return dateSting
}

/*
  document.querySelector("form").addEventListener("submit", async (event) => {
    event.preventDefault()
    const form = event.currentTarget
    const resource = form.action
    const options = {
      method: form.method,
      body: new FormData(form)
    }
    const r = await fetch(resource, options)
    if( !r.ok ) return
  }

  const resource = new URL(form.action || window.location.href)
  if( options.method === 'get' )
    resource.search = new URLSearchParams( formData )
  else{
    if( form.enctype === 'multipart/form-data' ){
      options.body = formData
    else{
      options.body = JSON.stringify( Object.fromEntries( formData ) )
      options.headers['Content-Type'] = 'application/json'
    }
  }
*/