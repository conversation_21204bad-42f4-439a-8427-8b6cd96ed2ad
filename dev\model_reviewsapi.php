<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>
 * 
 * Google:
 * https://developers.google.com/maps/documentation/javascript/examples/places-placeid-finder
 * Place ID: ChIJqYazAvgVaEcRwwdLL5nO7hE HolMiKi Kft.
 * Api Key: AIzaSyB8xOzehN6TbMq8zBRF6Ncvk0tRS164Gok HolMiKi Kft.
 * Endpoint: https://maps.googleapis.com/maps/api/place/details/json
 *
 * @method `get_reviews();`         Ért<PERSON><PERSON><PERSON><PERSON> lekérése
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2025, Tánczos Róbert
 * 
 * @version 1.0.0
 * @since 1.0.0 2025.03.28
 */

class ReviewsApi/* extends mysql*/{

  private static $googleEndpoint = 'https://maps.googleapis.com/maps/api/place/details/json';
  private static $googleApiKey = 'AIzaSyB8xOzehN6TbMq8zBRF6Ncvk0tRS164Gok';

  public static function get_reviews( $provider, $identifier ){
    switch( $provider ){
      case 'Google':
        $ch = curl_init();
        curl_setopt( $ch, CURLOPT_URL, self::$googleEndpoint.'?place_id='.$identifier.'&key='.self::$googleApiKey.'&fields=name,rating,reviews,user_ratings_total&language=hu' );
        curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
        curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
        $result = curl_exec( $ch );
        $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );
        if( $result === false )
          $result = json_encode( ['error' => ['code' => curl_errno( $ch ), 'message' => curl_error( $ch ), 'details' => '']] );
        elseif( $http_code >= 400 )
          $result = json_encode( ['error' => ['code' => $http_code, 'message' => $result, 'details' => '']] );
        curl_close($ch);
        $data = json_decode( $result , true );
      break;
    }
    return $data;
  }
}