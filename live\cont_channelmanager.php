<?php
if( http::$route[1] ?? 0 ){
  if( http::$route[2] ?? 0 ){
    // import
    $message = ical::import_ical( http::$route[1], http::$route[2] );
    if( strlen( $message ) )
      response::add( 'error', $message );
  }else{
    // export
    ical::export_ical( http::$route[1] );
  }
}
$szinkron = false;
if( $list = db::list( 'accommodation_units' ) )
  foreach( $list as $accommodationunit ){
    $le = $accommodationunit['id'].'|'.$accommodationunit['name'];
    $szinkron[$le] = false;
    if( $list2 = db::list_csatorna() )
      foreach( $list2 as $csatorna ){
        $cs = $csatorna['csatorna_id'].'|'.$csatorna['megnevezes'];
        $szinkron[$le][$cs] = false;
        if( $link = db::get_szinkronnaptar( $accommodationunit['id'], $csatorna['csatorna_id'] ) )
          $szinkron[$le][$cs] = [ 'link' => $link['link'], 'utolso' => $link['utolso'] ?? ''];
      }
  }
response::add( 'view','szinkronlist', $szinkron );